# Polymorphic Task Assignment

## Overview

The polymorphic task assignment system allows different entity types to automatically create appropriate task types when assigned to officers. This provides a unified assignment interface while maintaining task type specificity.

## How It Works

When an assign button is clicked on any entity (application, data breach, complaint, etc.), the system:

1. **Identifies the entity type** from the `itemType` prop
2. **Maps to appropriate task type** using predefined mappings
3. **Creates a new task** with entity-specific title, description, and metadata
4. **Assigns the task** to the selected officer

## Entity Type Mappings

| Entity Type | Task Type | Description |
|-------------|-----------|-------------|
| `application` | `EVALUATION` | Creates evaluation tasks for license applications |
| `data_breach` | `DATA_BREACH` | Creates investigation tasks for data breach reports |
| `complaint` | `COMPLAINT` | Creates review tasks for customer complaints |
| `inspection` | `INSPECTION` | Creates inspection tasks for facility reviews |
| `document_review` | `DOCUMENT_REVIEW` | Creates review tasks for document validation |

## Usage

### Basic Assignment Button

```tsx
import AssignButton from '../common/AssignButton';

// In license management table
<AssignButton
  itemId={application.id}
  itemType="application"
  itemTitle={application.title}
  onAssignSuccess={() => refreshData()}
/>

// In data breach table
<AssignButton
  itemId={breach.id}
  itemType="data_breach"
  itemTitle={breach.title}
  onAssignSuccess={() => refreshData()}
/>
```

### Assignment Modal

The `AssignModal` component handles the actual task creation:

```tsx
import AssignModal from '../common/AssignModal';

<AssignModal
  isOpen={isModalOpen}
  onClose={() => setIsModalOpen(false)}
  itemId={selectedItemId}
  itemType="application"
  itemTitle={selectedItemTitle}
  onAssignSuccess={handleAssignSuccess}
/>
```

## Task Creation Process

### 1. Task Type Determination

```typescript
const getTaskTypeFromItemType = (itemType: string): TaskType => {
  switch (itemType) {
    case 'application':
      return TaskType.EVALUATION;
    case 'data_breach':
      return TaskType.DATA_BREACH;
    // ... other mappings
  }
};
```

### 2. Dynamic Title Generation

```typescript
const getTaskTitle = (itemType: string, itemTitle?: string): string => {
  const baseTitle = itemTitle || 'Untitled';
  switch (itemType) {
    case 'application':
      return `Application Evaluation: ${baseTitle}`;
    case 'data_breach':
      return `Data Breach Investigation: ${baseTitle}`;
    // ... other mappings
  }
};
```

### 3. Task Creation

```typescript
const taskData = {
  task_type: getTaskTypeFromItemType(itemType),
  title: getTaskTitle(itemType, itemTitle),
  description: getTaskDescription(itemType, itemTitle),
  priority: TaskPriority.MEDIUM,
  status: TaskStatus.PENDING,
  entity_type: itemType,
  entity_id: itemId,
  assigned_to: selectedOfficer,
  metadata: {
    comment: comment.trim() || undefined,
    original_item_title: itemTitle,
    assignment_context: 'manual_assignment'
  }
};

await taskService.createTask(taskData);
```

## Benefits

1. **Consistency**: Unified assignment interface across all entity types
2. **Specificity**: Each task type has appropriate titles and descriptions
3. **Traceability**: Tasks maintain references to source entities
4. **Flexibility**: Easy to add new entity types and task mappings
5. **Metadata**: Rich context preserved in task metadata

## Implementation Examples

### License Management Table

```tsx
// In applications table
{applications.map(app => (
  <tr key={app.id}>
    <td>{app.title}</td>
    <td>{app.status}</td>
    <td>
      <AssignButton
        itemId={app.id}
        itemType="application"
        itemTitle={app.title}
        onAssignSuccess={() => loadApplications()}
      />
    </td>
  </tr>
))}
```

### Data Breach Reports

```tsx
// In data breach table
{breaches.map(breach => (
  <tr key={breach.id}>
    <td>{breach.title}</td>
    <td>{breach.severity}</td>
    <td>
      <AssignButton
        itemId={breach.id}
        itemType="data_breach"
        itemTitle={breach.title}
        onAssignSuccess={() => loadBreaches()}
      />
    </td>
  </tr>
))}
```

## Adding New Entity Types

To add a new entity type:

1. **Update interfaces** in `AssignButton.tsx` and `AssignModal.tsx`
2. **Add mapping functions** in `AssignModal.tsx`
3. **Add task type** to `TaskType` enum if needed
4. **Test the integration** with the new entity type

## API Endpoints

### Officer Management
- `GET /users/list/officers` - Get officers for task assignment
- `GET /users?filter[exclude_customers]=true` - Alternative endpoint with filtering
- Supports pagination, search, and filtering
- Returns users with roles like: administrator, evaluator, legal, accountant, etc.
- Excludes users with 'customer' role

### Task Management
- `POST /tasks` - Create new polymorphic tasks
- `GET /tasks` - List all tasks with filtering
- `PATCH /tasks/:id` - Update task details
- `PUT /tasks/:id/assign` - Assign task to officer
- `DELETE /tasks/:id` - Delete task

## Demo

Visit `/tasks` and click the "Assignment Demo" tab to see interactive examples of polymorphic task assignment for all supported entity types.

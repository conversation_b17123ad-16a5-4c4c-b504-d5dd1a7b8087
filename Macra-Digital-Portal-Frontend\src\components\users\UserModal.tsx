'use client';

import { useState, useEffect } from 'react';
import { User, Role, CreateUserDto, UpdateUserDto } from '../../services/userService';
import { userService } from '../../services/userService';
import { departmentService } from '../../services/departmentService';
import TextInput from '../forms/TextInput';
import Select from '../forms/Select';
import RolesDropdown from '../roles/RolesDropdown';
import DepartmentDropdown from '../departments/DepartmentDropdown';
import { Department } from '@/types/department';

interface UserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
  user?: User | null;
  roles: Role[];
  departments: Department[];
}

export default function UserModal({
  isOpen, onClose, onSave, user, roles = [], departments = []
}: UserModalProps) {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    first_name: '',
    last_name: '',
    middle_name: '',
    phone: '',
    department_id: '',
    organization_id: '',
    status: 'active' as 'active' | 'inactive' | 'suspended',
    role_ids: [] as string[],
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isManager, setIsManager] = useState(false);
  const [showOptions, setShowOptions] = useState(false);

  useEffect(() => {
    console.log('Form data', formData);
    if (user) {
      setFormData({
        email: user.email,
        password: '', // Don't populate password for editing
        first_name: user.first_name,
        last_name: user.last_name,
        middle_name: user.middle_name || '',
        phone: user.phone,
        department_id: user.department_id || '',
        organization_id: user.organization_id || '',
        status: user.status,
        role_ids: user.roles?.map(role => role.role_id) || [],
      });
      setIsManager(false); // Reset manager status for existing users
      setShowOptions(true); // Show options immediately for existing users
    } else {
      setFormData({
        email: '',
        password: '',
        first_name: '',
        last_name: '',
        middle_name: '',
        phone: '',
        department_id: '',
        organization_id: '',
        status: 'active',
        role_ids: [],
      });
      setIsManager(false);
      setShowOptions(false); // Hide options initially for new users
    }
    setError(null);
  }, [user, isOpen]);



  const validateForm = (): string | null => {
    // Basic field validation
    if (!formData.email || !formData.email.trim()) {
      return 'Email is required';
    }

    if (!formData.first_name || !formData.first_name.trim()) {
      return 'First name is required';
    }

    if (!formData.last_name || !formData.last_name.trim()) {
      return 'Last name is required';
    }

    if (!formData.phone || !formData.phone.trim()) {
      return 'Phone number is required';
    }

    // Password validation for new users
    if (!user && (!formData.password || !formData.password.trim())) {
      return 'Password is required for new users';
    }

    // Validate that department_id is present
    if (!formData.department_id || !formData.department_id.trim()) {
      return 'Please select a department';
    }

    if (formData.role_ids.length == 0) {
      return 'User cannot be created without roles';
    }

    // Check if any selected role is administrator
    const hasAdminRole = formData.role_ids.some(roleId => {
      const role = roles.find(r => r.role_id === roleId);
      if (!role) return false;
      const roleName = role.name.toLowerCase();
      return roleName === 'administrator' || roleName === 'admin' || roleName === 'super_admin';
    });

    if (hasAdminRole && !formData.email.endsWith('@macra.mw')) {
      return 'Only MACRA staff can be assigned administrator roles';
    }

    // Check if any selected role is evaluator
    const hasEvaluatorRole = formData.role_ids.some(roleId => {
      const role = roles.find(r => r.role_id === roleId);
      if (!role) return false;
      const roleName = role.name.toLowerCase();
      return roleName === 'evaluator';
    });

    if (hasEvaluatorRole && !formData.email.endsWith('@macra.mw')) {
      return 'Only MACRA staff can be assigned evaluator roles';
    }

    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    console.log('Form data submitted', formData);

    // Validate form before submission
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      setLoading(false);
      return;
    }

    try {
      // Check if department already has a manager when assigning manager
      if (isManager && formData.department_id) {
        const selectedDepartment = departments.find(dept => dept.department_id === formData.department_id);
        if (selectedDepartment && selectedDepartment.manager_id && selectedDepartment.manager_id !== user?.user_id) {
          setError('This department already has a manager assigned');
          setLoading(false);
          return;
        }
      }

      if (user) {
        // Update existing user
        const updateData: UpdateUserDto = {
          email: formData.email,
          first_name: formData.first_name,
          last_name: formData.last_name,
          middle_name: formData.middle_name || undefined,
          phone: formData.phone,
          status: formData.status,
          role_ids: formData.role_ids.length > 0 ? formData.role_ids : undefined,
          department_id: formData.department_id || undefined,
          organization_id: formData.organization_id || undefined,
        };

        // Only include password if it's provided
        if (formData.password.trim()) {
          updateData.password = formData.password;
        }

        await userService.updateUser(user.user_id, updateData);

        // If user is being assigned as manager, update department
        if (isManager && formData.department_id) {
          await departmentService.updateDepartment(formData.department_id, {
            manager_id: user.user_id
          });
        }
      } else {
        // Create new user
        const createData: CreateUserDto = {
          email: formData.email,
          password: formData.password,
          first_name: formData.first_name,
          last_name: formData.last_name,
          middle_name: formData.middle_name || undefined,
          phone: formData.phone,
          status: formData.status,
          role_ids: formData.role_ids.length > 0 ? formData.role_ids : undefined,
          department_id: formData.department_id || undefined,
          organization_id: formData.organization_id || undefined,
        };

        const newUser = await userService.createUser(createData);

        // If new user is being assigned as manager, update department
        if (isManager && formData.department_id && newUser.user_id) {
          await departmentService.updateDepartment(formData.department_id, {
            manager_id: newUser.user_id
          });
        }
      }

      onSave();
    } catch (err: unknown) {
      const errorMessage = err instanceof Error && 'response' in err &&
        typeof err.response === 'object' && err.response !== null &&
        'data' in err.response && typeof err.response.data === 'object' && err.response.data !== null &&
        'message' in err.response.data && typeof err.response.data.message === 'string'
        ? err.response.data.message
        : 'Failed to save user';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleRoleToggle = (roleId: string | number) => {
    const roleIdStr = String(roleId);
    setFormData(prev => ({
      ...prev,
      role_ids: prev.role_ids.includes(roleIdStr)
        ? prev.role_ids.filter(id => id !== roleIdStr)
        : [...prev.role_ids, roleIdStr]
    }));
  };

  const handleDepartmentSelect = (id: string) => {
    setFormData((prev) => ({
      ...prev,
      department_id: id,
    }));
  };



  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 dark:bg-gray-900 bg-opacity-75 dark:bg-opacity-75 transition-opacity" onClick={onClose}></div>

        <div className="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-visible shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <form onSubmit={handleSubmit}>
            <div className="bg-white rounded-lg dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="sm:flex sm:items-start">
                <div className="w-full">
                  <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">
                    {user ? 'Edit User' : 'Create New User'}
                  </h3>

                  {error && (
                    <div className="mb-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded">
                      {error}
                    </div>
                  )}

                  <div className="grid grid-cols-1 gap-4">
                    <div className="grid grid-cols-2 gap-4">
                      <TextInput
                        type="email"
                        name="email"
                        label="Email"
                        required
                        value={formData.email}
                        onChange={handleChange}
                        placeholder="Enter email address"
                      />

                      <TextInput
                        type="password"
                        name="password"
                        label={`Password ${!user ? '*' : ''}`}
                        required={!user}
                        value={formData.password}
                        onChange={handleChange}
                        placeholder={user ? 'Leave blank to keep current password' : 'Enter password'}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <TextInput
                        type="text"
                        name="first_name"
                        label="First Name"
                        required
                        value={formData.first_name}
                        onChange={handleChange}
                        placeholder="Enter first name"
                      />

                      <TextInput
                        type="text"
                        name="last_name"
                        label="Last Name"
                        required
                        value={formData.last_name}
                        onChange={handleChange}
                        placeholder="Enter last name"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <TextInput
                        type="text"
                        name="middle_name"
                        label="Middle Name"
                        value={formData.middle_name}
                        onChange={handleChange}
                        placeholder="Enter middle name (optional)"
                      />

                      <TextInput
                        type="tel"
                        name="phone"
                        label="Phone"
                        required
                        value={formData.phone}
                        onChange={handleChange}
                        placeholder="Enter phone number"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <Select
                        name="status"
                        label="Status"
                        value={formData.status}
                        onChange={handleChange}
                      >
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                        <option value="suspended">Suspended</option>
                      </Select>

                      <Select
                        name="isManager"
                        label="Is Manager?"
                        value={isManager ? 'true' : 'false'}
                        onChange={(e) => {
                          setIsManager(e.target.value === 'true');
                          setShowOptions(true);
                        }}
                      >
                        <option value="false">No</option>
                        <option value="true">Yes</option>
                      </Select>

                      {showOptions && (
                        <>
                          <DepartmentDropdown
                            selectedDepartmentId={formData.department_id}
                            onSelect={handleDepartmentSelect}
                            departments={departments}
                          />

                          <RolesDropdown
                            roles={roles}
                            formData={formData}
                            handleRoleToggle={handleRoleToggle}
                          />
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="submit"
                disabled={loading}
                className="main-button inline-flex items-center justify-center w-full sm:w-auto sm:ml-3 disabled:opacity-50"
              >
                {loading ? 'Saving...' : (user ? 'Update User' : 'Create User')}
              </button>
              <button
                type="button"
                onClick={onClose}
                className="secondary-main-button inline-flex items-center justify-center mt-3 w-full sm:mt-0 sm:ml-3 sm:w-auto"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}



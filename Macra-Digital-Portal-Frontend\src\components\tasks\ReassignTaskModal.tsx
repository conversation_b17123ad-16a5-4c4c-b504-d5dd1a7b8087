'use client';

import React, { useState, useEffect } from 'react';
import { Task, taskService } from '../../services/task-assignment';
import { User } from '../../services/userService';
import Select from '../common/Select';

interface ReassignTaskModalProps {
  isOpen: boolean;
  onClose: () => void;
  task: Task | null;
  onReassignSuccess: () => void;
}

const ReassignTaskModal: React.FC<ReassignTaskModalProps> = ({
  isOpen,
  onClose,
  task,
  onReassignSuccess
}) => {
  const [selectedOfficer, setSelectedOfficer] = useState('');
  const [comment, setComment] = useState('');
  const [officers, setOfficers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [reassigning, setReassigning] = useState(false);

  useEffect(() => {
    if (isOpen) {
      fetchOfficers();
      setSelectedOfficer(task?.assigned_to || '');
      setComment('');
    }
  }, [isOpen, task]);

  const fetchOfficers = async () => {
    setLoading(true);
    try {
      const response = await taskService.getOfficers();
      setOfficers(response.data || []);
    } catch (error) {
      console.error('Error fetching officers:', error);
      setOfficers([]);
    } finally {
      setLoading(false);
    }
  };

  const handleReassign = async () => {
    if (!selectedOfficer || !task) {
      return;
    }

    setReassigning(true);
    try {
      await taskService.updateTask(task.task_id, {
        assigned_to: selectedOfficer,
        metadata: {
          ...task.metadata,
          reassignment_comment: comment.trim() || undefined,
          reassignment_date: new Date().toISOString()
        }
      });

      onReassignSuccess();
      onClose();
    } catch (error) {
      console.error('Error reassigning task:', error);
    } finally {
      setReassigning(false);
    }
  };

  const handleCancel = () => {
    setSelectedOfficer('');
    setComment('');
    onClose();
  };

  if (!isOpen || !task) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div className="mt-3">
          {/* Header */}
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Reassign Task
            </h3>
            <button
              onClick={handleCancel}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <i className="ri-close-line text-xl"></i>
            </button>
          </div>

          {/* Task Info */}
          <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {task.title}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Task #{task.task_number}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              Currently assigned to: {task.assignee ? `${task.assignee.first_name} ${task.assignee.last_name}` : 'Unassigned'}
            </div>
          </div>

          {/* Officer Selection */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Reassign to Officer *
            </label>
            {loading ? (
              <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                Loading officers...
              </div>
            ) : (
              <Select
                value={selectedOfficer}
                onChange={setSelectedOfficer}
                options={[
                  { value: '', label: 'Select an officer...' },
                  ...officers.map((officer) => ({
                    value: officer.user_id,
                    label: `${officer.first_name} ${officer.last_name} - ${officer.email}`
                  }))
                ]}
              />
            )}
          </div>

          {/* Comment */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Reassignment Notes (Optional)
            </label>
            <textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
              rows={3}
              placeholder="Add any notes about this reassignment..."
            />
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3">
            <button
              type="button"
              onClick={handleCancel}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              disabled={reassigning}
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleReassign}
              disabled={!selectedOfficer || reassigning}
              className="px-4 py-2 text-sm font-medium text-white bg-primary hover:bg-primary-dark rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {reassigning ? (
                <>
                  <i className="ri-loader-4-line animate-spin mr-2"></i>
                  Reassigning...
                </>
              ) : (
                <>
                  <i className="ri-user-shared-line mr-2"></i>
                  Reassign Task
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReassignTaskModal;

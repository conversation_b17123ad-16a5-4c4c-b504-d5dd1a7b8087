'use client';

import React, { useState, useEffect } from 'react';
import { useToast } from '@/contexts/ToastContext';
import { taskService, TaskType, TaskPriority, TaskStatus } from '@/services/task-assignment';
import { applicationService } from '@/services/applicationService';

interface Officer {
  user_id: string;
  first_name: string;
  last_name: string;
  email: string;
  department?: string;
}

interface AssignModalProps {
  isOpen: boolean;
  onClose: () => void;
  itemId: string | null;
  itemType: 'data_breach' | 'application' | 'complaint' | 'inspection' | 'document_review';
  itemTitle?: string;
  onAssignSuccess?: () => void;
}

const AssignModal: React.FC<AssignModalProps> = ({
  isOpen,
  onClose,
  itemId,
  itemType,
  itemTitle,
  onAssignSuccess
}) => {
  const { showSuccess, showError } = useToast();
  const [officers, setOfficers] = useState<Officer[]>([]);
  const [filteredOfficers, setFilteredOfficers] = useState<Officer[]>([]);
  const [loading, setLoading] = useState(false);
  const [assigning, setAssigning] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedOfficer, setSelectedOfficer] = useState<string>('');
  const [comment, setComment] = useState('');

  useEffect(() => {
    if (isOpen) {
      fetchOfficers();
      setSearchQuery('');
      setSelectedOfficer('');
      setComment('');
    }
  }, [isOpen]);

  useEffect(() => {
    // Filter officers based on search query
    if (searchQuery.trim() === '') {
      setFilteredOfficers(officers);
    } else {
      const filtered = officers.filter(officer =>
        `${officer.first_name} ${officer.last_name}`.toLowerCase().includes(searchQuery.toLowerCase()) ||
        officer.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        officer.department?.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredOfficers(filtered);
    }
  }, [officers, searchQuery]);

  const fetchOfficers = async () => {
    setLoading(true);
    try {
      const response = await taskService.getOfficers();
      const officersData = response.data || [];
      setOfficers(officersData);

      if (officersData.length === 0) {
        console.warn('No officers found for task assignment');
      }
    } catch (error) {
      console.error('Error fetching officers:', error);
      setOfficers([]);
      showError('Failed to load officers. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getTaskTypeFromItemType = (itemType: string): TaskType => {
    switch (itemType) {
      case 'application':
        return TaskType.EVALUATION;
      case 'data_breach':
        return TaskType.DATA_BREACH;
      case 'complaint':
        return TaskType.COMPLAINT;
      case 'inspection':
        return TaskType.INSPECTION;
      case 'document_review':
        return TaskType.DOCUMENT_REVIEW;
      default:
        return TaskType.APPLICATION;
    }
  };

  const getTaskTitle = (itemType: string, itemTitle?: string): string => {
    const baseTitle = itemTitle || 'Untitled';
    switch (itemType) {
      case 'application':
        return `Application Evaluation: ${baseTitle}`;
      case 'data_breach':
        return `Data Breach Investigation: ${baseTitle}`;
      case 'complaint':
        return `Complaint Review: ${baseTitle}`;
      case 'inspection':
        return `Inspection Task: ${baseTitle}`;
      case 'document_review':
        return `Document Review: ${baseTitle}`;
      default:
        return `Task: ${baseTitle}`;
    }
  };

  const getTaskDescription = (itemType: string, itemTitle?: string): string => {
    const baseTitle = itemTitle || 'item';
    switch (itemType) {
      case 'application':
        return `Evaluate and review application ${baseTitle} for compliance and approval.`;
      case 'data_breach':
        return `Investigate and assess data breach report ${baseTitle} for regulatory compliance.`;
      case 'complaint':
        return `Review and resolve complaint ${baseTitle} according to regulatory procedures.`;
      case 'inspection':
        return `Conduct inspection for ${baseTitle} to ensure regulatory compliance.`;
      case 'document_review':
        return `Review and validate document ${baseTitle} for accuracy and compliance.`;
      default:
        return `Process and review ${baseTitle}.`;
    }
  };

  const handleAssign = async () => {
    if (!selectedOfficer || !itemId) {
      showError('Please select an officer');
      return;
    }

    setAssigning(true);
    try {
      // Create a polymorphic task based on item type
      const taskData = {
        task_type: getTaskTypeFromItemType(itemType),
        title: getTaskTitle(itemType, itemTitle),
        description: getTaskDescription(itemType, itemTitle),
        priority: TaskPriority.MEDIUM,
        status: TaskStatus.PENDING,
        entity_type: itemType,
        entity_id: itemId,
        assigned_to: selectedOfficer,
        metadata: {
          comment: comment.trim() || undefined,
          original_item_title: itemTitle,
          assignment_context: 'manual_assignment'
        }
      };

      await taskService.createTask(taskData);

      // If this is an application, update its status to evaluation
      if (itemType === 'application') {
        try {
          await applicationService.updateStatus(itemId, 'evaluation');
        } catch (statusError) {
          console.warn('Task created but failed to update application status:', statusError);
          // Don't fail the entire operation if status update fails
        }
      }

      showSuccess(`Successfully assigned ${itemType.replace('_', ' ')} to officer`);
      onAssignSuccess?.();
      onClose();
    } catch (error) {
      console.error('Error creating assignment task:', error);
      showError('Failed to assign task');
    } finally {
      setAssigning(false);
    }
  };

  const getSelectedOfficerDetails = () => {
    return officers.find(officer => officer.user_id === selectedOfficer);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div className="mt-3">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Create Task for {itemType.replace('_', ' ').toUpperCase()}
            </h3>
            <button
              type="button"
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <i className="ri-close-line text-xl"></i>
            </button>
          </div>

          {/* Task Details */}
          <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2 flex items-center">
              <i className="ri-task-line mr-2"></i>
              Task to be Created:
            </h4>
            <p className="text-sm text-blue-700 dark:text-blue-300 font-medium">
              {getTaskTitle(itemType, itemTitle)}
            </p>
            <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
              {getTaskDescription(itemType, itemTitle)}
            </p>
          </div>

          {/* Search Officers */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Search Officers
            </label>
            <input
              type="text"
              placeholder="Search by name, email, or department..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
            />
          </div>

          {/* Officers List */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Select Officer ({filteredOfficers.length} found)
            </label>
            <div className="max-h-64 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md">
              {loading ? (
                <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                  Loading officers...
                </div>
              ) : filteredOfficers.length === 0 ? (
                <div className="p-6 text-center text-gray-500 dark:text-gray-400">
                  <i className="ri-user-line text-2xl mb-2 block"></i>
                  <p className="font-medium">No officers found</p>
                  <p className="text-sm mt-1">
                    {officers.length === 0
                      ? 'No active officers available for task assignment.'
                      : 'Try adjusting your search criteria.'
                    }
                  </p>
                  {officers.length === 0 && (
                    <button
                      type="button"
                      onClick={fetchOfficers}
                      className="mt-3 text-sm text-primary hover:text-primary-dark underline"
                    >
                      Retry loading officers
                    </button>
                  )}
                </div>
              ) : (
                filteredOfficers.map((officer) => (
                  <div
                    key={officer.user_id}
                    className={`p-3 border-b border-gray-200 dark:border-gray-600 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 ${
                      selectedOfficer === officer.user_id ? 'bg-blue-50 dark:bg-blue-900' : ''
                    }`}
                    onClick={() => setSelectedOfficer(officer.user_id)}
                  >
                    <div className="flex items-center">
                      <input
                        type="radio"
                        name="officer"
                        value={officer.user_id}
                        checked={selectedOfficer === officer.user_id}
                        onChange={() => setSelectedOfficer(officer.user_id)}
                        className="mr-3"
                      />
                      <div className="flex-1">
                        <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {officer.first_name} {officer.last_name}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {officer.email}
                        </div>
                        {officer.department && (
                          <div className="text-xs text-gray-400 dark:text-gray-500">
                            {officer.department}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Comment */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Task Notes (Optional)
            </label>
            <textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder="Add any specific instructions or context for this task..."
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
            />
          </div>

          {/* Selected Officer Summary */}
          {selectedOfficer && (
            <div className="mb-6 p-4 bg-green-50 dark:bg-green-900 rounded-lg">
              <h4 className="text-sm font-medium text-green-900 dark:text-green-100 mb-2">
                Selected Officer:
              </h4>
              <div className="text-sm text-green-700 dark:text-green-200">
                {getSelectedOfficerDetails()?.first_name} {getSelectedOfficerDetails()?.last_name}
                <br />
                {getSelectedOfficerDetails()?.email}
                {getSelectedOfficerDetails()?.department && (
                  <>
                    <br />
                    Department: {getSelectedOfficerDetails()?.department}
                  </>
                )}
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleAssign}
              disabled={!selectedOfficer || assigning}
              className="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {assigning ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Creating Task...
                </>
              ) : (
                <>
                  <i className="ri-task-line mr-2"></i>
                  Create Task
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssignModal;

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  UserPlus,
  Eye,
  Clock,
  AlertTriangle,
  Calendar,
  User,
  ExternalLink
} from 'lucide-react';
import { TaskAssignmentModal } from '@/components/shared/TaskAssignmentModal';
import { useTaskAssignment } from '@/hooks/useTaskAssignment';
import { useTaskNavigation } from '@/hooks/useTaskNavigation';
import { format } from 'date-fns';

interface Task {
  task_id: string;
  task_number: string;
  title: string;
  description?: string;
  status: string;
  priority: string;
  assigned_to?: string;
  assignee?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  due_date?: string;
  created_at: string;
  entity_type?: string;
  entity_id?: string;
}

interface User {
  user_id: string;
  first_name: string;
  last_name: string;
  email: string;
  role?: {
    name: string;
  };
}

interface TasksTableProps {
  tasks: Task[];
  users: User[];
  onRefresh: () => void;
  loading?: boolean;
}

export const TasksTable: React.FC<TasksTableProps> = ({
  tasks,
  users,
  onRefresh,
  loading = false,
}) => {
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [isAssignModalOpen, setIsAssignModalOpen] = useState(false);

  const { assignTask, isAssigning } = useTaskAssignment({
    onSuccess: () => {
      onRefresh();
      setIsAssignModalOpen(false);
      setSelectedTask(null);
    },
  });

  const { navigateToTaskView, openTaskViewInNewTab, isLoading: isNavigating } = useTaskNavigation();

  const handleAssignClick = (task: Task) => {
    setSelectedTask(task);
    setIsAssignModalOpen(true);
  };

  const handleAssignment = async (assignmentData: any) => {
    if (!selectedTask) return;
    await assignTask(selectedTask.task_id, assignmentData);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { variant: 'outline' as const, label: 'Pending' },
      in_progress: { variant: 'default' as const, label: 'In Progress' },
      completed: { variant: 'success' as const, label: 'Completed' },
      cancelled: { variant: 'destructive' as const, label: 'Cancelled' },
      on_hold: { variant: 'secondary' as const, label: 'On Hold' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      low: { variant: 'outline' as const, label: 'Low', color: 'text-green-600' },
      medium: { variant: 'secondary' as const, label: 'Medium', color: 'text-yellow-600' },
      high: { variant: 'warning' as const, label: 'High', color: 'text-orange-600' },
      urgent: { variant: 'destructive' as const, label: 'Urgent', color: 'text-red-600' },
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;
    return (
      <div className="flex items-center gap-1">
        <AlertTriangle className={`h-3 w-3 ${config.color}`} />
        <Badge variant={config.variant}>{config.label}</Badge>
      </div>
    );
  };

  const isOverdue = (dueDate?: string) => {
    if (!dueDate) return false;
    return new Date(dueDate) < new Date();
  };

  const canAssignOrReassign = (task: Task) => {
    return task.status !== 'completed' && task.status !== 'cancelled';
  };

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Task #</TableHead>
              <TableHead>Title</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Priority</TableHead>
              <TableHead>Assigned To</TableHead>
              <TableHead>Due Date</TableHead>
              <TableHead>Created</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {tasks.map((task) => (
              <TableRow key={task.task_id}>
                <TableCell className="font-medium">
                  {task.task_number}
                </TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium">{task.title}</div>
                    {task.description && (
                      <div className="text-sm text-gray-500 truncate max-w-xs">
                        {task.description}
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  {getStatusBadge(task.status)}
                </TableCell>
                <TableCell>
                  {getPriorityBadge(task.priority)}
                </TableCell>
                <TableCell>
                  {task.assignee ? (
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-gray-400" />
                      <div>
                        <div className="font-medium text-sm">
                          {task.assignee.first_name} {task.assignee.last_name}
                        </div>
                        <div className="text-xs text-gray-500">
                          {task.assignee.email}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <span className="text-gray-400 text-sm">Unassigned</span>
                  )}
                </TableCell>
                <TableCell>
                  {task.due_date ? (
                    <div className={`flex items-center gap-1 text-sm ${
                      isOverdue(task.due_date) ? 'text-red-600' : 'text-gray-600'
                    }`}>
                      <Calendar className="h-3 w-3" />
                      {format(new Date(task.due_date), 'MMM dd, yyyy')}
                      {isOverdue(task.due_date) && (
                        <Badge variant="destructive" className="ml-1 text-xs">
                          Overdue
                        </Badge>
                      )}
                    </div>
                  ) : (
                    <span className="text-gray-400 text-sm">No due date</span>
                  )}
                </TableCell>
                <TableCell>
                  <div className="text-sm text-gray-600">
                    {format(new Date(task.created_at), 'MMM dd, yyyy')}
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex items-center justify-end gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => navigateToTaskView(task.task_id)}
                      disabled={isNavigating}
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      {isNavigating ? 'Loading...' : 'View'}
                    </Button>

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => openTaskViewInNewTab(task.task_id)}
                      disabled={isNavigating}
                      title="Open in new tab"
                    >
                      <ExternalLink className="h-4 w-4" />
                    </Button>

                    {canAssignOrReassign(task) && (
                      <Button
                        variant={task.assigned_to ? "secondary" : "default"}
                        size="sm"
                        onClick={() => handleAssignClick(task)}
                        disabled={isAssigning}
                      >
                        <UserPlus className="h-4 w-4 mr-1" />
                        {task.assigned_to ? 'Reassign' : 'Assign'}
                      </Button>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Task Assignment Modal */}
      <TaskAssignmentModal
        isOpen={isAssignModalOpen}
        onClose={() => {
          setIsAssignModalOpen(false);
          setSelectedTask(null);
        }}
        onAssign={handleAssignment}
        task={selectedTask || undefined}
        users={users}
        loading={isAssigning}
      />
    </>
  );
};

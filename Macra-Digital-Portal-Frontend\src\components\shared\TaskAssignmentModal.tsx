import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, User, Clock, AlertTriangle } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface User {
  user_id: string;
  first_name: string;
  last_name: string;
  email: string;
  role?: {
    name: string;
  };
}

interface Task {
  task_id: string;
  title: string;
  description?: string;
  assigned_to?: string;
  assignee?: User;
  priority: string;
  due_date?: string;
  entity_type?: string;
  entity_id?: string;
}

interface TaskAssignmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAssign: (assignmentData: AssignmentData) => Promise<void>;
  task?: Task;
  applicationId?: string; // For creating new tasks from license management
  users: User[];
  loading?: boolean;
  title?: string;
}

interface AssignmentData {
  assignedTo: string;
  comment?: string;
  due_date?: string;
  priority?: string;
  assignment_notes?: string;
}

const PRIORITY_OPTIONS = [
  { value: 'low', label: 'Low', color: 'text-green-600' },
  { value: 'medium', label: 'Medium', color: 'text-yellow-600' },
  { value: 'high', label: 'High', color: 'text-orange-600' },
  { value: 'urgent', label: 'Urgent', color: 'text-red-600' },
];

export const TaskAssignmentModal: React.FC<TaskAssignmentModalProps> = ({
  isOpen,
  onClose,
  onAssign,
  task,
  applicationId,
  users,
  loading = false,
  title,
}) => {
  const [assignedTo, setAssignedTo] = useState('');
  const [comment, setComment] = useState('');
  const [dueDate, setDueDate] = useState<Date | undefined>(undefined);
  const [priority, setPriority] = useState('medium');
  const [assignmentNotes, setAssignmentNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Filter users to exclude customers (only show staff/admin users)
  const staffUsers = users.filter(user => 
    user.role?.name !== 'customer' && 
    user.role?.name !== 'applicant'
  );

  // Reset form when modal opens/closes or task changes
  useEffect(() => {
    if (isOpen) {
      setAssignedTo(task?.assigned_to || '');
      setComment('');
      setDueDate(task?.due_date ? new Date(task.due_date) : undefined);
      setPriority(task?.priority || 'medium');
      setAssignmentNotes('');
    }
  }, [isOpen, task]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!assignedTo || !dueDate) {
      return;
    }

    setIsSubmitting(true);

    try {
      const assignmentData: AssignmentData = {
        assignedTo,
        comment: comment.trim() || undefined,
        due_date: dueDate.toISOString(),
        priority,
        assignment_notes: assignmentNotes.trim() || undefined,
      };

      await onAssign(assignmentData);
      onClose();
    } catch (error) {
      console.error('Assignment failed:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const isReassignment = task && task.assigned_to;
  const modalTitle = title || (isReassignment ? 'Reassign Task' : 'Assign Task');
  const currentAssignee = task?.assignee;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            {modalTitle}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Task Information */}
          {task && (
            <div className="bg-gray-50 p-3 rounded-lg">
              <h4 className="font-medium text-sm text-gray-900 mb-1">Task</h4>
              <p className="text-sm text-gray-600">{task.title}</p>
              {currentAssignee && (
                <p className="text-xs text-gray-500 mt-1">
                  Currently assigned to: {currentAssignee.first_name} {currentAssignee.last_name}
                </p>
              )}
            </div>
          )}

          {/* Assignee Selection */}
          <div className="space-y-2">
            <Label htmlFor="assignee">Assign to Officer *</Label>
            <Select value={assignedTo} onValueChange={setAssignedTo} required>
              <SelectTrigger>
                <SelectValue placeholder="Select an officer" />
              </SelectTrigger>
              <SelectContent>
                {staffUsers.map((user) => (
                  <SelectItem key={user.user_id} value={user.user_id}>
                    <div className="flex items-center justify-between w-full">
                      <div>
                        <span className="font-medium">{user.first_name} {user.last_name}</span>
                        {user.role && (
                          <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full capitalize">
                            {user.role.name}
                          </span>
                        )}
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Due Date - Made more prominent */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Due Date *</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal h-10",
                    !dueDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {dueDate ? format(dueDate, "PPP") : "Select due date (required)"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={dueDate}
                  onSelect={setDueDate}
                  disabled={(date) => date < new Date()}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
            <p className="text-xs text-gray-500">
              Select when this task should be completed
            </p>
          </div>

          {/* Priority Selection */}
          <div className="space-y-2">
            <Label htmlFor="priority">Priority</Label>
            <Select value={priority} onValueChange={setPriority}>
              <SelectTrigger>
                <SelectValue placeholder="Select priority" />
              </SelectTrigger>
              <SelectContent>
                {PRIORITY_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex items-center gap-2">
                      <AlertTriangle className={cn("h-4 w-4", option.color)} />
                      <span className={option.color}>{option.label}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Assignment Comment */}
          <div className="space-y-2">
            <Label htmlFor="comment" className="text-sm font-medium">
              {isReassignment ? 'Reassignment Reason' : 'Assignment Instructions'}
            </Label>
            <Textarea
              id="comment"
              placeholder={
                isReassignment
                  ? "Explain why this task is being reassigned..."
                  : "Provide specific instructions or context for this assignment..."
              }
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              rows={3}
              className="resize-none"
            />
            <p className="text-xs text-gray-500">
              {isReassignment
                ? "Help the new assignee understand the context"
                : "Clear instructions help ensure successful completion"
              }
            </p>
          </div>

          {/* Assignment Notes */}
          <div className="space-y-2">
            <Label htmlFor="assignment_notes" className="text-sm font-medium">Additional Notes</Label>
            <Textarea
              id="assignment_notes"
              placeholder="Any special requirements, deadlines, or important details..."
              value={assignmentNotes}
              onChange={(e) => setAssignmentNotes(e.target.value)}
              rows={2}
              className="resize-none"
            />
          </div>

          <DialogFooter className="flex flex-col gap-2">
            {(!assignedTo || !dueDate) && (
              <p className="text-sm text-red-600 text-center">
                Please select an officer and due date to continue
              </p>
            )}
            <div className="flex gap-2 justify-end">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={!assignedTo || !dueDate || isSubmitting || loading}
                className="min-w-[120px]"
              >
                {isSubmitting ? (
                  <div className="flex items-center gap-2">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                    {isReassignment ? 'Reassigning...' : 'Assigning...'}
                  </div>
                ) : (
                  <>
                    <User className="h-4 w-4 mr-2" />
                    {isReassignment ? 'Reassign Task' : 'Assign Task'}
                  </>
                )}
              </Button>
            </div>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

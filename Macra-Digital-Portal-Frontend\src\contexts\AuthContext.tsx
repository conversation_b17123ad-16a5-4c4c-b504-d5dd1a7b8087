'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { authService, AuthResponse } from '../services/auth.service';
import { User } from '../services/auth.service';
import Cookies from 'js-cookie';
import { startTokenValidationTimer, isTokenExpired } from '../lib/authUtils';

interface AuthContextType {
  user: User | null;
  token: string | null;
  login: (email: string, password: string, rememberMe?: boolean) => Promise<{ requiresTwoFactor?: boolean; requiresRecovery?: boolean; userId?: string; user?: User; token?: string } | void>;
  completeTwoFactorLogin: (token: string, userData: User, rememberMe?: boolean) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => void;
  updateUser: (user: User) => void;
  loading: boolean;
  isAuthenticated: boolean;
}

interface RegisterData {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  middle_name?: string;
  phone: string;
  organization?: string;
}

interface UpdateUserData {
  user_id: string;
  email: string;
  first_name: string;
  last_name: string;
  middle_name?: string;
  phone?: string;
  status?: string;
  profile_image?: string;
  roles: (string | { name?: string; role_name?: string })[];
  isAdmin?: boolean;
}



const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [mounted, setMounted] = useState(false);
  // Set mounted to true after hydration
  useEffect(() => {
    setMounted(true);
  }, []);

  // Start token validation timer when mounted and authenticated
  useEffect(() => {
    if (!mounted || !user || !token) return;

    // Start periodic token validation (check every 5 minutes instead of 1 minute)
    const validationTimer = startTokenValidationTimer(300000);

    return () => {
      clearInterval(validationTimer);
    };
  }, [mounted, user, token]);

  useEffect(() => {
    if (!mounted) return;



    // Check for existing token on mount with validation
    const initAuth = () => {
      
      // Get saved data from cookies
      const savedToken = Cookies.get('auth_token');
      const savedUser = Cookies.get('auth_user');

      

      if (savedToken && savedUser) {
        try {
          const parsedUser = JSON.parse(savedUser);
          // Ensure isAdmin property is set for backward compatibility
          const userWithAdmin = {
            ...parsedUser,
            isAdmin: parsedUser.roles?.includes('administrator') || parsedUser.isAdmin || false,
            isCustomer: parsedUser.roles?.includes('customer')
          };

          // Validate token is not expired
          if (!isTokenExpired(savedToken)) {
            setToken(savedToken);
            setUser(userWithAdmin);
            authService.setAuthToken(savedToken);
          } else {
            Cookies.remove('auth_token');
            Cookies.remove('auth_user');
            authService.clearAuthToken();
          }
        } catch (error) {
          console.error('AuthContext: Failed to parse saved user data:', error);
          Cookies.remove('auth_token');
          Cookies.remove('auth_user');
          authService.clearAuthToken();
        }
      } else {
        console.log('AuthContext: No saved authentication data found');
      }

      setLoading(false);
    };

    // Execute immediately instead of waiting for next render cycle
    initAuth();
  }, [mounted]);

  const login = async (email: string, password: string, rememberMe: boolean = false): Promise<{requiresTwoFactor?:boolean, requiresRecovery?: boolean, userId?: string, user?: User, token?: string } | void> => {
    try {
      const response: AuthResponse = await authService.login({ email, password });

      console.log("=====================================LOGIN RESPONSE======================================")
      console.log('Access Token:', response.access_token ? 'Present' : 'Missing');
      console.log('User Data:', response.user);
      console.log("=================================================================================");      

      // Check for account recovery requirement first
      if (response.requiresRecovery) {
        console.log('AuthContext: Account recovery required');
        return {
          requiresRecovery: true,
          requiresTwoFactor: response.requiresTwoFactor || false,
          userId: response.user.user_id,
          user: response.user,
          token: response.access_token || ''
        };
      }

      // Validate response structure for normal login
      if (!response || !response.user || !response.access_token) {
        throw new Error('Invalid response from authentication service');
      }
      
      // Ensure roles is an array
      const roles = Array.isArray(response.user.roles) ? response.user.roles : [];

      // Add computed isAdmin property for backward compatibility
      const userWithAdmin = {
        ...response.user,
        roles,
        isAdmin: roles.includes('administrator'),
        isCustomer: roles.includes('customer')
      };

      // Check for 2FA requirement first, before setting user/token in context
      if (response.user) {
        // Use backend's requiresTwoFactor decision if available, otherwise fall back to old logic
        const backendRequires2FA = response.requiresTwoFactor !== undefined ? response.requiresTwoFactor : response.user.two_factor_enabled;

        if (backendRequires2FA) {
          if (response.user.two_factor_enabled) {
            // User has 2FA enabled and backend requires verification
            console.log('AuthContext: 2FA verification required by backend');
            return {
              requiresTwoFactor: true,
              userId: response.user.user_id,
              user: userWithAdmin,
              token: response.access_token
            };
          } else {
            // User doesn't have 2FA enabled, they need to set it up
            console.log('AuthContext: 2FA not enabled, requiring 2FA setup');
            return {
              requiresTwoFactor: true,  // Still requires 2FA (setup)
              userId: response.user.user_id,
              user: userWithAdmin,
              token: response.access_token
            };
          }
        } else {
          // Backend says no 2FA required, proceed with normal login
          console.log('AuthContext: No 2FA required by backend, proceeding with login');
          setUser(userWithAdmin);
          setToken(response.access_token);

          // Set cookies with appropriate expiration based on rememberMe
          const cookieExpiration = rememberMe ? 30 : 1; // 30 days if remember me, 1 day otherwise
          Cookies.set('auth_token', response.access_token, { expires: cookieExpiration });
          Cookies.set('auth_user', JSON.stringify(userWithAdmin), { expires: cookieExpiration });
          authService.setAuthToken(response.access_token);

          return {
            requiresTwoFactor: false,
            userId: response.user.user_id,
            user: userWithAdmin,
            token: response.access_token
          };
        }
      }
    } catch (error) {
      console.error('AuthContext: Login failed', error);
      throw error;
    }
  };

  const completeTwoFactorLogin = async (token: string, userData: User, rememberMe: boolean = false): Promise<void> => {
    try {
      // Ensure roles is an array
      const roles = Array.isArray(userData.roles) ? userData.roles : [];

      // Add computed isAdmin property for backward compatibility
      const userWithAdmin = {
        ...userData,
        roles,
        isAdmin: roles.includes('administrator'),
        isCustomer: roles.includes('customer'),
      };

      if (process.env.NODE_ENV === 'development') {
        console.log('AuthContext: 2FA login successful, setting user', userWithAdmin);
      }
      
      setToken(token);
      setUser(userWithAdmin);

      // Set cookies with appropriate expiration based on rememberMe
      const cookieExpiration = rememberMe ? 30 : 1; // 30 days if remember me, 1 day otherwise
      Cookies.set('auth_token', token, { expires: cookieExpiration });
      Cookies.set('auth_user', JSON.stringify(userWithAdmin), { expires: cookieExpiration });
      authService.setAuthToken(token);
    } catch (error) {
      console.error('AuthContext: 2FA login completion failed', error);
      throw error;
    }
  };

  const register = async (userData: RegisterData): Promise<void> => {
    await authService.register(userData);

    if (process.env.NODE_ENV === 'development') {
      console.log('AuthContext: Registration successful - user should login manually');
    }

    // Don't automatically log in the user after registration
    // User should be redirected to login page to manually log in
    // This follows the requirement that after account creation,
    // users should be redirected to login page, not dashboard
  };

  const logout = (): void => {
    if (process.env.NODE_ENV === 'development') {
      console.log('AuthContext: Logging out user');
    }

    // Clear state
    setUser(null);
    setToken(null);

    // Remove from cookies
    Cookies.remove('auth_token');
    Cookies.remove('auth_user');

    // Clear auth service token
    authService.clearAuthToken();

    // Clear any other localStorage items related to auth
    if (mounted && typeof window !== 'undefined') {
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user_preferences');
      // Clear any cached data
      sessionStorage.clear();
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('AuthContext: Logout complete');
    }
  };

  const updateUser = (updatedUser: UpdateUserData): void => {
    if (process.env.NODE_ENV === 'development') {
      console.log('AuthContext: Updating user', updatedUser);
    }

    // Convert roles to string array if they're objects
    let roles: string[] = [];
    if (updatedUser.roles) {
      roles = updatedUser.roles.map((role: string | { name?: string; role_name?: string }) =>
        typeof role === 'string' ? role : role.name || role.role_name || 'unknown'
      );
    }

    // Ensure isAdmin property is set for backward compatibility
    const userWithAdmin = {
      user_id: updatedUser.user_id,
      email: updatedUser.email,
      first_name: updatedUser.first_name,
      last_name: updatedUser.last_name,
      middle_name: updatedUser.middle_name,
      phone: updatedUser.phone,
      status: updatedUser.status,
      profile_image: updatedUser.profile_image, // Include profile image
      roles: roles,
      isAdmin: roles.includes('administrator') || updatedUser.isAdmin || false,
      isCustomer: roles.includes('customer')
    };

    if (process.env.NODE_ENV === 'development') {
      console.log('AuthContext: Setting updated user', userWithAdmin);
    }
    setUser(userWithAdmin);
    // Update cookies with new user data
    Cookies.set('auth_user', JSON.stringify(userWithAdmin), { expires: 1 });
  };

  const value: AuthContextType = {
    user,
    token,
    login,
    completeTwoFactorLogin,
    register,
    logout,
    updateUser,
    loading: loading || !mounted,
    isAuthenticated: mounted && !!user && !!token,
    
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

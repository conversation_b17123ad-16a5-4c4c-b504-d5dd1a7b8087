import { useState } from 'react';
import toast from 'react-hot-toast/headless';

interface AssignmentData {
  assignedTo: string;
  comment?: string;
  due_date?: string;
  priority?: string;
  assignment_notes?: string;
}

interface UseTaskAssignmentProps {
  onSuccess?: () => void;
  onError?: (error: any) => void;
}

export const useTaskAssignment = ({ onSuccess, onError }: UseTaskAssignmentProps = {}) => {
  const [isAssigning, setIsAssigning] = useState(false);

  /**
   * Assign or reassign an existing task
   */
  const assignTask = async (taskId: string, assignmentData: AssignmentData) => {
    setIsAssigning(true);
    
    try {
      const response = await fetch(`/api/tasks/${taskId}/assign-or-reassign`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(assignmentData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to assign task');
      }

      const result = await response.json();
      
      toast.success(
        result.assigned_to === assignmentData.assignedTo 
          ? 'Task reassigned successfully' 
          : 'Task assigned successfully'
      );
      
      onSuccess?.();
      return result;
    } catch (error: any) {
      console.error('Task assignment failed:', error);
      toast.error(error.message || 'Failed to assign task');
      onError?.(error);
      throw error;
    } finally {
      setIsAssigning(false);
    }
  };

  /**
   * Create a new task for an application and assign it
   */
  const createAndAssignApplicationTask = async (
    applicationId: string, 
    assignmentData: AssignmentData & {
      title?: string;
      description?: string;
      task_type?: string;
    }
  ) => {
    setIsAssigning(true);
    
    try {
      // First create the task
      const createTaskResponse = await fetch('/api/tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: assignmentData.title || `Review Application`,
          description: assignmentData.description || `Application review task`,
          task_type: assignmentData.task_type || 'application',
          entity_type: 'application',
          entity_id: applicationId,
          priority: assignmentData.priority || 'medium',
          due_date: assignmentData.due_date,
        }),
      });

      if (!createTaskResponse.ok) {
        const errorData = await createTaskResponse.json();
        throw new Error(errorData.message || 'Failed to create task');
      }

      const newTask = await createTaskResponse.json();

      // Then assign the task
      const assignResponse = await fetch(`/api/tasks/${newTask.task_id}/assign-or-reassign`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          assignedTo: assignmentData.assignedTo,
          comment: assignmentData.comment,
          assignment_notes: assignmentData.assignment_notes,
          priority: assignmentData.priority,
          due_date: assignmentData.due_date,
        }),
      });

      if (!assignResponse.ok) {
        const errorData = await assignResponse.json();
        throw new Error(errorData.message || 'Failed to assign task');
      }

      const assignedTask = await assignResponse.json();
      
      toast.success('Task created and assigned successfully');
      onSuccess?.();
      return assignedTask;
    } catch (error: any) {
      console.error('Task creation and assignment failed:', error);
      toast.error(error.message || 'Failed to create and assign task');
      onError?.(error);
      throw error;
    } finally {
      setIsAssigning(false);
    }
  };

  /**
   * Update application status to 'evaluation' when task is assigned
   */
  const updateApplicationStatus = async (applicationId: string, status: string = 'evaluation') => {
    try {
      const response = await fetch(`/api/applications/${applicationId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      });

      if (!response.ok) {
        console.warn('Failed to update application status, but task assignment succeeded');
      }
    } catch (error) {
      console.warn('Failed to update application status:', error);
    }
  };

  /**
   * Assign task from license management table
   * This creates a task if none exists and updates application status
   */
  const assignFromLicenseManagement = async (
    applicationId: string,
    applicationNumber: string,
    assignmentData: AssignmentData
  ) => {
    setIsAssigning(true);
    
    try {
      // Create and assign the task
      const task = await createAndAssignApplicationTask(applicationId, {
        ...assignmentData,
        title: `Review Application - ${applicationNumber}`,
        description: `Application ${applicationNumber} assigned for review and evaluation.`,
        task_type: 'evaluation',
      });

      // Update application status to 'evaluation'
      await updateApplicationStatus(applicationId, 'evaluation');

      toast.success(`Application ${applicationNumber} assigned for evaluation`);
      onSuccess?.();
      return task;
    } catch (error: any) {
      console.error('License management assignment failed:', error);
      toast.error(error.message || 'Failed to assign application');
      onError?.(error);
      throw error;
    } finally {
      setIsAssigning(false);
    }
  };

  return {
    assignTask,
    createAndAssignApplicationTask,
    assignFromLicenseManagement,
    isAssigning,
  };
};

{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/task-assignment.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\nimport { PaginateQuery } from './licenseTypeService';\r\n\r\n// Task enums matching backend\r\nexport enum TaskType {\r\n  APPLICATION = 'application',\r\n  COMPLAINT = 'complaint',\r\n  DATA_BREACH = 'data_breach',\r\n  EVALUATION = 'evaluation',\r\n  INSPECTION = 'inspection',\r\n  DOCUMENT_REVIEW = 'document_review',\r\n  COMPLIANCE_CHECK = 'compliance_check',\r\n  FOLLOW_UP = 'follow_up',\r\n}\r\n\r\nexport enum TaskStatus {\r\n  PENDING = 'pending',\r\n  IN_PROGRESS = 'in_progress',\r\n  COMPLETED = 'completed',\r\n  CANCELLED = 'cancelled',\r\n  ON_HOLD = 'on_hold',\r\n}\r\n\r\nexport enum TaskPriority {\r\n  LOW = 'low',\r\n  MEDIUM = 'medium',\r\n  HIGH = 'high',\r\n  URGENT = 'urgent',\r\n}\r\n\r\n// User interface for task assignments\r\nexport interface TaskUser {\r\n  user_id: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  email: string;\r\n}\r\n\r\n// Main Task interface matching backend entity\r\nexport interface Task {\r\n  task_id: string;\r\n  task_number: string;\r\n  title: string;\r\n  description: string;\r\n  task_type: TaskType;\r\n  status: TaskStatus;\r\n  priority: TaskPriority;\r\n  entity_type?: string;\r\n  entity_id?: string;\r\n  assigned_to?: string;\r\n  assigned_by: string;\r\n  assigned_at?: string;\r\n  due_date?: string;\r\n  completed_at?: string;\r\n  review?: string;\r\n  review_notes?: string;\r\n  completion_notes?: string;\r\n  created_at: string;\r\n  created_by: string;\r\n  updated_at: string;\r\n  updated_by?: string;\r\n  deleted_at?: string;\r\n  assignee?: TaskUser;\r\n  assigner: TaskUser;\r\n  creator: TaskUser;\r\n  updater?: TaskUser;\r\n}\r\n\r\n// Legacy interface for backward compatibility\r\nexport interface GenericTask extends Task {}\r\n\r\n// Legacy interface for backward compatibility\r\nexport interface TaskAssignmentApplication {\r\n  application_id: string;\r\n  application_number: string;\r\n  status: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  applicant: {\r\n    applicant_id: string;\r\n    company_name: string;\r\n    first_name: string;\r\n    last_name: string;\r\n  };\r\n  license_category: {\r\n    license_category_id: string;\r\n    name: string;\r\n    license_type: {\r\n      license_type_id: string;\r\n      name: string;\r\n    };\r\n  };\r\n  assignee?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n  assigned_at?: string;\r\n}\r\n\r\nexport interface TaskAssignmentOfficer {\r\n  user_id: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  email: string;\r\n  department?: string;\r\n}\r\n\r\n// DTOs for task operations\r\nexport interface CreateTaskDto {\r\n  task_type: TaskType;\r\n  title: string;\r\n  description: string;\r\n  priority?: TaskPriority;\r\n  status?: TaskStatus;\r\n  entity_type?: string;\r\n  entity_id?: string;\r\n  due_date?: string;\r\n  assigned_to?: string;\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\nexport interface UpdateTaskDto {\r\n  title?: string;\r\n  description?: string;\r\n  priority?: TaskPriority;\r\n  status?: TaskStatus;\r\n  entity_type?: string;\r\n  entity_id?: string;\r\n  due_date?: string;\r\n  review?: string;\r\n  review_notes?: string;\r\n  completion_notes?: string;\r\n}\r\n\r\nexport interface AssignTaskDto {\r\n  assignedTo: string;\r\n  comment?: string;\r\n}\r\n\r\nexport interface TaskFilters {\r\n  task_type?: string;\r\n  status?: string;\r\n  priority?: string;\r\n  assigned_to?: string;\r\n  created_by?: string;\r\n  assignment_status?: string;\r\n}\r\n\r\nexport interface TaskStats {\r\n  total: number;\r\n  pending: number;\r\n  in_progress: number;\r\n  completed: number;\r\n  cancelled: number;\r\n  on_hold: number;\r\n  unassigned: number;\r\n  assigned: number;\r\n  overdue: number;\r\n}\r\n\r\n// Legacy interfaces for backward compatibility\r\nexport interface AssignApplicationRequest {\r\n  assignedTo: string;\r\n}\r\n\r\nexport interface PaginatedResponse<T> {\r\n  data: T[];\r\n  meta: {\r\n    itemCount: number;\r\n    totalItems: number;\r\n    itemsPerPage: number;\r\n    totalPages: number;\r\n    currentPage: number;\r\n  };\r\n  links: {\r\n    first: string;\r\n    previous: string;\r\n    next: string;\r\n    last: string;\r\n  };\r\n}\r\n\r\n// Import types from userService for compatibility\r\nexport type { PaginateQuery } from '../services/userService';\r\n\r\nexport const taskService = {\r\n  // Main task CRUD operations\r\n  getTasks: async (params?: PaginateQuery & TaskFilters): Promise<PaginatedResponse<Task>> => {\r\n    // Handle assignment_status filter by using different endpoints or filters\r\n    if (params?.assignment_status === 'unassigned') {\r\n      // Use the unassigned tasks endpoint\r\n      return taskService.getUnassignedTasks(params);\r\n    } else if (params?.assignment_status === 'assigned') {\r\n      // Use the assigned tasks endpoint instead of recursive call\r\n      return taskService.getAssignedTasks(params);\r\n    }\r\n\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n    if (params?.task_type) searchParams.append('task_type', params.task_type);\r\n    if (params?.status) searchParams.append('status', params.status);\r\n    if (params?.priority) searchParams.append('priority', params.priority);\r\n    if (params?.assigned_to) searchParams.append('assigned_to', params.assigned_to);\r\n    if (params?.created_by) searchParams.append('created_by', params.created_by);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/tasks${queryString ? `?${queryString}` : ''}`;\r\n\r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  getUnassignedTasks: async (params?: PaginateQuery & TaskFilters): Promise<PaginatedResponse<Task>> => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n    if (params?.task_type) searchParams.append('task_type', params.task_type);\r\n    if (params?.status) searchParams.append('status', params.status);\r\n    if (params?.priority) searchParams.append('priority', params.priority);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/tasks/unassigned${queryString ? `?${queryString}` : ''}`;\r\n\r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  getAssignedTasks: async (params?: PaginateQuery & TaskFilters): Promise<PaginatedResponse<Task>> => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n    if (params?.task_type) searchParams.append('task_type', params.task_type);\r\n    if (params?.status) searchParams.append('status', params.status);\r\n    if (params?.priority) searchParams.append('priority', params.priority);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/tasks/assigned${queryString ? `?${queryString}` : ''}`;\r\n\r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  getMyTasks: async (params?: PaginateQuery & TaskFilters): Promise<PaginatedResponse<Task>> => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n    if (params?.task_type) searchParams.append('task_type', params.task_type);\r\n    if (params?.status) searchParams.append('status', params.status);\r\n    if (params?.priority) searchParams.append('priority', params.priority);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/tasks/assigned/me${queryString ? `?${queryString}` : ''}`;\r\n\r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  getTaskStats: async (): Promise<TaskStats> => {\r\n    const response = await apiClient.get('/tasks/stats');\r\n    return response.data;\r\n  },\r\n\r\n  getTask: async (taskId: string): Promise<Task> => {\r\n    const response = await apiClient.get(`/tasks/${taskId}`);\r\n    return response.data;\r\n  },\r\n\r\n  createTask: async (taskData: CreateTaskDto): Promise<Task> => {\r\n    const response = await apiClient.post('/tasks', taskData);\r\n    return response.data;\r\n  },\r\n\r\n  updateTask: async (taskId: string, taskData: UpdateTaskDto): Promise<Task> => {\r\n    const response = await apiClient.patch(`/tasks/${taskId}`, taskData);\r\n    return response.data;\r\n  },\r\n\r\n  assignTask: async (taskId: string, assignData: AssignTaskDto): Promise<Task> => {\r\n    const response = await apiClient.put(`/tasks/${taskId}/assign`, assignData);\r\n    return response.data;\r\n  },\r\n\r\n  reassignTask: async (taskId: string, assignData: AssignTaskDto): Promise<Task> => {\r\n    const response = await apiClient.put(`/tasks/${taskId}/reassign`, assignData);\r\n    return response.data;\r\n  },\r\n\r\n  deleteTask: async (taskId: string): Promise<void> => {\r\n    await apiClient.delete(`/tasks/${taskId}`);\r\n  },\r\n\r\n  // Get users for assignment (officers only, exclude customers)\r\n  getUsers: async (): Promise<PaginatedResponse<TaskUser>> => {\r\n    try {\r\n      const response = await apiClient.get('/users', {\r\n        params: {\r\n          limit: 100,\r\n          filter: {\r\n            exclude_customers: true\r\n          }\r\n        }\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error fetching users:', error);\r\n      return {\r\n        data: [],\r\n        meta: {\r\n          itemCount: 0,\r\n          totalItems: 0,\r\n          itemsPerPage: 10,\r\n          totalPages: 0,\r\n          currentPage: 1,\r\n        },\r\n        links: {\r\n          first: '',\r\n          previous: '',\r\n          next: '',\r\n          last: '',\r\n        },\r\n      };\r\n    }\r\n  },\r\n\r\n  // Get officers specifically (non-customer users)\r\n  getOfficers: async (): Promise<PaginatedResponse<any>> => {\r\n    try {\r\n      const response = await apiClient.get('/users/list/officers');\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error fetching officers:', error);\r\n      // Fallback to regular users endpoint with filtering\r\n      try {\r\n        const fallbackResponse = await apiClient.get('/users', {\r\n          params: {\r\n            limit: 100,\r\n            filter: {\r\n              exclude_customers: true\r\n            }\r\n          }\r\n        });\r\n        return processApiResponse(fallbackResponse);\r\n      } catch (fallbackError) {\r\n        console.error('Error fetching users as fallback:', fallbackError);\r\n        return {\r\n          data: [],\r\n          meta: {\r\n            itemCount: 0,\r\n            totalItems: 0,\r\n            itemsPerPage: 10,\r\n            totalPages: 0,\r\n            currentPage: 1,\r\n          },\r\n          links: {\r\n            first: '',\r\n            previous: '',\r\n            next: '',\r\n            last: '',\r\n          },\r\n        };\r\n      }\r\n    }\r\n  },\r\n};\r\n\r\n// Legacy service for backward compatibility\r\nexport const taskAssignmentService = {\r\n  // Generic task management methods\r\n  getUnassignedTasks: taskService.getUnassignedTasks,\r\n  getAssignedTasks: taskService.getAssignedTasks,\r\n  assignTask: taskService.assignTask,\r\n  getTaskById: taskService.getTask,\r\n\r\n  // Legacy application-specific methods (for backward compatibility)\r\n  getUnassignedApplications: async (params?: { page?: number; limit?: number; search?: string }) => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/applications/unassigned${queryString ? `?${queryString}` : ''}`;\r\n    \r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  // Get all applications (including assigned)\r\n  getAllApplications: async (params?: { page?: number; limit?: number; search?: string }) => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/applications${queryString ? `?${queryString}` : ''}`;\r\n    \r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  // Get applications assigned to current user\r\n  getMyAssignedApplications: async (params?: { page?: number; limit?: number; search?: string }) => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/applications/assigned/me${queryString ? `?${queryString}` : ''}`;\r\n    \r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  // Get officers for assignment\r\n  getOfficers: async () => {\r\n    try {\r\n      const response = await apiClient.get('/users');\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error fetching officers:', error);\r\n      return { data: [] };\r\n    }\r\n  },\r\n\r\n  // Assign application to officer\r\n  assignApplication: async (applicationId: string, assignData: AssignApplicationRequest) => {\r\n    const response = await apiClient.put(`/applications/${applicationId}/assign`, assignData);\r\n    return response.data;\r\n  },\r\n\r\n  // Get application details\r\n  getApplication: async (applicationId: string) => {\r\n    const response = await apiClient.get(`/applications/${applicationId}`);\r\n    return response.data;\r\n  },\r\n};"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAIO,IAAA,AAAK,kCAAA;;;;;;;;;WAAA;;AAWL,IAAA,AAAK,oCAAA;;;;;;WAAA;;AAQL,IAAA,AAAK,sCAAA;;;;;WAAA;;AAoKL,MAAM,cAAc;IACzB,4BAA4B;IAC5B,UAAU,OAAO;QACf,0EAA0E;QAC1E,IAAI,QAAQ,sBAAsB,cAAc;YAC9C,oCAAoC;YACpC,OAAO,YAAY,kBAAkB,CAAC;QACxC,OAAO,IAAI,QAAQ,sBAAsB,YAAY;YACnD,4DAA4D;YAC5D,OAAO,YAAY,gBAAgB,CAAC;QACtC;QAEA,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAC/D,IAAI,QAAQ,WAAW,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QACxE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAC/D,IAAI,QAAQ,UAAU,aAAa,MAAM,CAAC,YAAY,OAAO,QAAQ;QACrE,IAAI,QAAQ,aAAa,aAAa,MAAM,CAAC,eAAe,OAAO,WAAW;QAC9E,IAAI,QAAQ,YAAY,aAAa,MAAM,CAAC,cAAc,OAAO,UAAU;QAE3E,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAE3D,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,oBAAoB,OAAO;QACzB,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAC/D,IAAI,QAAQ,WAAW,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QACxE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAC/D,IAAI,QAAQ,UAAU,aAAa,MAAM,CAAC,YAAY,OAAO,QAAQ;QAErE,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAEtE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB,OAAO;QACvB,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAC/D,IAAI,QAAQ,WAAW,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QACxE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAC/D,IAAI,QAAQ,UAAU,aAAa,MAAM,CAAC,YAAY,OAAO,QAAQ;QAErE,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAEpE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY,OAAO;QACjB,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAC/D,IAAI,QAAQ,WAAW,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QACxE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAC/D,IAAI,QAAQ,UAAU,aAAa,MAAM,CAAC,YAAY,OAAO,QAAQ;QAErE,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAEvE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;QACZ,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACvD,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,UAAU;QAChD,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QAC3D,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,OAAO,CAAC,EAAE;QAChE,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc,OAAO,QAAgB;QACnC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,SAAS,CAAC,EAAE;QAClE,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY,OAAO;QACjB,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ;IAC3C;IAEA,8DAA8D;IAC9D,UAAU;QACR,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,UAAU;gBAC7C,QAAQ;oBACN,OAAO;oBACP,QAAQ;wBACN,mBAAmB;oBACrB;gBACF;YACF;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;gBACL,MAAM,EAAE;gBACR,MAAM;oBACJ,WAAW;oBACX,YAAY;oBACZ,cAAc;oBACd,YAAY;oBACZ,aAAa;gBACf;gBACA,OAAO;oBACL,OAAO;oBACP,UAAU;oBACV,MAAM;oBACN,MAAM;gBACR;YACF;QACF;IACF;IAEA,iDAAiD;IACjD,aAAa;QACX,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,oDAAoD;YACpD,IAAI;gBACF,MAAM,mBAAmB,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,UAAU;oBACrD,QAAQ;wBACN,OAAO;wBACP,QAAQ;4BACN,mBAAmB;wBACrB;oBACF;gBACF;gBACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAC5B,EAAE,OAAO,eAAe;gBACtB,QAAQ,KAAK,CAAC,qCAAqC;gBACnD,OAAO;oBACL,MAAM,EAAE;oBACR,MAAM;wBACJ,WAAW;wBACX,YAAY;wBACZ,cAAc;wBACd,YAAY;wBACZ,aAAa;oBACf;oBACA,OAAO;wBACL,OAAO;wBACP,UAAU;wBACV,MAAM;wBACN,MAAM;oBACR;gBACF;YACF;QACF;IACF;AACF;AAGO,MAAM,wBAAwB;IACnC,kCAAkC;IAClC,oBAAoB,YAAY,kBAAkB;IAClD,kBAAkB,YAAY,gBAAgB;IAC9C,YAAY,YAAY,UAAU;IAClC,aAAa,YAAY,OAAO;IAEhC,mEAAmE;IACnE,2BAA2B,OAAO;QAChC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,wBAAwB,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAE7E,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,4CAA4C;IAC5C,oBAAoB,OAAO;QACzB,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAElE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,4CAA4C;IAC5C,2BAA2B,OAAO;QAChC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,yBAAyB,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAE9E,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,8BAA8B;IAC9B,aAAa;QACX,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;gBAAE,MAAM,EAAE;YAAC;QACpB;IACF;IAEA,gCAAgC;IAChC,mBAAmB,OAAO,eAAuB;QAC/C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,cAAc,OAAO,CAAC,EAAE;QAC9E,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe;QACrE,OAAO,SAAS,IAAI;IACtB;AACF", "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/Pagination.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\n// Import the pagination response type from the existing structure\ninterface PaginationMeta {\n  itemsPerPage: number;\n  totalItems: number;\n  currentPage: number;\n  totalPages: number;\n  sortBy: [string, string][];\n  searchBy: string[];\n  search: string;\n  filter?: Record<string, string | string[]>;\n}\n\ninterface PaginationProps {\n  meta: PaginationMeta;\n  onPageChange: (page: number) => void;\n  onPageSizeChange?: (pageSize: number) => void;\n  showFirstLast?: boolean;\n  showPageSizeSelector?: boolean;\n  showInfo?: boolean;\n  maxVisiblePages?: number;\n  pageSizeOptions?: number[];\n  className?: string;\n}\n\nconst Pagination: React.FC<PaginationProps> = ({\n  meta,\n  onPageChange,\n  onPageSizeChange,\n  showFirstLast = true,\n  showPageSizeSelector = true,\n  showInfo = true,\n  maxVisiblePages = 7,\n  pageSizeOptions = [10, 25, 50, 100],\n  className = ''\n}) => {\n  const { currentPage, totalPages, totalItems, itemsPerPage } = meta;\n\n  // Don't render if there's only one page or no pages and no additional features\n  if (totalPages <= 1 && !showPageSizeSelector && !showInfo) {\n    return null;\n  }\n\n  // Calculate current items range\n  const startItem = totalItems > 0 ? (currentPage - 1) * itemsPerPage + 1 : 0;\n  const endItem = Math.min(currentPage * itemsPerPage, totalItems);\n\n  // Calculate which pages to show\n  const getVisiblePages = (): (number | string)[] => {\n    const pages: (number | string)[] = [];\n    \n    // If total pages is less than or equal to maxVisiblePages, show all\n    if (totalPages <= maxVisiblePages) {\n      for (let i = 1; i <= totalPages; i++) {\n        pages.push(i);\n      }\n      return pages;\n    }\n\n    // Always show first page\n    pages.push(1);\n\n    // Calculate start and end of the visible range around current page\n    const sidePages = Math.floor((maxVisiblePages - 3) / 2); // -3 for first, last, and current\n    let startPage = Math.max(2, currentPage - sidePages);\n    let endPage = Math.min(totalPages - 1, currentPage + sidePages);\n\n    // Adjust if we're near the beginning\n    if (currentPage <= sidePages + 2) {\n      startPage = 2;\n      endPage = Math.min(totalPages - 1, maxVisiblePages - 1);\n    }\n\n    // Adjust if we're near the end\n    if (currentPage >= totalPages - sidePages - 1) {\n      startPage = Math.max(2, totalPages - maxVisiblePages + 2);\n      endPage = totalPages - 1;\n    }\n\n    // Add ellipsis after first page if needed\n    if (startPage > 2) {\n      pages.push('...');\n    }\n\n    // Add pages in the visible range\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n\n    // Add ellipsis before last page if needed\n    if (endPage < totalPages - 1) {\n      pages.push('...');\n    }\n\n    // Always show last page (if it's not already included)\n    if (totalPages > 1) {\n      pages.push(totalPages);\n    }\n\n    return pages;\n  };\n\n  const visiblePages = getVisiblePages();\n\n  const handlePageClick = (page: number | string) => {\n    if (typeof page === 'number' && page !== currentPage) {\n      onPageChange(page);\n    }\n  };\n\n  const handlePageSizeChange = (event: React.ChangeEvent<HTMLSelectElement>) => {\n    const newPageSize = parseInt(event.target.value);\n    if (onPageSizeChange) {\n      onPageSizeChange(newPageSize);\n    }\n  };\n\n  const handlePrevious = () => {\n    if (currentPage > 1) {\n      onPageChange(currentPage - 1);\n    }\n  };\n\n  const handleNext = () => {\n    if (currentPage < totalPages) {\n      onPageChange(currentPage + 1);\n    }\n  };\n\n  const handleFirst = () => {\n    if (currentPage !== 1) {\n      onPageChange(1);\n    }\n  };\n\n  const handleLast = () => {\n    if (currentPage !== totalPages) {\n      onPageChange(totalPages);\n    }\n  };\n\n  return (\n    <div className={`flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0 px-6 py-3 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 ${className}`}>\n      {/* Left side - Info and page size selector */}\n      <div className=\"flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4\">\n        {/* Items info */}\n        {showInfo && totalItems > 0 && (\n          <div className=\"text-sm text-gray-700 dark:text-gray-300\">\n            Showing <span className=\"font-medium\">{startItem}</span> to{' '}\n            <span className=\"font-medium\">{endItem}</span> of{' '}\n            <span className=\"font-medium\">{totalItems}</span> results\n          </div>\n        )}\n\n        {/* Page size selector */}\n        {showPageSizeSelector && onPageSizeChange && (\n          <div className=\"flex items-center space-x-2\">\n            <select\n              value={itemsPerPage}\n              onChange={handlePageSizeChange}\n              className=\"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent\"\n            >\n              {pageSizeOptions.map((size) => (\n                <option key={size} value={size}>\n                  {size} per page\n                </option>\n              ))}\n            </select>\n          </div>\n        )}\n      </div>\n\n      {/* Right side - Pagination controls */}\n      {totalPages > 1 && (\n        <nav className=\"flex items-center space-x-1\" aria-label=\"Pagination\">\n          {/* First page button */}\n          {showFirstLast && currentPage > 1 && (\n            <button\n              onClick={handleFirst}\n              className=\"inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300\"\n              aria-label=\"Go to first page\"\n            >\n              <i className=\"ri-skip-back-line\"></i>\n            </button>\n          )}\n\n          {/* Previous button */}\n          <button\n            onClick={handlePrevious}\n            disabled={currentPage === 1}\n            className={`inline-flex items-center px-2 py-2 text-sm font-medium border ${\n              currentPage === 1\n                ? 'text-gray-300 bg-gray-100 border-gray-300 cursor-not-allowed dark:bg-gray-700 dark:border-gray-600 dark:text-gray-500'\n                : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300'\n            } ${showFirstLast && currentPage > 1 ? '' : 'rounded-l-md'}`}\n            aria-label=\"Go to previous page\"\n          >\n            <i className=\"ri-arrow-left-s-line\"></i>\n          </button>\n\n          {/* Page numbers */}\n          {visiblePages.map((page, index) => (\n            <React.Fragment key={index}>\n              {page === '...' ? (\n                <span className=\"inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400\">\n                  ...\n                </span>\n              ) : (\n                <button\n                  onClick={() => handlePageClick(page)}\n                  className={`inline-flex items-center px-4 py-2 text-sm font-medium border ${\n                    page === currentPage\n                      ? 'text-white bg-red-600 border-red-600 hover:bg-red-700'\n                      : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300'\n                  }`}\n                  aria-label={`Go to page ${page}`}\n                  aria-current={page === currentPage ? 'page' : undefined}\n                >\n                  {page}\n                </button>\n              )}\n            </React.Fragment>\n          ))}\n\n          {/* Next button */}\n          <button\n            onClick={handleNext}\n            disabled={currentPage === totalPages}\n            className={`inline-flex items-center px-2 py-2 text-sm font-medium border ${\n              currentPage === totalPages\n                ? 'text-gray-300 bg-gray-100 border-gray-300 cursor-not-allowed dark:bg-gray-700 dark:border-gray-600 dark:text-gray-500'\n                : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300'\n            } ${showFirstLast && currentPage < totalPages ? '' : 'rounded-r-md'}`}\n            aria-label=\"Go to next page\"\n          >\n            <i className=\"ri-arrow-right-s-line\"></i>\n          </button>\n\n          {/* Last page button */}\n          {showFirstLast && currentPage < totalPages && (\n            <button\n              onClick={handleLast}\n              className=\"inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300\"\n              aria-label=\"Go to last page\"\n            >\n              <i className=\"ri-skip-forward-line\"></i>\n            </button>\n          )}\n        </nav>\n      )}\n    </div>\n  );\n};\n\nexport default Pagination;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AA4BA,MAAM,aAAwC,CAAC,EAC7C,IAAI,EACJ,YAAY,EACZ,gBAAgB,EAChB,gBAAgB,IAAI,EACpB,uBAAuB,IAAI,EAC3B,WAAW,IAAI,EACf,kBAAkB,CAAC,EACnB,kBAAkB;IAAC;IAAI;IAAI;IAAI;CAAI,EACnC,YAAY,EAAE,EACf;IACC,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG;IAE9D,+EAA+E;IAC/E,IAAI,cAAc,KAAK,CAAC,wBAAwB,CAAC,UAAU;QACzD,OAAO;IACT;IAEA,gCAAgC;IAChC,MAAM,YAAY,aAAa,IAAI,CAAC,cAAc,CAAC,IAAI,eAAe,IAAI;IAC1E,MAAM,UAAU,KAAK,GAAG,CAAC,cAAc,cAAc;IAErD,gCAAgC;IAChC,MAAM,kBAAkB;QACtB,MAAM,QAA6B,EAAE;QAErC,oEAAoE;QACpE,IAAI,cAAc,iBAAiB;YACjC,IAAK,IAAI,IAAI,GAAG,KAAK,YAAY,IAAK;gBACpC,MAAM,IAAI,CAAC;YACb;YACA,OAAO;QACT;QAEA,yBAAyB;QACzB,MAAM,IAAI,CAAC;QAEX,mEAAmE;QACnE,MAAM,YAAY,KAAK,KAAK,CAAC,CAAC,kBAAkB,CAAC,IAAI,IAAI,kCAAkC;QAC3F,IAAI,YAAY,KAAK,GAAG,CAAC,GAAG,cAAc;QAC1C,IAAI,UAAU,KAAK,GAAG,CAAC,aAAa,GAAG,cAAc;QAErD,qCAAqC;QACrC,IAAI,eAAe,YAAY,GAAG;YAChC,YAAY;YACZ,UAAU,KAAK,GAAG,CAAC,aAAa,GAAG,kBAAkB;QACvD;QAEA,+BAA+B;QAC/B,IAAI,eAAe,aAAa,YAAY,GAAG;YAC7C,YAAY,KAAK,GAAG,CAAC,GAAG,aAAa,kBAAkB;YACvD,UAAU,aAAa;QACzB;QAEA,0CAA0C;QAC1C,IAAI,YAAY,GAAG;YACjB,MAAM,IAAI,CAAC;QACb;QAEA,iCAAiC;QACjC,IAAK,IAAI,IAAI,WAAW,KAAK,SAAS,IAAK;YACzC,MAAM,IAAI,CAAC;QACb;QAEA,0CAA0C;QAC1C,IAAI,UAAU,aAAa,GAAG;YAC5B,MAAM,IAAI,CAAC;QACb;QAEA,uDAAuD;QACvD,IAAI,aAAa,GAAG;YAClB,MAAM,IAAI,CAAC;QACb;QAEA,OAAO;IACT;IAEA,MAAM,eAAe;IAErB,MAAM,kBAAkB,CAAC;QACvB,IAAI,OAAO,SAAS,YAAY,SAAS,aAAa;YACpD,aAAa;QACf;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,cAAc,SAAS,MAAM,MAAM,CAAC,KAAK;QAC/C,IAAI,kBAAkB;YACpB,iBAAiB;QACnB;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,cAAc,GAAG;YACnB,aAAa,cAAc;QAC7B;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,cAAc,YAAY;YAC5B,aAAa,cAAc;QAC7B;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,gBAAgB,GAAG;YACrB,aAAa;QACf;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,gBAAgB,YAAY;YAC9B,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,gKAAgK,EAAE,WAAW;;0BAE5L,6LAAC;gBAAI,WAAU;;oBAEZ,YAAY,aAAa,mBACxB,6LAAC;wBAAI,WAAU;;4BAA2C;0CAChD,6LAAC;gCAAK,WAAU;0CAAe;;;;;;4BAAiB;4BAAI;0CAC5D,6LAAC;gCAAK,WAAU;0CAAe;;;;;;4BAAe;4BAAI;0CAClD,6LAAC;gCAAK,WAAU;0CAAe;;;;;;4BAAkB;;;;;;;oBAKpD,wBAAwB,kCACvB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,OAAO;4BACP,UAAU;4BACV,WAAU;sCAET,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC;oCAAkB,OAAO;;wCACvB;wCAAK;;mCADK;;;;;;;;;;;;;;;;;;;;;YAUtB,aAAa,mBACZ,6LAAC;gBAAI,WAAU;gBAA8B,cAAW;;oBAErD,iBAAiB,cAAc,mBAC9B,6LAAC;wBACC,SAAS;wBACT,WAAU;wBACV,cAAW;kCAEX,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;kCAKjB,6LAAC;wBACC,SAAS;wBACT,UAAU,gBAAgB;wBAC1B,WAAW,CAAC,8DAA8D,EACxE,gBAAgB,IACZ,0HACA,uLACL,CAAC,EAAE,iBAAiB,cAAc,IAAI,KAAK,gBAAgB;wBAC5D,cAAW;kCAEX,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;oBAId,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;sCACZ,SAAS,sBACR,6LAAC;gCAAK,WAAU;0CAAgK;;;;;qDAIhL,6LAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAW,CAAC,8DAA8D,EACxE,SAAS,cACL,0DACA,wLACJ;gCACF,cAAY,CAAC,WAAW,EAAE,MAAM;gCAChC,gBAAc,SAAS,cAAc,SAAS;0CAE7C;;;;;;2BAhBc;;;;;kCAuBvB,6LAAC;wBACC,SAAS;wBACT,UAAU,gBAAgB;wBAC1B,WAAW,CAAC,8DAA8D,EACxE,gBAAgB,aACZ,0HACA,uLACL,CAAC,EAAE,iBAAiB,cAAc,aAAa,KAAK,gBAAgB;wBACrE,cAAW;kCAEX,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;oBAId,iBAAiB,cAAc,4BAC9B,6LAAC;wBACC,SAAS;wBACT,WAAU;wBACV,cAAW;kCAEX,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAO3B;KAnOM;uCAqOS", "debugId": null}}, {"offset": {"line": 574, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/DataTable.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { PaginatedResponse, PaginateQuery } from '../../services/userService';\r\nimport Pagination from './Pagination';\r\n\r\ninterface Column<T> {\r\n  key: keyof T | string;\r\n  label: string;\r\n  sortable?: boolean;\r\n  searchable?: boolean;\r\n  render?: (value: any, item: T) => React.ReactNode;\r\n  className?: string;\r\n}\r\n\r\ninterface DataTableProps<T> {\r\n  columns: Column<T>[];\r\n  data: PaginatedResponse<T> | null;\r\n  loading?: boolean;\r\n  onQueryChange: (query: PaginateQuery) => void;\r\n  searchPlaceholder?: string;\r\n  className?: string;\r\n}\r\n\r\nexport default function DataTable<T extends Record<string, any>>({\r\n  columns,\r\n  data,\r\n  loading = false,\r\n  onQueryChange,\r\n  searchPlaceholder = \"Search...\",\r\n  className = \"\",\r\n}: DataTableProps<T>) {\r\n  const [query, setQuery] = useState<PaginateQuery>({\r\n    page: 1,\r\n    limit: 10,\r\n    search: '',\r\n    sortBy: [],\r\n  });\r\n  const [searchInput, setSearchInput] = useState('');\r\n\r\n  // Debounced search effect\r\n  useEffect(() => {\r\n    const timeoutId = setTimeout(() => {\r\n      if (searchInput !== query.search) {\r\n        handleSearch(searchInput);\r\n      }\r\n    }, 300);\r\n\r\n    return () => clearTimeout(timeoutId);\r\n  }, [searchInput]);\r\n\r\n  const handleSearch = useCallback((search: string) => {\r\n    try {\r\n      const newQuery = { ...query, search, page: 1 };\r\n      setQuery(newQuery);\r\n      onQueryChange(newQuery);\r\n    } catch (error) {\r\n      console.error('Error handling search:', error);\r\n    }\r\n  }, [query, onQueryChange]);\r\n\r\n  const handleSort = (columnKey: string) => {\r\n    const currentSort = query.sortBy?.find(sort => sort.startsWith(columnKey));\r\n    let newSortBy: string[] = [];\r\n\r\n    if (!currentSort) {\r\n      newSortBy = [`${columnKey}:ASC`];\r\n    } else if (currentSort.endsWith(':ASC')) {\r\n      newSortBy = [`${columnKey}:DESC`];\r\n    } else {\r\n      newSortBy = [];\r\n    }\r\n\r\n    const newQuery = { ...query, sortBy: newSortBy, page: 1 };\r\n    setQuery(newQuery);\r\n    onQueryChange(newQuery);\r\n  };\r\n\r\n  const handlePageChange = (page: number) => {\r\n    const newQuery = { ...query, page };\r\n    setQuery(newQuery);\r\n    onQueryChange(newQuery);\r\n  };\r\n\r\n  const handleLimitChange = (limit: number) => {\r\n    const newQuery = { ...query, limit, page: 1 };\r\n    setQuery(newQuery);\r\n    onQueryChange(newQuery);\r\n  };\r\n\r\n  const getSortDirection = (columnKey: string): 'asc' | 'desc' | null => {\r\n    const currentSort = query.sortBy?.find(sort => sort.startsWith(columnKey));\r\n    if (!currentSort) return null;\r\n    return currentSort.endsWith(':ASC') ? 'asc' : 'desc';\r\n  };\r\n\r\n  // Handle null data case early\r\n  if (!data) {\r\n    return (\r\n      <div className={`bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden ${className}`}>\r\n        <div className=\"p-6 text-center text-gray-500 dark:text-gray-400\">\r\n          <div className=\"flex items-center justify-center\">\r\n            <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-red-600\"></div>\r\n            <span className=\"ml-2\">Loading...</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const handlePageSizeChange = (newPageSize: number) => {\r\n    const newQuery = { ...query, limit: newPageSize, page: 1 };\r\n    setQuery(newQuery);\r\n    onQueryChange(newQuery);\r\n  };\r\n\r\n  return (\r\n    <div className={`bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden ${className}`}>\r\n      {/* Search Bar */}\r\n      <div className=\"p-6 border-b border-gray-200 dark:border-gray-700\">\r\n        <div className=\"relative\">\r\n          <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n            <i className=\"ri-search-line text-gray-400 dark:text-gray-500\"></i>\r\n          </div>\r\n          <input\r\n            type=\"text\"\r\n            placeholder={searchPlaceholder}\r\n            value={searchInput}\r\n            onChange={(e) => setSearchInput(e.target.value)}\r\n            className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-gray-900 dark:text-gray-100\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Table */}\r\n      <div className=\"overflow-x-auto\" style={{ minHeight: '500px' }}>\r\n        <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\r\n          <thead className=\"bg-gray-50 dark:bg-gray-900\">\r\n            <tr>\r\n              {columns.map((column) => (\r\n                <th\r\n                  key={String(column.key)}\r\n                  className={`px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${\r\n                    column.sortable ? 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800' : ''\r\n                  } ${column.className || ''}`}\r\n                  onClick={() => column.sortable && handleSort(String(column.key))}\r\n                >\r\n                  <div className=\"flex items-center space-x-1\">\r\n                    <span>{column.label}</span>\r\n                    {column.sortable && (\r\n                      <div className=\"flex flex-col\">\r\n                        <i className={`ri-arrow-up-s-line text-xs ${\r\n                          getSortDirection(String(column.key)) === 'asc' ? 'text-red-600' : 'text-gray-400'\r\n                        }`}></i>\r\n                        <i className={`ri-arrow-down-s-line text-xs -mt-1 ${\r\n                          getSortDirection(String(column.key)) === 'desc' ? 'text-red-600' : 'text-gray-400'\r\n                        }`}></i>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </th>\r\n              ))}\r\n            </tr>\r\n          </thead>\r\n          <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\r\n            {loading ? (\r\n              <tr>\r\n                <td colSpan={columns.length} className=\"px-6 py-4 text-center text-gray-500 dark:text-gray-400\">\r\n                  <div className=\"flex items-center justify-center\">\r\n                    <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-red-600\"></div>\r\n                    <span className=\"ml-2\">Loading...</span>\r\n                  </div>\r\n                </td>\r\n              </tr>\r\n            ) : !data?.data || data.data.length === 0 ? (\r\n              <tr>\r\n                <td colSpan={columns.length} className=\"px-6 py-4 text-center text-gray-500 dark:text-gray-400\">\r\n                  No data found\r\n                </td>\r\n              </tr>\r\n            ) : (\r\n              data.data.map((item, index) => (\r\n                <tr key={index} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\r\n                  {columns.map((column) => (\r\n                    <td key={String(column.key)} className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100\">\r\n                      {column.render\r\n                        ? column.render(item[column.key as keyof T], item)\r\n                        : String(item[column.key as keyof T] || '')\r\n                      }\r\n                    </td>\r\n                  ))}\r\n                </tr>\r\n              ))\r\n            )}\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n\r\n      {/* Pagination */}\r\n      {data?.meta && (\r\n        <Pagination\r\n          meta={data.meta}\r\n          onPageChange={handlePageChange}\r\n          onPageSizeChange={handlePageSizeChange}\r\n          showFirstLast={true}\r\n          showPageSizeSelector={true}\r\n          showInfo={true}\r\n          maxVisiblePages={7}\r\n          pageSizeOptions={[10, 25, 50, 100]}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;;;AAJA;;;AAwBe,SAAS,UAAyC,EAC/D,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,aAAa,EACb,oBAAoB,WAAW,EAC/B,YAAY,EAAE,EACI;;IAClB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QAChD,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ,EAAE;IACZ;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM,YAAY;iDAAW;oBAC3B,IAAI,gBAAgB,MAAM,MAAM,EAAE;wBAChC,aAAa;oBACf;gBACF;gDAAG;YAEH;uCAAO,IAAM,aAAa;;QAC5B;8BAAG;QAAC;KAAY;IAEhB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE,CAAC;YAChC,IAAI;gBACF,MAAM,WAAW;oBAAE,GAAG,KAAK;oBAAE;oBAAQ,MAAM;gBAAE;gBAC7C,SAAS;gBACT,cAAc;YAChB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;YAC1C;QACF;8CAAG;QAAC;QAAO;KAAc;IAEzB,MAAM,aAAa,CAAC;QAClB,MAAM,cAAc,MAAM,MAAM,EAAE,KAAK,CAAA,OAAQ,KAAK,UAAU,CAAC;QAC/D,IAAI,YAAsB,EAAE;QAE5B,IAAI,CAAC,aAAa;YAChB,YAAY;gBAAC,GAAG,UAAU,IAAI,CAAC;aAAC;QAClC,OAAO,IAAI,YAAY,QAAQ,CAAC,SAAS;YACvC,YAAY;gBAAC,GAAG,UAAU,KAAK,CAAC;aAAC;QACnC,OAAO;YACL,YAAY,EAAE;QAChB;QAEA,MAAM,WAAW;YAAE,GAAG,KAAK;YAAE,QAAQ;YAAW,MAAM;QAAE;QACxD,SAAS;QACT,cAAc;IAChB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,WAAW;YAAE,GAAG,KAAK;YAAE;QAAK;QAClC,SAAS;QACT,cAAc;IAChB;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,WAAW;YAAE,GAAG,KAAK;YAAE;YAAO,MAAM;QAAE;QAC5C,SAAS;QACT,cAAc;IAChB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,cAAc,MAAM,MAAM,EAAE,KAAK,CAAA,OAAQ,KAAK,UAAU,CAAC;QAC/D,IAAI,CAAC,aAAa,OAAO;QACzB,OAAO,YAAY,QAAQ,CAAC,UAAU,QAAQ;IAChD;IAEA,8BAA8B;IAC9B,IAAI,CAAC,MAAM;QACT,qBACE,6LAAC;YAAI,WAAW,CAAC,4DAA4D,EAAE,WAAW;sBACxF,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAK,WAAU;sCAAO;;;;;;;;;;;;;;;;;;;;;;IAKjC;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,WAAW;YAAE,GAAG,KAAK;YAAE,OAAO;YAAa,MAAM;QAAE;QACzD,SAAS;QACT,cAAc;IAChB;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,4DAA4D,EAAE,WAAW;;0BAExF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;;;;;;;;;;;sCAEf,6LAAC;4BACC,MAAK;4BACL,aAAa;4BACb,OAAO;4BACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4BAC9C,WAAU;;;;;;;;;;;;;;;;;0BAMhB,6LAAC;gBAAI,WAAU;gBAAkB,OAAO;oBAAE,WAAW;gBAAQ;0BAC3D,cAAA,6LAAC;oBAAM,WAAU;;sCACf,6LAAC;4BAAM,WAAU;sCACf,cAAA,6LAAC;0CACE,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;wCAEC,WAAW,CAAC,kGAAkG,EAC5G,OAAO,QAAQ,GAAG,4DAA4D,GAC/E,CAAC,EAAE,OAAO,SAAS,IAAI,IAAI;wCAC5B,SAAS,IAAM,OAAO,QAAQ,IAAI,WAAW,OAAO,OAAO,GAAG;kDAE9D,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAM,OAAO,KAAK;;;;;;gDAClB,OAAO,QAAQ,kBACd,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAW,CAAC,2BAA2B,EACxC,iBAAiB,OAAO,OAAO,GAAG,OAAO,QAAQ,iBAAiB,iBAClE;;;;;;sEACF,6LAAC;4DAAE,WAAW,CAAC,mCAAmC,EAChD,iBAAiB,OAAO,OAAO,GAAG,OAAO,SAAS,iBAAiB,iBACnE;;;;;;;;;;;;;;;;;;uCAfH,OAAO,OAAO,GAAG;;;;;;;;;;;;;;;sCAuB9B,6LAAC;4BAAM,WAAU;sCACd,wBACC,6LAAC;0CACC,cAAA,6LAAC;oCAAG,SAAS,QAAQ,MAAM;oCAAE,WAAU;8CACrC,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAK,WAAU;0DAAO;;;;;;;;;;;;;;;;;;;;;uCAI3B,CAAC,MAAM,QAAQ,KAAK,IAAI,CAAC,MAAM,KAAK,kBACtC,6LAAC;0CACC,cAAA,6LAAC;oCAAG,SAAS,QAAQ,MAAM;oCAAE,WAAU;8CAAyD;;;;;;;;;;uCAKlG,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,sBACnB,6LAAC;oCAAe,WAAU;8CACvB,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;4CAA4B,WAAU;sDACpC,OAAO,MAAM,GACV,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,CAAY,EAAE,QAC3C,OAAO,IAAI,CAAC,OAAO,GAAG,CAAY,IAAI;2CAHnC,OAAO,OAAO,GAAG;;;;;mCAFrB;;;;;;;;;;;;;;;;;;;;;YAiBlB,MAAM,sBACL,6LAAC,6IAAA,CAAA,UAAU;gBACT,MAAM,KAAK,IAAI;gBACf,cAAc;gBACd,kBAAkB;gBAClB,eAAe;gBACf,sBAAsB;gBACtB,UAAU;gBACV,iBAAiB;gBACjB,iBAAiB;oBAAC;oBAAI;oBAAI;oBAAI;iBAAI;;;;;;;;;;;;AAK5C;GA7LwB;KAAA", "debugId": null}}, {"offset": {"line": 958, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/ConfirmationModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ReactNode } from 'react';\r\n\r\ninterface ConfirmationModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onConfirm: () => void;\r\n  title: string;\r\n  message: string | ReactNode;\r\n  confirmText?: string;\r\n  cancelText?: string;\r\n  confirmVariant?: 'danger' | 'primary' | 'warning';\r\n  loading?: boolean;\r\n  icon?: ReactNode;\r\n}\r\n\r\nexport default function ConfirmationModal({\r\n  isOpen,\r\n  onClose,\r\n  onConfirm,\r\n  title,\r\n  message,\r\n  confirmText = 'Confirm',\r\n  cancelText = 'Cancel',\r\n  confirmVariant = 'danger',\r\n  loading = false,\r\n  icon,\r\n}: ConfirmationModalProps) {\r\n  if (!isOpen) return null;\r\n\r\n  const getConfirmButtonStyles = () => {\r\n    switch (confirmVariant) {\r\n      case 'danger':\r\n        return 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500';\r\n      case 'primary':\r\n        return 'bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500';\r\n      case 'warning':\r\n        return 'bg-yellow-600 hover:bg-yellow-700 text-white focus:ring-yellow-500';\r\n      default:\r\n        return 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500';\r\n    }\r\n  };\r\n\r\n  const getDefaultIcon = () => {\r\n    switch (confirmVariant) {\r\n      case 'danger':\r\n        return (\r\n          <div className=\"flex-shrink-0\">\r\n            <div className=\"h-10 w-10 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center\">\r\n              <i className=\"ri-delete-bin-line text-red-600 dark:text-red-400 text-xl\"></i>\r\n            </div>\r\n          </div>\r\n        );\r\n      case 'warning':\r\n        return (\r\n          <div className=\"flex-shrink-0\">\r\n            <div className=\"h-10 w-10 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center\">\r\n              <i className=\"ri-alert-line text-yellow-600 dark:text-yellow-400 text-xl\"></i>\r\n            </div>\r\n          </div>\r\n        );\r\n      default:\r\n        return (\r\n          <div className=\"flex-shrink-0\">\r\n            <div className=\"h-10 w-10 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center\">\r\n              <i className=\"ri-information-line text-blue-600 dark:text-blue-400 text-xl\"></i>\r\n            </div>\r\n          </div>\r\n        );\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center z-50 p-4\">\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all\">\r\n        <div className=\"p-6\">\r\n          {/* Header */}\r\n          <div className=\"flex items-start mb-4\">\r\n            {icon || getDefaultIcon()}\r\n            <div className=\"ml-4 flex-1\">\r\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2\">\r\n                {title}\r\n              </h3>\r\n              <div className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                {typeof message === 'string' ? (\r\n                  <p>{message}</p>\r\n                ) : (\r\n                  message\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Actions */}\r\n          <div className=\"flex space-x-3 mt-6\">\r\n            <button\r\n              onClick={onConfirm}\r\n              disabled={loading}\r\n              className={`flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed ${getConfirmButtonStyles()}`}\r\n            >\r\n              {loading ? (\r\n                <div className=\"flex items-center justify-center\">\r\n                  <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                    <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                    <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                  </svg>\r\n                  Processing...\r\n                </div>\r\n              ) : (\r\n                confirmText\r\n              )}\r\n            </button>\r\n            <button\r\n              onClick={onClose}\r\n              disabled={loading}\r\n              className=\"flex-1 px-4 py-2 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-800 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n            >\r\n              {cancelText}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;AAiBe,SAAS,kBAAkB,EACxC,MAAM,EACN,OAAO,EACP,SAAS,EACT,KAAK,EACL,OAAO,EACP,cAAc,SAAS,EACvB,aAAa,QAAQ,EACrB,iBAAiB,QAAQ,EACzB,UAAU,KAAK,EACf,IAAI,EACmB;IACvB,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,yBAAyB;QAC7B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;YAIrB,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;YAIrB;gBACE,qBACE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;QAIvB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;4BACZ,QAAQ;0CACT,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDACX;;;;;;kDAEH,6LAAC;wCAAI,WAAU;kDACZ,OAAO,YAAY,yBAClB,6LAAC;sDAAG;;;;;mDAEJ;;;;;;;;;;;;;;;;;;kCAOR,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAW,CAAC,mNAAmN,EAAE,0BAA0B;0CAE1P,wBACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;4CAA6C,OAAM;4CAA6B,MAAK;4CAAO,SAAQ;;8DACjH,6LAAC;oDAAO,WAAU;oDAAa,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAK,QAAO;oDAAe,aAAY;;;;;;8DACxF,6LAAC;oDAAK,WAAU;oDAAa,MAAK;oDAAe,GAAE;;;;;;;;;;;;wCAC/C;;;;;;2CAIR;;;;;;0CAGJ,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;KA5GwB", "debugId": null}}, {"offset": {"line": 1196, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/AssignModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport { taskService, TaskType, TaskPriority, TaskStatus, Task } from '@/services/task-assignment';\r\n\r\ninterface Officer {\r\n  user_id: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  email: string;\r\n  department?: string;\r\n}\r\n\r\ninterface AssignModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  itemId: string | null;\r\n  itemType: 'data_breach' | 'application' | 'complaint' | 'inspection' | 'document_review' | 'task';\r\n  itemTitle?: string;\r\n  onAssignSuccess?: () => void;\r\n  // Reassignment mode props\r\n  mode?: 'assign' | 'reassign';\r\n  task?: Task | null; // For reassignment mode\r\n  onReassignSuccess?: () => void;\r\n}\r\n\r\nconst AssignModal: React.FC<AssignModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  itemId,\r\n  itemType,\r\n  itemTitle,\r\n  onAssignSuccess,\r\n  mode = 'assign',\r\n  task,\r\n  onReassignSuccess\r\n}) => {\r\n  const { showSuccess, showError } = useToast();\r\n  const [officers, setOfficers] = useState<Officer[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [assigning, setAssigning] = useState(false);\r\n  const [selectedOfficer, setSelectedOfficer] = useState<string>('');\r\n  const [comment, setComment] = useState('');\r\n  const [dueDate, setDueDate] = useState<string>('');\r\n\r\n  const isReassignMode = mode === 'reassign' && task;\r\n\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      fetchOfficers();\r\n      // For reassignment, pre-select the current assignee\r\n      setSelectedOfficer(isReassignMode ? (task?.assigned_to || '') : '');\r\n      setComment('');\r\n      // For reassignment, pre-fill the current due date if available\r\n      setDueDate(isReassignMode && task?.due_date ? task.due_date.split('T')[0] : '');\r\n    }\r\n  }, [isOpen, isReassignMode, task]);\r\n\r\n  const fetchOfficers = async () => {\r\n    setLoading(true);\r\n    try {\r\n      const response = await taskService.getOfficers();\r\n      const officersData = response.data || [];\r\n      setOfficers(officersData);\r\n\r\n      if (officersData.length === 0) {\r\n        console.warn('No officers found for task assignment');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching officers:', error);\r\n      setOfficers([]);\r\n      showError('Failed to load officers. Please try again.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const getTaskTypeFromItemType = (itemType: string): TaskType => {\r\n    switch (itemType) {\r\n      case 'application':\r\n        return TaskType.EVALUATION;\r\n      case 'data_breach':\r\n        return TaskType.DATA_BREACH;\r\n      case 'complaint':\r\n        return TaskType.COMPLAINT;\r\n      case 'inspection':\r\n        return TaskType.INSPECTION;\r\n      case 'document_review':\r\n        return TaskType.DOCUMENT_REVIEW;\r\n      case 'task':\r\n        return TaskType.APPLICATION; // Default for existing tasks\r\n      default:\r\n        return TaskType.APPLICATION;\r\n    }\r\n  };\r\n\r\n  const getTaskTitle = (itemType: string, itemTitle?: string): string => {\r\n    const baseTitle = itemTitle || 'Untitled';\r\n    switch (itemType) {\r\n      case 'application':\r\n        return `Application Evaluation: ${baseTitle}`;\r\n      case 'data_breach':\r\n        return `Data Breach Investigation: ${baseTitle}`;\r\n      case 'complaint':\r\n        return `Complaint Review: ${baseTitle}`;\r\n      case 'inspection':\r\n        return `Inspection Task: ${baseTitle}`;\r\n      case 'document_review':\r\n        return `Document Review: ${baseTitle}`;\r\n      default:\r\n        return `Task: ${baseTitle}`;\r\n    }\r\n  };\r\n\r\n  const getTaskDescription = (itemType: string, itemTitle?: string): string => {\r\n    const baseTitle = itemTitle || 'item';\r\n    switch (itemType) {\r\n      case 'application':\r\n        return `Evaluate and review application ${baseTitle} for compliance and approval.`;\r\n      case 'data_breach':\r\n        return `Investigate and assess data breach report ${baseTitle} for regulatory compliance.`;\r\n      case 'complaint':\r\n        return `Review and resolve complaint ${baseTitle} according to regulatory procedures.`;\r\n      case 'inspection':\r\n        return `Conduct inspection for ${baseTitle} to ensure regulatory compliance.`;\r\n      case 'document_review':\r\n        return `Review and validate document ${baseTitle} for accuracy and compliance.`;\r\n      default:\r\n        return `Process and review ${baseTitle}.`;\r\n    }\r\n  };\r\n\r\n  const handleAssign = async () => {\r\n    if (!selectedOfficer) {\r\n      showError('Please select an officer');\r\n      return;\r\n    }\r\n\r\n    if (!dueDate) {\r\n      showError('Please select a due date');\r\n      return;\r\n    }\r\n\r\n    if (isReassignMode && !task) {\r\n      showError('Task information is missing');\r\n      return;\r\n    }\r\n\r\n    if (!isReassignMode && !itemId) {\r\n      showError('Item ID is missing');\r\n      return;\r\n    }\r\n\r\n    setAssigning(true);\r\n    try {\r\n      if (isReassignMode && task) {\r\n        // Reassign existing task\r\n        await taskService.reassignTask(task.task_id, {\r\n          assignedTo: selectedOfficer,\r\n          comment: comment.trim() || undefined\r\n        });\r\n\r\n        // Update due date if provided\r\n        if (dueDate) {\r\n          await taskService.updateTask(task.task_id, {\r\n            due_date: dueDate\r\n          });\r\n        }\r\n\r\n        showSuccess('Task reassigned successfully');\r\n        onReassignSuccess?.();\r\n      } else {\r\n        // Create a new polymorphic task based on item type\r\n        const taskData = {\r\n          task_type: getTaskTypeFromItemType(itemType),\r\n          title: getTaskTitle(itemType, itemTitle),\r\n          description: getTaskDescription(itemType, itemTitle),\r\n          priority: TaskPriority.MEDIUM,\r\n          status: TaskStatus.PENDING,\r\n          entity_type: itemType,\r\n          entity_id: itemId!,\r\n          assigned_to: selectedOfficer,\r\n          due_date: dueDate,\r\n          metadata: {\r\n            comment: comment.trim() || undefined,\r\n            original_item_title: itemTitle,\r\n            assignment_context: 'manual_assignment'\r\n          }\r\n        };\r\n\r\n        await taskService.createTask(taskData);\r\n\r\n        // Note: For application tasks, the backend automatically handles:\r\n        // - Setting application.assigned_to and assigned_at fields\r\n        // - Updating application status to 'evaluation'\r\n        // This happens in the TasksService when the task is assigned\r\n\r\n        showSuccess(`Successfully assigned ${itemType.replace('_', ' ')} to officer`);\r\n        onAssignSuccess?.();\r\n      }\r\n\r\n      onClose();\r\n    } catch (error) {\r\n      console.error(`Error ${isReassignMode ? 'reassigning' : 'creating assignment'} task:`, error);\r\n      showError(`Failed to ${isReassignMode ? 'reassign' : 'assign'} task`);\r\n    } finally {\r\n      setAssigning(false);\r\n    }\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\r\n      <div className=\"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800\">\r\n        <div className=\"mt-3\">\r\n          {/* Header */}\r\n          <div className=\"flex items-center justify-between mb-6\">\r\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n              {isReassignMode ? 'Reassign Task' : `Create Task for ${itemType.replace('_', ' ').toUpperCase()}`}\r\n            </h3>\r\n            <button\r\n              type=\"button\"\r\n              onClick={onClose}\r\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\r\n            >\r\n              <i className=\"ri-close-line text-xl\"></i>\r\n            </button>\r\n          </div>\r\n\r\n          {/* Task Details */}\r\n          <div className=\"mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800\">\r\n            <h4 className=\"text-sm font-medium text-blue-900 dark:text-blue-100 mb-2 flex items-center\">\r\n              <i className=\"ri-task-line mr-2\"></i>\r\n              {isReassignMode ? 'Task Details:' : 'Task to be Created:'}\r\n            </h4>\r\n            <p className=\"text-sm text-blue-700 dark:text-blue-300 font-medium\">\r\n              {isReassignMode ? task?.title : getTaskTitle(itemType, itemTitle)}\r\n            </p>\r\n            <p className=\"text-xs text-blue-600 dark:text-blue-400 mt-1\">\r\n              {isReassignMode ? task?.description : getTaskDescription(itemType, itemTitle)}\r\n            </p>\r\n            {isReassignMode && task?.task_number && (\r\n              <p className=\"text-xs text-blue-600 dark:text-blue-400 mt-1\">\r\n                Task #{task.task_number}\r\n              </p>\r\n            )}\r\n            {isReassignMode && task?.assignee && (\r\n              <p className=\"text-xs text-blue-600 dark:text-blue-400 mt-1\">\r\n                Currently assigned to: {task.assignee.first_name} {task.assignee.last_name}\r\n              </p>\r\n            )}\r\n          </div>\r\n\r\n          {/* Officer Selection */}\r\n          <div className=\"mb-6\">\r\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n              {isReassignMode ? 'Reassign to Officer *' : 'Assign to Officer *'}\r\n            </label>\r\n            {loading ? (\r\n              <div className=\"text-center py-4 text-gray-500 dark:text-gray-400\">\r\n                Loading officers...\r\n              </div>\r\n            ) : (\r\n              <select\r\n                value={selectedOfficer}\r\n                onChange={(e) => setSelectedOfficer(e.target.value)}\r\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\r\n              >\r\n                <option value=\"\">Select an officer...</option>\r\n                {officers.map((officer) => (\r\n                  <option key={officer.user_id} value={officer.user_id}>\r\n                    {officer.first_name} {officer.last_name} - {officer.email}\r\n                    {officer.department ? ` (${officer.department})` : ''}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n            )}\r\n          </div>\r\n\r\n          {/* Due Date */}\r\n          <div className=\"mb-6\">\r\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n              Due Date *\r\n            </label>\r\n            <input\r\n              type=\"date\"\r\n              value={dueDate}\r\n              onChange={(e) => setDueDate(e.target.value)}\r\n              min={new Date().toISOString().split('T')[0]} // Prevent past dates\r\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\r\n            />\r\n          </div>\r\n\r\n          {/* Comment */}\r\n          <div className=\"mb-6\">\r\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n              {isReassignMode ? 'Reassignment Notes (Optional)' : 'Task Notes (Optional)'}\r\n            </label>\r\n            <textarea\r\n              value={comment}\r\n              onChange={(e) => setComment(e.target.value)}\r\n              placeholder={isReassignMode ? 'Add any notes about this reassignment...' : 'Add any specific instructions or context for this task...'}\r\n              rows={3}\r\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\r\n            />\r\n          </div>\r\n\r\n          {/* Selected Officer Summary */}\r\n          {selectedOfficer && (\r\n            <div className=\"mb-6 p-4 bg-green-50 dark:bg-green-900 rounded-lg\">\r\n              <h4 className=\"text-sm font-medium text-green-900 dark:text-green-100 mb-2\">\r\n                Selected Officer:\r\n              </h4>\r\n              <div className=\"text-sm text-green-700 dark:text-green-200\">\r\n                {(() => {\r\n                  const officer = officers.find(o => o.user_id === selectedOfficer);\r\n                  return officer ? (\r\n                    <>\r\n                      {officer.first_name} {officer.last_name}\r\n                      <br />\r\n                      {officer.email}\r\n                      {officer.department && (\r\n                        <>\r\n                          <br />\r\n                          Department: {officer.department}\r\n                        </>\r\n                      )}\r\n                    </>\r\n                  ) : 'Officer not found';\r\n                })()}\r\n              </div>\r\n              {dueDate && (\r\n                <div className=\"text-sm text-green-700 dark:text-green-200 mt-2\">\r\n                  <strong>Due Date:</strong> {new Date(dueDate).toLocaleDateString()}\r\n                </div>\r\n              )}\r\n            </div>\r\n          )}\r\n\r\n          {/* Actions */}\r\n          <div className=\"flex justify-end space-x-3\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={onClose}\r\n              className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n            >\r\n              Cancel\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={handleAssign}\r\n              disabled={!selectedOfficer || !dueDate || assigning}\r\n              className=\"px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center\"\r\n            >\r\n              {assigning ? (\r\n                <>\r\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\r\n                  {isReassignMode ? 'Reassigning...' : 'Creating Task...'}\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <i className={isReassignMode ? \"ri-user-shared-line mr-2\" : \"ri-task-line mr-2\"}></i>\r\n                  {isReassignMode ? 'Reassign Task' : 'Create Task'}\r\n                </>\r\n              )}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AssignModal;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AA2BA,MAAM,cAA0C,CAAC,EAC/C,MAAM,EACN,OAAO,EACP,MAAM,EACN,QAAQ,EACR,SAAS,EACT,eAAe,EACf,OAAO,QAAQ,EACf,IAAI,EACJ,iBAAiB,EAClB;;IACC,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE/C,MAAM,iBAAiB,SAAS,cAAc;IAE9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,QAAQ;gBACV;gBACA,oDAAoD;gBACpD,mBAAmB,iBAAkB,MAAM,eAAe,KAAM;gBAChE,WAAW;gBACX,+DAA+D;gBAC/D,WAAW,kBAAkB,MAAM,WAAW,KAAK,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;YAC9E;QACF;gCAAG;QAAC;QAAQ;QAAgB;KAAK;IAEjC,MAAM,gBAAgB;QACpB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,wIAAA,CAAA,cAAW,CAAC,WAAW;YAC9C,MAAM,eAAe,SAAS,IAAI,IAAI,EAAE;YACxC,YAAY;YAEZ,IAAI,aAAa,MAAM,KAAK,GAAG;gBAC7B,QAAQ,IAAI,CAAC;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,YAAY,EAAE;YACd,UAAU;QACZ,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,OAAQ;YACN,KAAK;gBACH,OAAO,wIAAA,CAAA,WAAQ,CAAC,UAAU;YAC5B,KAAK;gBACH,OAAO,wIAAA,CAAA,WAAQ,CAAC,WAAW;YAC7B,KAAK;gBACH,OAAO,wIAAA,CAAA,WAAQ,CAAC,SAAS;YAC3B,KAAK;gBACH,OAAO,wIAAA,CAAA,WAAQ,CAAC,UAAU;YAC5B,KAAK;gBACH,OAAO,wIAAA,CAAA,WAAQ,CAAC,eAAe;YACjC,KAAK;gBACH,OAAO,wIAAA,CAAA,WAAQ,CAAC,WAAW,EAAE,6BAA6B;YAC5D;gBACE,OAAO,wIAAA,CAAA,WAAQ,CAAC,WAAW;QAC/B;IACF;IAEA,MAAM,eAAe,CAAC,UAAkB;QACtC,MAAM,YAAY,aAAa;QAC/B,OAAQ;YACN,KAAK;gBACH,OAAO,CAAC,wBAAwB,EAAE,WAAW;YAC/C,KAAK;gBACH,OAAO,CAAC,2BAA2B,EAAE,WAAW;YAClD,KAAK;gBACH,OAAO,CAAC,kBAAkB,EAAE,WAAW;YACzC,KAAK;gBACH,OAAO,CAAC,iBAAiB,EAAE,WAAW;YACxC,KAAK;gBACH,OAAO,CAAC,iBAAiB,EAAE,WAAW;YACxC;gBACE,OAAO,CAAC,MAAM,EAAE,WAAW;QAC/B;IACF;IAEA,MAAM,qBAAqB,CAAC,UAAkB;QAC5C,MAAM,YAAY,aAAa;QAC/B,OAAQ;YACN,KAAK;gBACH,OAAO,CAAC,gCAAgC,EAAE,UAAU,6BAA6B,CAAC;YACpF,KAAK;gBACH,OAAO,CAAC,0CAA0C,EAAE,UAAU,2BAA2B,CAAC;YAC5F,KAAK;gBACH,OAAO,CAAC,6BAA6B,EAAE,UAAU,oCAAoC,CAAC;YACxF,KAAK;gBACH,OAAO,CAAC,uBAAuB,EAAE,UAAU,iCAAiC,CAAC;YAC/E,KAAK;gBACH,OAAO,CAAC,6BAA6B,EAAE,UAAU,6BAA6B,CAAC;YACjF;gBACE,OAAO,CAAC,mBAAmB,EAAE,UAAU,CAAC,CAAC;QAC7C;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,iBAAiB;YACpB,UAAU;YACV;QACF;QAEA,IAAI,CAAC,SAAS;YACZ,UAAU;YACV;QACF;QAEA,IAAI,kBAAkB,CAAC,MAAM;YAC3B,UAAU;YACV;QACF;QAEA,IAAI,CAAC,kBAAkB,CAAC,QAAQ;YAC9B,UAAU;YACV;QACF;QAEA,aAAa;QACb,IAAI;YACF,IAAI,kBAAkB,MAAM;gBAC1B,yBAAyB;gBACzB,MAAM,wIAAA,CAAA,cAAW,CAAC,YAAY,CAAC,KAAK,OAAO,EAAE;oBAC3C,YAAY;oBACZ,SAAS,QAAQ,IAAI,MAAM;gBAC7B;gBAEA,8BAA8B;gBAC9B,IAAI,SAAS;oBACX,MAAM,wIAAA,CAAA,cAAW,CAAC,UAAU,CAAC,KAAK,OAAO,EAAE;wBACzC,UAAU;oBACZ;gBACF;gBAEA,YAAY;gBACZ;YACF,OAAO;gBACL,mDAAmD;gBACnD,MAAM,WAAW;oBACf,WAAW,wBAAwB;oBACnC,OAAO,aAAa,UAAU;oBAC9B,aAAa,mBAAmB,UAAU;oBAC1C,UAAU,wIAAA,CAAA,eAAY,CAAC,MAAM;oBAC7B,QAAQ,wIAAA,CAAA,aAAU,CAAC,OAAO;oBAC1B,aAAa;oBACb,WAAW;oBACX,aAAa;oBACb,UAAU;oBACV,UAAU;wBACR,SAAS,QAAQ,IAAI,MAAM;wBAC3B,qBAAqB;wBACrB,oBAAoB;oBACtB;gBACF;gBAEA,MAAM,wIAAA,CAAA,cAAW,CAAC,UAAU,CAAC;gBAE7B,kEAAkE;gBAClE,2DAA2D;gBAC3D,gDAAgD;gBAChD,6DAA6D;gBAE7D,YAAY,CAAC,sBAAsB,EAAE,SAAS,OAAO,CAAC,KAAK,KAAK,WAAW,CAAC;gBAC5E;YACF;YAEA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,MAAM,EAAE,iBAAiB,gBAAgB,sBAAsB,MAAM,CAAC,EAAE;YACvF,UAAU,CAAC,UAAU,EAAE,iBAAiB,aAAa,SAAS,KAAK,CAAC;QACtE,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACX,iBAAiB,kBAAkB,CAAC,gBAAgB,EAAE,SAAS,OAAO,CAAC,KAAK,KAAK,WAAW,IAAI;;;;;;0CAEnG,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC;oCAAE,WAAU;;;;;;;;;;;;;;;;;kCAKjB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAE,WAAU;;;;;;oCACZ,iBAAiB,kBAAkB;;;;;;;0CAEtC,6LAAC;gCAAE,WAAU;0CACV,iBAAiB,MAAM,QAAQ,aAAa,UAAU;;;;;;0CAEzD,6LAAC;gCAAE,WAAU;0CACV,iBAAiB,MAAM,cAAc,mBAAmB,UAAU;;;;;;4BAEpE,kBAAkB,MAAM,6BACvB,6LAAC;gCAAE,WAAU;;oCAAgD;oCACpD,KAAK,WAAW;;;;;;;4BAG1B,kBAAkB,MAAM,0BACvB,6LAAC;gCAAE,WAAU;;oCAAgD;oCACnC,KAAK,QAAQ,CAAC,UAAU;oCAAC;oCAAE,KAAK,QAAQ,CAAC,SAAS;;;;;;;;;;;;;kCAMhF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;0CACd,iBAAiB,0BAA0B;;;;;;4BAE7C,wBACC,6LAAC;gCAAI,WAAU;0CAAoD;;;;;qDAInE,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;gCAClD,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAG;;;;;;oCAChB,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;4CAA6B,OAAO,QAAQ,OAAO;;gDACjD,QAAQ,UAAU;gDAAC;gDAAE,QAAQ,SAAS;gDAAC;gDAAI,QAAQ,KAAK;gDACxD,QAAQ,UAAU,GAAG,CAAC,EAAE,EAAE,QAAQ,UAAU,CAAC,CAAC,CAAC,GAAG;;2CAFxC,QAAQ,OAAO;;;;;;;;;;;;;;;;;kCAUpC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,6LAAC;gCACC,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;gCAC1C,KAAK,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gCAC3C,WAAU;;;;;;;;;;;;kCAKd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;0CACd,iBAAiB,kCAAkC;;;;;;0CAEtD,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;gCAC1C,aAAa,iBAAiB,6CAA6C;gCAC3E,MAAM;gCACN,WAAU;;;;;;;;;;;;oBAKb,iCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA8D;;;;;;0CAG5E,6LAAC;gCAAI,WAAU;0CACZ,CAAC;oCACA,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK;oCACjD,OAAO,wBACL;;4CACG,QAAQ,UAAU;4CAAC;4CAAE,QAAQ,SAAS;0DACvC,6LAAC;;;;;4CACA,QAAQ,KAAK;4CACb,QAAQ,UAAU,kBACjB;;kEACE,6LAAC;;;;;oDAAK;oDACO,QAAQ,UAAU;;;;uDAInC;gCACN,CAAC;;;;;;4BAEF,yBACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAO;;;;;;oCAAkB;oCAAE,IAAI,KAAK,SAAS,kBAAkB;;;;;;;;;;;;;kCAOxE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,UAAU,CAAC,mBAAmB,CAAC,WAAW;gCAC1C,WAAU;0CAET,0BACC;;sDACE,6LAAC;4CAAI,WAAU;;;;;;wCACd,iBAAiB,mBAAmB;;iEAGvC;;sDACE,6LAAC;4CAAE,WAAW,iBAAiB,6BAA6B;;;;;;wCAC3D,iBAAiB,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStD;GA1VM;;QAW+B,mIAAA,CAAA,WAAQ;;;KAXvC;uCA4VS", "debugId": null}}, {"offset": {"line": 1748, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/tasks/ReassignTaskModal.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Task } from '../../services/task-assignment';\nimport AssignModal from '../common/AssignModal';\n\ninterface ReassignTaskModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  task: Task | null;\n  onReassignSuccess: () => void;\n}\n\nconst ReassignTaskModal: React.FC<ReassignTaskModalProps> = ({\n  isOpen,\n  onClose,\n  task,\n  onReassignSuccess\n}) => {\n  return (\n    <AssignModal\n      isOpen={isOpen}\n      onClose={onClose}\n      itemId={task?.task_id || null}\n      itemType=\"task\"\n      itemTitle={task?.title}\n      mode=\"reassign\"\n      task={task}\n      onReassignSuccess={onReassignSuccess}\n    />\n  );\n};\n\nexport default ReassignTaskModal;\n"], "names": [], "mappings": ";;;;AAIA;AAJA;;;AAaA,MAAM,oBAAsD,CAAC,EAC3D,MAAM,EACN,OAAO,EACP,IAAI,EACJ,iBAAiB,EAClB;IACC,qBACE,6LAAC,8IAAA,CAAA,UAAW;QACV,QAAQ;QACR,SAAS;QACT,QAAQ,MAAM,WAAW;QACzB,UAAS;QACT,WAAW,MAAM;QACjB,MAAK;QACL,MAAM;QACN,mBAAmB;;;;;;AAGzB;KAlBM;uCAoBS", "debugId": null}}, {"offset": {"line": 1785, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/Select.tsx"], "sourcesContent": ["'use client';\n\nimport React, { forwardRef } from 'react';\n\ninterface SelectOption {\n  value: string;\n  label: string;\n  disabled?: boolean;\n}\n\ninterface SelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'onChange'> {\n  label?: string;\n  error?: string;\n  helperText?: string;\n  required?: boolean;\n  options: SelectOption[];\n  placeholder?: string;\n  className?: string;\n  containerClassName?: string;\n  onChange?: (value: string) => void;\n}\n\nconst Select = forwardRef<HTMLSelectElement, SelectProps>(({\n  label,\n  error,\n  helperText,\n  required = false,\n  options = [],\n  placeholder = 'Select an option...',\n  className = '',\n  containerClassName = '',\n  onChange,\n  id,\n  value,\n  ...props\n}, ref) => {\n  const selectId = id || `select-${Math.random().toString(36).substr(2, 9)}`;\n  \n  const baseSelectClasses = `\n    w-full px-3 py-2 border rounded-md shadow-sm \n    focus:outline-none focus:ring-2 focus:ring-offset-2 \n    disabled:opacity-50 disabled:cursor-not-allowed\n    dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600\n    transition-colors duration-200\n    appearance-none bg-white\n    bg-no-repeat bg-right bg-[length:16px_16px]\n    pr-10\n  `;\n  \n  const selectClasses = error\n    ? `${baseSelectClasses} border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600`\n    : `${baseSelectClasses} border-gray-300 focus:border-primary focus:ring-primary dark:focus:border-primary dark:focus:ring-primary`;\n\n  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    if (onChange) {\n      onChange(e.target.value);\n    }\n  };\n\n  return (\n    <div className={`space-y-1 ${containerClassName}`}>\n      {label && (\n        <label \n          htmlFor={selectId}\n          className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\"\n        >\n          {label}\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\n        </label>\n      )}\n      \n      <div className=\"relative\">\n        <select\n          ref={ref}\n          id={selectId}\n          value={value || ''}\n          onChange={handleChange}\n          className={`${selectClasses} ${className}`}\n          {...props}\n        >\n          {placeholder && (\n            <option value=\"\" disabled>\n              {placeholder}\n            </option>\n          )}\n          \n          {options.map((option) => (\n            <option \n              key={option.value} \n              value={option.value}\n              disabled={option.disabled}\n            >\n              {option.label}\n            </option>\n          ))}\n        </select>\n        \n        {/* Custom dropdown arrow */}\n        <div className=\"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\">\n          <i className=\"ri-arrow-down-s-line text-gray-400 dark:text-gray-500\"></i>\n        </div>\n      </div>\n      \n      {error && (\n        <p className=\"text-sm text-red-600 dark:text-red-400 flex items-center\">\n          <i className=\"ri-error-warning-line mr-1\"></i>\n          {error}\n        </p>\n      )}\n      \n      {helperText && !error && (\n        <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n          {helperText}\n        </p>\n      )}\n    </div>\n  );\n});\n\nSelect.displayName = 'Select';\n\nexport default Select;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAsBA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAAkC,CAAC,EACzD,KAAK,EACL,KAAK,EACL,UAAU,EACV,WAAW,KAAK,EAChB,UAAU,EAAE,EACZ,cAAc,qBAAqB,EACnC,YAAY,EAAE,EACd,qBAAqB,EAAE,EACvB,QAAQ,EACR,EAAE,EACF,KAAK,EACL,GAAG,OACJ,EAAE;IACD,MAAM,WAAW,MAAM,CAAC,OAAO,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAE1E,MAAM,oBAAoB,CAAC;;;;;;;;;EAS3B,CAAC;IAED,MAAM,gBAAgB,QAClB,GAAG,kBAAkB,2EAA2E,CAAC,GACjG,GAAG,kBAAkB,0GAA0G,CAAC;IAEpI,MAAM,eAAe,CAAC;QACpB,IAAI,UAAU;YACZ,SAAS,EAAE,MAAM,CAAC,KAAK;QACzB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,UAAU,EAAE,oBAAoB;;YAC9C,uBACC,6LAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBACA,0BAAY,6LAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,KAAK;wBACL,IAAI;wBACJ,OAAO,SAAS;wBAChB,UAAU;wBACV,WAAW,GAAG,cAAc,CAAC,EAAE,WAAW;wBACzC,GAAG,KAAK;;4BAER,6BACC,6LAAC;gCAAO,OAAM;gCAAG,QAAQ;0CACtB;;;;;;4BAIJ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;oCAEC,OAAO,OAAO,KAAK;oCACnB,UAAU,OAAO,QAAQ;8CAExB,OAAO,KAAK;mCAJR,OAAO,KAAK;;;;;;;;;;;kCAUvB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;;YAIhB,uBACC,6LAAC;gBAAE,WAAU;;kCACX,6LAAC;wBAAE,WAAU;;;;;;oBACZ;;;;;;;YAIJ,cAAc,CAAC,uBACd,6LAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;;AAEA,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 1935, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/hooks/useTaskNavigation.ts"], "sourcesContent": ["import { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport toast from 'react-hot-toast';\nimport { apiClient } from '../lib/apiClient';\nimport { taskAssignmentService, Task } from '@/services/task-assignment';\n\ninterface TaskNavigationInfo {\n  task: {\n    task_id: string;\n    title: string;\n    description?: string;\n    entity_type?: string;\n    entity_id?: string;\n    task_type: string;\n    status: string;\n  };\n  canNavigateToEntity: boolean;\n}\n\nexport const useTaskNavigation = () => {\n  const [isLoading, setIsLoading] = useState(false);\n  const router = useRouter();\n\n  /**\n   * Get task navigation information\n   */\n  const getTaskNavigationInfo = async (taskId: string): Promise<Task | null> => {\n    setIsLoading(true);\n\n    try {\n      const task = await taskAssignmentService.getTaskById(taskId);\n\n      console.log('Fetched task:', task);\n\n      if (task) {\n        return task;\n      }\n      return null;\n    } catch (error: any) {\n      console.error('Error fetching task navigation info:', error);\n      toast.error('Failed to load task information');\n      return null;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  /**\n   * Navigate to the appropriate view based on task type and entity\n   */\n  const navigateToTaskView = async (taskId: string) => {\n    const taskInfo = await getTaskNavigationInfo(taskId);\n\n\n    // If task is related to an application, navigate to evaluation\n    if (taskInfo?.entity_type === 'application' && taskInfo.entity_id) {\n      // For application tasks, we need to get the application details to build the evaluation URL\n      try {\n        const appResponse = await apiClient.get(`/applications/${taskInfo.entity_id}`);\n        const application = appResponse.data;\n        const licenseTypeCode = application.license_category?.license_type?.code;\n\n        if (licenseTypeCode) {\n          const evaluationUrl = `/applications/${licenseTypeCode}/evaluate/applicant-info?application_id=${application.application_id}&license_category_id=${application.license_category_id}`;\n          console.log(`Navigating to evaluation: ${evaluationUrl}`);\n          router.push(evaluationUrl);\n          return;\n        }\n      } catch (error) {\n        console.error('Error fetching application details:', error);\n      }\n\n      // Fallback to general task view if application fetch fails\n      router.push(`/admin/tasks/${taskId}`);\n      toast.error('Unable to load application details, showing task details');\n      return;\n    }\n\n    // For other task types\n    switch (taskInfo?.task_type) {\n      case 'complaint':\n        // Navigate to complaint management\n        if (taskInfo?.entity_id) {\n          router.push(`/admin/complaints/${taskInfo?.entity_id}`);\n        } else {\n          router.push(`/admin/tasks/${taskId}`);\n        }\n        break;\n\n      case 'data_breach':\n        // Navigate to data breach management\n        if (taskInfo?.entity_id) {\n          router.push(`/admin/data-breaches/${taskInfo?.entity_id}`);\n        } else {\n          router.push(`/admin/tasks/${taskId}`);\n        }\n        break;\n\n      case 'inspection':\n        // Navigate to inspection management\n        if (taskInfo?.entity_id) {\n          router.push(`/admin/inspections/${taskInfo?.entity_id}`);\n        } else {\n          router.push(`/admin/tasks/${taskId}`);\n        }\n        break;\n\n      case 'document_review':\n        // Navigate to document review\n        if (taskInfo.entity_id) {\n          router.push(`/admin/documents/${taskInfo.entity_id}`);\n        } else {\n          router.push(`/admin/tasks/${taskId}`);\n        }\n        break;\n\n      default:\n        // Default to task details page\n        router.push(`/admin/tasks/${taskId}`);\n        break;\n    }\n  };\n\n  /**\n   * Get the appropriate view URL for a task without navigating\n   */\n  const getTaskViewUrl = async (taskId: string): Promise<string | null> => {\n    const taskInfo = await getTaskNavigationInfo(taskId);\n\n    if (!taskInfo) {\n      return null;\n    }\n\n    const task  = taskInfo;\n\n    // If task is related to an application, try to build evaluation URL\n    if (task.entity_type === 'application' && task.entity_id) {\n      try {\n        const appResponse = await apiClient.get(`/applications/${task.entity_id}`);\n        const application = appResponse.data;\n        const licenseTypeCode = application.license_category?.license_type?.code;\n\n        if (licenseTypeCode) {\n          return `/applications/${licenseTypeCode}/evaluate/applicant-info?application_id=${application.application_id}&license_category_id=${application.license_category_id}`;\n        }\n      } catch (error) {\n        console.error('Error fetching application details for URL:', error);\n      }\n\n      // Fallback to task details if application fetch fails\n      return `/admin/tasks/${taskId}`;\n    }\n\n    // For other task types\n    switch (task.task_type) {\n      case 'complaint':\n        return task.entity_id ? `/admin/complaints/${task.entity_id}` : `/admin/tasks/${taskId}`;\n\n      case 'data_breach':\n        return task.entity_id ? `/admin/data-breaches/${task.entity_id}` : `/admin/tasks/${taskId}`;\n\n      case 'inspection':\n        return task.entity_id ? `/admin/inspections/${task.entity_id}` : `/admin/tasks/${taskId}`;\n\n      case 'document_review':\n        return task.entity_id ? `/admin/documents/${task.entity_id}` : `/admin/tasks/${taskId}`;\n\n      default:\n        return `/admin/tasks/${taskId}`;\n    }\n  };\n\n  /**\n   * Open task view in a new tab\n   */\n  const openTaskViewInNewTab = async (taskId: string) => {\n    const url = await getTaskViewUrl(taskId);\n    \n    if (url) {\n      window.open(url, '_blank');\n    } else {\n      toast.error('Unable to determine task view URL');\n    }\n  };\n\n  /**\n   * Get display information for task navigation\n   */\n  const getTaskDisplayInfo = (taskInfo: TaskNavigationInfo) => {\n    const { task } = taskInfo;\n\n    let title = task.title;\n    let subtitle = '';\n    let icon = '📋';\n\n    // For application tasks, the title usually contains the application number\n    if (task.entity_type === 'application') {\n      icon = '📄';\n      subtitle = 'Application Evaluation';\n    }\n\n    switch (task.task_type) {\n      case 'application':\n      case 'evaluation':\n        icon = '📄';\n        break;\n      case 'complaint':\n        icon = '⚠️';\n        break;\n      case 'data_breach':\n        icon = '🔒';\n        break;\n      case 'inspection':\n        icon = '🔍';\n        break;\n      case 'document_review':\n        icon = '📑';\n        break;\n      default:\n        icon = '📋';\n        break;\n    }\n\n    return {\n      title,\n      subtitle,\n      icon,\n      taskType: task.task_type,\n      status: task.status,\n    };\n  };\n\n  return {\n    navigateToTaskView,\n    getTaskViewUrl,\n    openTaskViewInNewTab,\n    getTaskNavigationInfo,\n    getTaskDisplayInfo,\n    isLoading,\n  };\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAeO,MAAM,oBAAoB;;IAC/B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB;;GAEC,GACD,MAAM,wBAAwB,OAAO;QACnC,aAAa;QAEb,IAAI;YACF,MAAM,OAAO,MAAM,wIAAA,CAAA,wBAAqB,CAAC,WAAW,CAAC;YAErD,QAAQ,GAAG,CAAC,iBAAiB;YAE7B,IAAI,MAAM;gBACR,OAAO;YACT;YACA,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,wCAAwC;YACtD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA;;GAEC,GACD,MAAM,qBAAqB,OAAO;QAChC,MAAM,WAAW,MAAM,sBAAsB;QAG7C,+DAA+D;QAC/D,IAAI,UAAU,gBAAgB,iBAAiB,SAAS,SAAS,EAAE;YACjE,4FAA4F;YAC5F,IAAI;gBACF,MAAM,cAAc,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,SAAS,SAAS,EAAE;gBAC7E,MAAM,cAAc,YAAY,IAAI;gBACpC,MAAM,kBAAkB,YAAY,gBAAgB,EAAE,cAAc;gBAEpE,IAAI,iBAAiB;oBACnB,MAAM,gBAAgB,CAAC,cAAc,EAAE,gBAAgB,wCAAwC,EAAE,YAAY,cAAc,CAAC,qBAAqB,EAAE,YAAY,mBAAmB,EAAE;oBACpL,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,eAAe;oBACxD,OAAO,IAAI,CAAC;oBACZ;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uCAAuC;YACvD;YAEA,2DAA2D;YAC3D,OAAO,IAAI,CAAC,CAAC,aAAa,EAAE,QAAQ;YACpC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,uBAAuB;QACvB,OAAQ,UAAU;YAChB,KAAK;gBACH,mCAAmC;gBACnC,IAAI,UAAU,WAAW;oBACvB,OAAO,IAAI,CAAC,CAAC,kBAAkB,EAAE,UAAU,WAAW;gBACxD,OAAO;oBACL,OAAO,IAAI,CAAC,CAAC,aAAa,EAAE,QAAQ;gBACtC;gBACA;YAEF,KAAK;gBACH,qCAAqC;gBACrC,IAAI,UAAU,WAAW;oBACvB,OAAO,IAAI,CAAC,CAAC,qBAAqB,EAAE,UAAU,WAAW;gBAC3D,OAAO;oBACL,OAAO,IAAI,CAAC,CAAC,aAAa,EAAE,QAAQ;gBACtC;gBACA;YAEF,KAAK;gBACH,oCAAoC;gBACpC,IAAI,UAAU,WAAW;oBACvB,OAAO,IAAI,CAAC,CAAC,mBAAmB,EAAE,UAAU,WAAW;gBACzD,OAAO;oBACL,OAAO,IAAI,CAAC,CAAC,aAAa,EAAE,QAAQ;gBACtC;gBACA;YAEF,KAAK;gBACH,8BAA8B;gBAC9B,IAAI,SAAS,SAAS,EAAE;oBACtB,OAAO,IAAI,CAAC,CAAC,iBAAiB,EAAE,SAAS,SAAS,EAAE;gBACtD,OAAO;oBACL,OAAO,IAAI,CAAC,CAAC,aAAa,EAAE,QAAQ;gBACtC;gBACA;YAEF;gBACE,+BAA+B;gBAC/B,OAAO,IAAI,CAAC,CAAC,aAAa,EAAE,QAAQ;gBACpC;QACJ;IACF;IAEA;;GAEC,GACD,MAAM,iBAAiB,OAAO;QAC5B,MAAM,WAAW,MAAM,sBAAsB;QAE7C,IAAI,CAAC,UAAU;YACb,OAAO;QACT;QAEA,MAAM,OAAQ;QAEd,oEAAoE;QACpE,IAAI,KAAK,WAAW,KAAK,iBAAiB,KAAK,SAAS,EAAE;YACxD,IAAI;gBACF,MAAM,cAAc,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,KAAK,SAAS,EAAE;gBACzE,MAAM,cAAc,YAAY,IAAI;gBACpC,MAAM,kBAAkB,YAAY,gBAAgB,EAAE,cAAc;gBAEpE,IAAI,iBAAiB;oBACnB,OAAO,CAAC,cAAc,EAAE,gBAAgB,wCAAwC,EAAE,YAAY,cAAc,CAAC,qBAAqB,EAAE,YAAY,mBAAmB,EAAE;gBACvK;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+CAA+C;YAC/D;YAEA,sDAAsD;YACtD,OAAO,CAAC,aAAa,EAAE,QAAQ;QACjC;QAEA,uBAAuB;QACvB,OAAQ,KAAK,SAAS;YACpB,KAAK;gBACH,OAAO,KAAK,SAAS,GAAG,CAAC,kBAAkB,EAAE,KAAK,SAAS,EAAE,GAAG,CAAC,aAAa,EAAE,QAAQ;YAE1F,KAAK;gBACH,OAAO,KAAK,SAAS,GAAG,CAAC,qBAAqB,EAAE,KAAK,SAAS,EAAE,GAAG,CAAC,aAAa,EAAE,QAAQ;YAE7F,KAAK;gBACH,OAAO,KAAK,SAAS,GAAG,CAAC,mBAAmB,EAAE,KAAK,SAAS,EAAE,GAAG,CAAC,aAAa,EAAE,QAAQ;YAE3F,KAAK;gBACH,OAAO,KAAK,SAAS,GAAG,CAAC,iBAAiB,EAAE,KAAK,SAAS,EAAE,GAAG,CAAC,aAAa,EAAE,QAAQ;YAEzF;gBACE,OAAO,CAAC,aAAa,EAAE,QAAQ;QACnC;IACF;IAEA;;GAEC,GACD,MAAM,uBAAuB,OAAO;QAClC,MAAM,MAAM,MAAM,eAAe;QAEjC,IAAI,KAAK;YACP,OAAO,IAAI,CAAC,KAAK;QACnB,OAAO;YACL,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA;;GAEC,GACD,MAAM,qBAAqB,CAAC;QAC1B,MAAM,EAAE,IAAI,EAAE,GAAG;QAEjB,IAAI,QAAQ,KAAK,KAAK;QACtB,IAAI,WAAW;QACf,IAAI,OAAO;QAEX,2EAA2E;QAC3E,IAAI,KAAK,WAAW,KAAK,eAAe;YACtC,OAAO;YACP,WAAW;QACb;QAEA,OAAQ,KAAK,SAAS;YACpB,KAAK;YACL,KAAK;gBACH,OAAO;gBACP;YACF,KAAK;gBACH,OAAO;gBACP;YACF,KAAK;gBACH,OAAO;gBACP;YACF,KAAK;gBACH,OAAO;gBACP;YACF,KAAK;gBACH,OAAO;gBACP;YACF;gBACE,OAAO;gBACP;QACJ;QAEA,OAAO;YACL;YACA;YACA;YACA,UAAU,KAAK,SAAS;YACxB,QAAQ,KAAK,MAAM;QACrB;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GA7Na;;QAEI,qIAAA,CAAA,YAAS", "debugId": null}}, {"offset": {"line": 2148, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/tasks/TasksTab.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useCallback } from 'react';\nimport { taskService, Task, TaskFilters, TaskType, TaskStatus, TaskPriority } from '../../services/task-assignment';\nimport { User } from '../../services/userService';\nimport { PaginateQuery } from '../../services/userService';\nimport DataTable from '../common/DataTable';\nimport ConfirmationModal from '../common/ConfirmationModal';\nimport ReassignTaskModal from './ReassignTaskModal';\nimport Select from '../common/Select';\nimport { useTaskNavigation } from '../../hooks/useTaskNavigation';\n\ninterface TasksTabProps {\n  onEditTask: (task: Task) => void;\n  onCreateTask: () => void;\n}\n\nconst TasksTab = ({ onEditTask, onCreateTask }: TasksTabProps) => {\n  const [tasksData, setTasksData] = useState<any>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [showDeleteModal, setShowDeleteModal] = useState(false);\n  const [taskToDelete, setTaskToDelete] = useState<Task | null>(null);\n  const [isDeleting, setIsDeleting] = useState(false);\n  const [showReassignModal, setShowReassignModal] = useState(false);\n  const [taskToReassign, setTaskToReassign] = useState<Task | null>(null);\n  const [filters, setFilters] = useState<TaskFilters>({});\n  const [users, setUsers] = useState<User[]>([]);\n  const [currentQuery, setCurrentQuery] = useState<PaginateQuery>({ page: 1, limit: 10 });\n\n  // Add task navigation hook\n  const { navigateToTaskView, openTaskViewInNewTab, isLoading: isNavigating } = useTaskNavigation();\n\n  const handleFilterChange = (key: keyof TaskFilters, value: string) => {\n    setFilters(prev => ({\n      ...prev,\n      [key]: value === '' ? undefined : value\n    }));\n  };\n\n  const handleDeleteTask = (task: Task) => {\n    setTaskToDelete(task);\n    setShowDeleteModal(true);\n  };\n\n  const handleCancelDelete = () => {\n    setShowDeleteModal(false);\n    setTaskToDelete(null);\n  };\n\n  const handleReassignTask = (task: Task) => {\n    setTaskToReassign(task);\n    setShowReassignModal(true);\n  };\n\n  const handleCancelReassign = () => {\n    setShowReassignModal(false);\n    setTaskToReassign(null);\n  };\n\n  const handleReassignSuccess = () => {\n    setShowReassignModal(false);\n    setTaskToReassign(null);\n    // Reload tasks to show updated assignment\n    loadTasks(currentQuery);\n  };\n\n  const handleConfirmDelete = async () => {\n    if (!taskToDelete) return;\n\n    setIsDeleting(true);\n    try {\n      await taskService.deleteTask(taskToDelete.task_id);\n      setShowDeleteModal(false);\n      setTaskToDelete(null);\n      // Reload tasks\n      loadTasks(currentQuery);\n    } catch (err) {\n      console.error('Error deleting task:', err);\n      setError('Failed to delete task');\n    } finally {\n      setIsDeleting(false);\n    }\n  };\n\n  const loadTasks = useCallback(async (query: PaginateQuery) => {\n    try {\n      setLoading(true);\n      setError(null);\n      setCurrentQuery(query);\n\n      // Combine query with filters\n      const params = {\n        ...query,\n        ...filters\n      };\n\n      const response = await taskService.getTasks(params);\n      setTasksData(response);\n    } catch (err: any) {\n      let errorMessage = 'Failed to load tasks. Please try again.';\n      \n      if (err && typeof err === 'object') {\n        if ('response' in err && err.response && typeof err.response === 'object') {\n          if ('status' in err.response) {\n            const status = err.response.status;\n            if (status === 401) {\n              errorMessage = 'Authentication required. Please log in again.';\n            } else if (status === 403) {\n              errorMessage = 'You do not have permission to view tasks.';\n            } else if (status === 500) {\n              errorMessage = 'Server error. Please try again later.';\n            } else if ('data' in err.response &&\n                      err.response.data &&\n                      typeof err.response.data === 'object' &&\n                      'message' in err.response.data &&\n                      typeof err.response.data.message === 'string') {\n              errorMessage = err.response.data.message;\n            }\n          }\n        } else if ('message' in err && typeof err.message === 'string') {\n          errorMessage = err.message;\n        }\n      }\n\n      setError(errorMessage);\n      setTasksData({\n        data: [],\n        meta: {\n          itemsPerPage: query.limit || 10,\n          totalItems: 0,\n          currentPage: query.page || 1,\n          totalPages: 0,\n          sortBy: [],\n          searchBy: [],\n          search: '',\n          select: [],\n        },\n        links: {\n          current: '',\n        },\n      });\n    } finally {\n      setLoading(false);\n    }\n  }, [filters]);\n\n  useEffect(() => {\n    loadTasks({ page: 1, limit: 10 });\n    loadUsers();\n  }, [loadTasks]);\n\n  // Reload data when filters change\n  useEffect(() => {\n    const cleanFilters: Record<string, string> = {};\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value.trim() !== '') {\n        cleanFilters[key] = value;\n      }\n    });\n\n    loadTasks({\n      page: 1,\n      limit: currentQuery.limit || 10,\n      filter: Object.keys(cleanFilters).length > 0 ? cleanFilters : undefined\n    });\n  }, [filters, currentQuery.limit]);\n\n  const loadUsers = async () => {\n    try {\n      const usersResponse = await taskService.getOfficers();\n      setUsers(usersResponse.data);\n    } catch (err) {\n      console.error('Error loading users:', err);\n      setUsers([]);\n    }\n  };\n\n  const getStatusBadgeClass = (status: TaskStatus) => {\n    switch (status) {\n      case TaskStatus.PENDING:\n        return 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200';\n      case TaskStatus.IN_PROGRESS:\n        return 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200';\n      case TaskStatus.COMPLETED:\n        return 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200';\n      case TaskStatus.CANCELLED:\n        return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200';\n      case TaskStatus.ON_HOLD:\n        return 'bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200';\n      default:\n        return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200';\n    }\n  };\n\n  const getPriorityBadgeClass = (priority: TaskPriority) => {\n    switch (priority) {\n      case TaskPriority.LOW:\n        return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200';\n      case TaskPriority.MEDIUM:\n        return 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200';\n      case TaskPriority.HIGH:\n        return 'bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200';\n      case TaskPriority.URGENT:\n        return 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200';\n      default:\n        return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200';\n    }\n  };\n\n  const taskColumns = [\n    {\n      key: 'task_number',\n      label: 'Task Number',\n      sortable: true,\n      render: (value: string, task: Task) => (\n        <div\n          className=\"text-sm font-medium text-gray-900 dark:text-gray-100 cursor-pointer hover:text-blue-600 dark:hover:text-blue-400 hover:underline\"\n          onClick={() => navigateToTaskView(task.task_id)}\n          title=\"Click to view task\"\n        >\n          {value}\n        </div>\n      ),\n    },\n    {\n      key: 'title',\n      label: 'Title',\n      sortable: true,\n      render: (value: string, task: Task) => (\n        <div>\n          <div\n            className=\"text-sm font-medium text-gray-900 dark:text-gray-100 cursor-pointer hover:text-blue-600 dark:hover:text-blue-400 hover:underline\"\n            onClick={() => navigateToTaskView(task.task_id)}\n            title=\"Click to view task\"\n          >\n            {value}\n          </div>\n          <div className=\"text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs\">\n            {task.description}\n          </div>\n        </div>\n      ),\n    },\n    {\n      key: 'task_type',\n      label: 'Type',\n      render: (value: TaskType) => (\n        <span className=\"text-sm text-gray-900 dark:text-gray-100 capitalize\">\n          {value.replace('_', ' ')}\n        </span>\n      ),\n    },\n    {\n      key: 'status',\n      label: 'Status',\n      sortable: true,\n      render: (value: TaskStatus) => (\n        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(value)}`}>\n          {value.replace('_', ' ').toUpperCase()}\n        </span>\n      ),\n    },\n    {\n      key: 'priority',\n      label: 'Priority',\n      sortable: true,\n      render: (value: TaskPriority) => (\n        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getPriorityBadgeClass(value)}`}>\n          {value.toUpperCase()}\n        </span>\n      ),\n    },\n    {\n      key: 'assignee',\n      label: 'Assigned To',\n      render: (value: any, task: Task) => (\n        <span className=\"text-sm text-gray-900 dark:text-gray-100\">\n          {task.assignee ? `${task.assignee.first_name} ${task.assignee.last_name}` : 'Unassigned'}\n        </span>\n      ),\n    },\n    {\n      key: 'due_date',\n      label: 'Due Date',\n      sortable: true,\n      render: (value: string) => (\n        <span className=\"text-sm text-gray-500 dark:text-gray-400\">\n          {value ? new Date(value).toLocaleDateString() : 'No due date'}\n        </span>\n      ),\n    },\n    {\n      key: 'actions',\n      label: 'Actions',\n      render: (value: unknown, task: Task) => (\n        <div className=\"flex items-center space-x-2\">\n          <button\n            onClick={() => navigateToTaskView(task.task_id)}\n            disabled={isNavigating}\n            className=\"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 p-1 rounded hover:bg-blue-50 dark:hover:bg-blue-900 disabled:opacity-50\"\n            title=\"View task\"\n          >\n            <i className=\"ri-eye-line\"></i>\n          </button>\n          <button\n            onClick={() => openTaskViewInNewTab(task.task_id)}\n            disabled={isNavigating}\n            className=\"text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300 p-1 rounded hover:bg-green-50 dark:hover:bg-green-900 disabled:opacity-50\"\n            title=\"Open in new tab\"\n          >\n            <i className=\"ri-external-link-line\"></i>\n          </button>\n          <button\n            onClick={() => handleReassignTask(task)}\n            className=\"text-purple-600 dark:text-purple-400 hover:text-purple-900 dark:hover:text-purple-300 p-1 rounded hover:bg-purple-50 dark:hover:bg-purple-900\"\n            title=\"Reassign task\"\n          >\n            <i className=\"ri-user-shared-line\"></i>\n          </button>\n          <button\n            onClick={() => handleDeleteTask(task)}\n            className=\"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 p-1 rounded hover:bg-red-50 dark:hover:bg-red-900\"\n            title=\"Delete task\"\n          >\n            <i className=\"ri-delete-bin-line\"></i>\n          </button>\n        </div>\n      ),\n    },\n  ];\n\n  return (\n    <div>\n      {/* Action header */}\n      <div className=\"mb-6 flex justify-end\">\n        <button\n          type=\"button\"\n          onClick={onCreateTask}\n          className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-900\"\n        >\n          <i className=\"ri-add-line w-5 h-5 mr-2\"></i>\n          Add Task\n        </button>\n      </div>\n\n      {error && (\n        <div className=\"mb-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded\">\n          {error}\n        </div>\n      )}\n\n      {/* Filters Section */}\n      <div className=\"mb-6\">\n        <h2 className=\"text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4\">Filters</h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\">\n          {/* Task Type Filter */}\n          <Select\n            label=\"Task Type\"\n            value={filters.task_type || ''}\n            onChange={(value) => handleFilterChange('task_type', value)}\n            options={[\n              { value: '', label: 'All Types' },\n              { value: TaskType.APPLICATION, label: 'Application' },\n              { value: TaskType.COMPLAINT, label: 'Complaint' },\n              { value: TaskType.DATA_BREACH, label: 'Data Breach' },\n              { value: TaskType.EVALUATION, label: 'Evaluation' },\n              { value: TaskType.INSPECTION, label: 'Inspection' },\n              { value: TaskType.DOCUMENT_REVIEW, label: 'Document Review' },\n              { value: TaskType.COMPLIANCE_CHECK, label: 'Compliance Check' },\n              { value: TaskType.FOLLOW_UP, label: 'Follow Up' },\n            ]}\n          />\n\n          {/* Status Filter */}\n          <Select\n            label=\"Status\"\n            value={filters.status || ''}\n            onChange={(value) => handleFilterChange('status', value)}\n            options={[\n              { value: '', label: 'All Statuses' },\n              { value: TaskStatus.PENDING, label: 'Pending' },\n              { value: TaskStatus.IN_PROGRESS, label: 'In Progress' },\n              { value: TaskStatus.COMPLETED, label: 'Completed' },\n              { value: TaskStatus.CANCELLED, label: 'Cancelled' },\n              { value: TaskStatus.ON_HOLD, label: 'On Hold' },\n            ]}\n          />\n\n          {/* Priority Filter */}\n          <Select\n            label=\"Priority\"\n            value={filters.priority || ''}\n            onChange={(value) => handleFilterChange('priority', value)}\n            options={[\n              { value: '', label: 'All Priorities' },\n              { value: TaskPriority.LOW, label: 'Low' },\n              { value: TaskPriority.MEDIUM, label: 'Medium' },\n              { value: TaskPriority.HIGH, label: 'High' },\n              { value: TaskPriority.URGENT, label: 'Urgent' },\n            ]}\n          />\n\n          {/* Assignment Status Filter */}\n          <Select\n            label=\"Assignment Status\"\n            value={filters.assignment_status || ''}\n            onChange={(value) => handleFilterChange('assignment_status', value)}\n            options={[\n              { value: '', label: 'All Tasks' },\n              { value: 'assigned', label: 'Assigned' },\n              { value: 'unassigned', label: 'Unassigned' },\n            ]}\n          />\n\n          {/* Assigned To Filter */}\n          {users.length > 0 && (\n            <Select\n              label=\"Assigned To\"\n              value={filters.assigned_to || ''}\n              onChange={(value) => handleFilterChange('assigned_to', value)}\n              options={[\n                { value: '', label: 'All Users' },\n                { value: 'null', label: 'Unassigned' },\n                ...users.map((user) => ({\n                  value: user.user_id,\n                  label: `${user.first_name} ${user.last_name}`\n                }))\n              ]}\n            />\n          )}\n\n          {/* Created By Filter */}\n          {users.length > 0 && (\n            <Select\n              label=\"Created By\"\n              value={filters.created_by || ''}\n              onChange={(value) => handleFilterChange('created_by', value)}\n              options={[\n                { value: '', label: 'All Users' },\n                ...users.map((user) => ({\n                  value: user.user_id,\n                  label: `${user.first_name} ${user.last_name}`\n                }))\n              ]}\n            />\n          )}\n        </div>\n      </div>\n\n      <DataTable\n        columns={taskColumns}\n        data={tasksData}\n        loading={loading}\n        onQueryChange={loadTasks}\n        searchPlaceholder=\"Search tasks by title, description, or task number...\"\n      />\n\n      {/* Delete Confirmation Modal */}\n      <ConfirmationModal\n        isOpen={showDeleteModal}\n        onClose={handleCancelDelete}\n        onConfirm={handleConfirmDelete}\n        title=\"Delete Task\"\n        message={\n          taskToDelete ? (\n            <div>\n              <p className=\"mb-2\">\n                Are you sure you want to delete task <strong>{taskToDelete.task_number}</strong>?\n              </p>\n              <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n                This action cannot be undone. All data associated with this task will be permanently removed.\n              </p>\n            </div>\n          ) : (\n            'Are you sure you want to delete this task?'\n          )\n        }\n        confirmText=\"Yes, Delete Task\"\n        cancelText=\"Cancel\"\n        confirmVariant=\"danger\"\n        loading={isDeleting}\n      />\n\n      {/* Reassign Task Modal */}\n      <ReassignTaskModal\n        isOpen={showReassignModal}\n        onClose={handleCancelReassign}\n        task={taskToReassign}\n        onReassignSuccess={handleReassignSuccess}\n      />\n    </div>\n  );\n};\n\nexport default TasksTab;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAGA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;AAiBA,MAAM,WAAW,CAAC,EAAE,UAAU,EAAE,YAAY,EAAiB;;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAChD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,CAAC;IACrD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QAAE,MAAM;QAAG,OAAO;IAAG;IAErF,2BAA2B;IAC3B,MAAM,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,WAAW,YAAY,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,oBAAiB,AAAD;IAE9F,MAAM,qBAAqB,CAAC,KAAwB;QAClD,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,CAAC,IAAI,EAAE,UAAU,KAAK,YAAY;YACpC,CAAC;IACH;IAEA,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;QAChB,mBAAmB;IACrB;IAEA,MAAM,qBAAqB;QACzB,mBAAmB;QACnB,gBAAgB;IAClB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,kBAAkB;QAClB,qBAAqB;IACvB;IAEA,MAAM,uBAAuB;QAC3B,qBAAqB;QACrB,kBAAkB;IACpB;IAEA,MAAM,wBAAwB;QAC5B,qBAAqB;QACrB,kBAAkB;QAClB,0CAA0C;QAC1C,UAAU;IACZ;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,cAAc;QAEnB,cAAc;QACd,IAAI;YACF,MAAM,wIAAA,CAAA,cAAW,CAAC,UAAU,CAAC,aAAa,OAAO;YACjD,mBAAmB;YACnB,gBAAgB;YAChB,eAAe;YACf,UAAU;QACZ,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS;QACX,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2CAAE,OAAO;YACnC,IAAI;gBACF,WAAW;gBACX,SAAS;gBACT,gBAAgB;gBAEhB,6BAA6B;gBAC7B,MAAM,SAAS;oBACb,GAAG,KAAK;oBACR,GAAG,OAAO;gBACZ;gBAEA,MAAM,WAAW,MAAM,wIAAA,CAAA,cAAW,CAAC,QAAQ,CAAC;gBAC5C,aAAa;YACf,EAAE,OAAO,KAAU;gBACjB,IAAI,eAAe;gBAEnB,IAAI,OAAO,OAAO,QAAQ,UAAU;oBAClC,IAAI,cAAc,OAAO,IAAI,QAAQ,IAAI,OAAO,IAAI,QAAQ,KAAK,UAAU;wBACzE,IAAI,YAAY,IAAI,QAAQ,EAAE;4BAC5B,MAAM,SAAS,IAAI,QAAQ,CAAC,MAAM;4BAClC,IAAI,WAAW,KAAK;gCAClB,eAAe;4BACjB,OAAO,IAAI,WAAW,KAAK;gCACzB,eAAe;4BACjB,OAAO,IAAI,WAAW,KAAK;gCACzB,eAAe;4BACjB,OAAO,IAAI,UAAU,IAAI,QAAQ,IACvB,IAAI,QAAQ,CAAC,IAAI,IACjB,OAAO,IAAI,QAAQ,CAAC,IAAI,KAAK,YAC7B,aAAa,IAAI,QAAQ,CAAC,IAAI,IAC9B,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,KAAK,UAAU;gCACvD,eAAe,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO;4BAC1C;wBACF;oBACF,OAAO,IAAI,aAAa,OAAO,OAAO,IAAI,OAAO,KAAK,UAAU;wBAC9D,eAAe,IAAI,OAAO;oBAC5B;gBACF;gBAEA,SAAS;gBACT,aAAa;oBACX,MAAM,EAAE;oBACR,MAAM;wBACJ,cAAc,MAAM,KAAK,IAAI;wBAC7B,YAAY;wBACZ,aAAa,MAAM,IAAI,IAAI;wBAC3B,YAAY;wBACZ,QAAQ,EAAE;wBACV,UAAU,EAAE;wBACZ,QAAQ;wBACR,QAAQ,EAAE;oBACZ;oBACA,OAAO;wBACL,SAAS;oBACX;gBACF;YACF,SAAU;gBACR,WAAW;YACb;QACF;0CAAG;QAAC;KAAQ;IAEZ,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,UAAU;gBAAE,MAAM;gBAAG,OAAO;YAAG;YAC/B;QACF;6BAAG;QAAC;KAAU;IAEd,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM,eAAuC,CAAC;YAC9C,OAAO,OAAO,CAAC,SAAS,OAAO;sCAAC,CAAC,CAAC,KAAK,MAAM;oBAC3C,IAAI,UAAU,aAAa,MAAM,IAAI,OAAO,IAAI;wBAC9C,YAAY,CAAC,IAAI,GAAG;oBACtB;gBACF;;YAEA,UAAU;gBACR,MAAM;gBACN,OAAO,aAAa,KAAK,IAAI;gBAC7B,QAAQ,OAAO,IAAI,CAAC,cAAc,MAAM,GAAG,IAAI,eAAe;YAChE;QACF;6BAAG;QAAC;QAAS,aAAa,KAAK;KAAC;IAEhC,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,gBAAgB,MAAM,wIAAA,CAAA,cAAW,CAAC,WAAW;YACnD,SAAS,cAAc,IAAI;QAC7B,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS,EAAE;QACb;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK,wIAAA,CAAA,aAAU,CAAC,OAAO;gBACrB,OAAO;YACT,KAAK,wIAAA,CAAA,aAAU,CAAC,WAAW;gBACzB,OAAO;YACT,KAAK,wIAAA,CAAA,aAAU,CAAC,SAAS;gBACvB,OAAO;YACT,KAAK,wIAAA,CAAA,aAAU,CAAC,SAAS;gBACvB,OAAO;YACT,KAAK,wIAAA,CAAA,aAAU,CAAC,OAAO;gBACrB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK,wIAAA,CAAA,eAAY,CAAC,GAAG;gBACnB,OAAO;YACT,KAAK,wIAAA,CAAA,eAAY,CAAC,MAAM;gBACtB,OAAO;YACT,KAAK,wIAAA,CAAA,eAAY,CAAC,IAAI;gBACpB,OAAO;YACT,KAAK,wIAAA,CAAA,eAAY,CAAC,MAAM;gBACtB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc;QAClB;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,OAAe,qBACtB,6LAAC;oBACC,WAAU;oBACV,SAAS,IAAM,mBAAmB,KAAK,OAAO;oBAC9C,OAAM;8BAEL;;;;;;QAGP;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,OAAe,qBACtB,6LAAC;;sCACC,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,mBAAmB,KAAK,OAAO;4BAC9C,OAAM;sCAEL;;;;;;sCAEH,6LAAC;4BAAI,WAAU;sCACZ,KAAK,WAAW;;;;;;;;;;;;QAIzB;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,sBACP,6LAAC;oBAAK,WAAU;8BACb,MAAM,OAAO,CAAC,KAAK;;;;;;QAG1B;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,sBACP,6LAAC;oBAAK,WAAW,CAAC,8DAA8D,EAAE,oBAAoB,QAAQ;8BAC3G,MAAM,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;QAG1C;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,sBACP,6LAAC;oBAAK,WAAW,CAAC,8DAA8D,EAAE,sBAAsB,QAAQ;8BAC7G,MAAM,WAAW;;;;;;QAGxB;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,OAAY,qBACnB,6LAAC;oBAAK,WAAU;8BACb,KAAK,QAAQ,GAAG,GAAG,KAAK,QAAQ,CAAC,UAAU,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,SAAS,EAAE,GAAG;;;;;;QAGlF;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,sBACP,6LAAC;oBAAK,WAAU;8BACb,QAAQ,IAAI,KAAK,OAAO,kBAAkB,KAAK;;;;;;QAGtD;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,OAAgB,qBACvB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS,IAAM,mBAAmB,KAAK,OAAO;4BAC9C,UAAU;4BACV,WAAU;4BACV,OAAM;sCAEN,cAAA,6LAAC;gCAAE,WAAU;;;;;;;;;;;sCAEf,6LAAC;4BACC,SAAS,IAAM,qBAAqB,KAAK,OAAO;4BAChD,UAAU;4BACV,WAAU;4BACV,OAAM;sCAEN,cAAA,6LAAC;gCAAE,WAAU;;;;;;;;;;;sCAEf,6LAAC;4BACC,SAAS,IAAM,mBAAmB;4BAClC,WAAU;4BACV,OAAM;sCAEN,cAAA,6LAAC;gCAAE,WAAU;;;;;;;;;;;sCAEf,6LAAC;4BACC,SAAS,IAAM,iBAAiB;4BAChC,WAAU;4BACV,OAAM;sCAEN,cAAA,6LAAC;gCAAE,WAAU;;;;;;;;;;;;;;;;;QAIrB;KACD;IAED,qBACE,6LAAC;;0BAEC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,MAAK;oBACL,SAAS;oBACT,WAAU;;sCAEV,6LAAC;4BAAE,WAAU;;;;;;wBAA+B;;;;;;;;;;;;YAK/C,uBACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;0BAKL,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA8D;;;;;;kCAC5E,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,yIAAA,CAAA,UAAM;gCACL,OAAM;gCACN,OAAO,QAAQ,SAAS,IAAI;gCAC5B,UAAU,CAAC,QAAU,mBAAmB,aAAa;gCACrD,SAAS;oCACP;wCAAE,OAAO;wCAAI,OAAO;oCAAY;oCAChC;wCAAE,OAAO,wIAAA,CAAA,WAAQ,CAAC,WAAW;wCAAE,OAAO;oCAAc;oCACpD;wCAAE,OAAO,wIAAA,CAAA,WAAQ,CAAC,SAAS;wCAAE,OAAO;oCAAY;oCAChD;wCAAE,OAAO,wIAAA,CAAA,WAAQ,CAAC,WAAW;wCAAE,OAAO;oCAAc;oCACpD;wCAAE,OAAO,wIAAA,CAAA,WAAQ,CAAC,UAAU;wCAAE,OAAO;oCAAa;oCAClD;wCAAE,OAAO,wIAAA,CAAA,WAAQ,CAAC,UAAU;wCAAE,OAAO;oCAAa;oCAClD;wCAAE,OAAO,wIAAA,CAAA,WAAQ,CAAC,eAAe;wCAAE,OAAO;oCAAkB;oCAC5D;wCAAE,OAAO,wIAAA,CAAA,WAAQ,CAAC,gBAAgB;wCAAE,OAAO;oCAAmB;oCAC9D;wCAAE,OAAO,wIAAA,CAAA,WAAQ,CAAC,SAAS;wCAAE,OAAO;oCAAY;iCACjD;;;;;;0CAIH,6LAAC,yIAAA,CAAA,UAAM;gCACL,OAAM;gCACN,OAAO,QAAQ,MAAM,IAAI;gCACzB,UAAU,CAAC,QAAU,mBAAmB,UAAU;gCAClD,SAAS;oCACP;wCAAE,OAAO;wCAAI,OAAO;oCAAe;oCACnC;wCAAE,OAAO,wIAAA,CAAA,aAAU,CAAC,OAAO;wCAAE,OAAO;oCAAU;oCAC9C;wCAAE,OAAO,wIAAA,CAAA,aAAU,CAAC,WAAW;wCAAE,OAAO;oCAAc;oCACtD;wCAAE,OAAO,wIAAA,CAAA,aAAU,CAAC,SAAS;wCAAE,OAAO;oCAAY;oCAClD;wCAAE,OAAO,wIAAA,CAAA,aAAU,CAAC,SAAS;wCAAE,OAAO;oCAAY;oCAClD;wCAAE,OAAO,wIAAA,CAAA,aAAU,CAAC,OAAO;wCAAE,OAAO;oCAAU;iCAC/C;;;;;;0CAIH,6LAAC,yIAAA,CAAA,UAAM;gCACL,OAAM;gCACN,OAAO,QAAQ,QAAQ,IAAI;gCAC3B,UAAU,CAAC,QAAU,mBAAmB,YAAY;gCACpD,SAAS;oCACP;wCAAE,OAAO;wCAAI,OAAO;oCAAiB;oCACrC;wCAAE,OAAO,wIAAA,CAAA,eAAY,CAAC,GAAG;wCAAE,OAAO;oCAAM;oCACxC;wCAAE,OAAO,wIAAA,CAAA,eAAY,CAAC,MAAM;wCAAE,OAAO;oCAAS;oCAC9C;wCAAE,OAAO,wIAAA,CAAA,eAAY,CAAC,IAAI;wCAAE,OAAO;oCAAO;oCAC1C;wCAAE,OAAO,wIAAA,CAAA,eAAY,CAAC,MAAM;wCAAE,OAAO;oCAAS;iCAC/C;;;;;;0CAIH,6LAAC,yIAAA,CAAA,UAAM;gCACL,OAAM;gCACN,OAAO,QAAQ,iBAAiB,IAAI;gCACpC,UAAU,CAAC,QAAU,mBAAmB,qBAAqB;gCAC7D,SAAS;oCACP;wCAAE,OAAO;wCAAI,OAAO;oCAAY;oCAChC;wCAAE,OAAO;wCAAY,OAAO;oCAAW;oCACvC;wCAAE,OAAO;wCAAc,OAAO;oCAAa;iCAC5C;;;;;;4BAIF,MAAM,MAAM,GAAG,mBACd,6LAAC,yIAAA,CAAA,UAAM;gCACL,OAAM;gCACN,OAAO,QAAQ,WAAW,IAAI;gCAC9B,UAAU,CAAC,QAAU,mBAAmB,eAAe;gCACvD,SAAS;oCACP;wCAAE,OAAO;wCAAI,OAAO;oCAAY;oCAChC;wCAAE,OAAO;wCAAQ,OAAO;oCAAa;uCAClC,MAAM,GAAG,CAAC,CAAC,OAAS,CAAC;4CACtB,OAAO,KAAK,OAAO;4CACnB,OAAO,GAAG,KAAK,UAAU,CAAC,CAAC,EAAE,KAAK,SAAS,EAAE;wCAC/C,CAAC;iCACF;;;;;;4BAKJ,MAAM,MAAM,GAAG,mBACd,6LAAC,yIAAA,CAAA,UAAM;gCACL,OAAM;gCACN,OAAO,QAAQ,UAAU,IAAI;gCAC7B,UAAU,CAAC,QAAU,mBAAmB,cAAc;gCACtD,SAAS;oCACP;wCAAE,OAAO;wCAAI,OAAO;oCAAY;uCAC7B,MAAM,GAAG,CAAC,CAAC,OAAS,CAAC;4CACtB,OAAO,KAAK,OAAO;4CACnB,OAAO,GAAG,KAAK,UAAU,CAAC,CAAC,EAAE,KAAK,SAAS,EAAE;wCAC/C,CAAC;iCACF;;;;;;;;;;;;;;;;;;0BAMT,6LAAC,4IAAA,CAAA,UAAS;gBACR,SAAS;gBACT,MAAM;gBACN,SAAS;gBACT,eAAe;gBACf,mBAAkB;;;;;;0BAIpB,6LAAC,oJAAA,CAAA,UAAiB;gBAChB,QAAQ;gBACR,SAAS;gBACT,WAAW;gBACX,OAAM;gBACN,SACE,6BACE,6LAAC;;sCACC,6LAAC;4BAAE,WAAU;;gCAAO;8CACmB,6LAAC;8CAAQ,aAAa,WAAW;;;;;;gCAAU;;;;;;;sCAElF,6LAAC;4BAAE,WAAU;sCAA2C;;;;;;;;;;;6BAK1D;gBAGJ,aAAY;gBACZ,YAAW;gBACX,gBAAe;gBACf,SAAS;;;;;;0BAIX,6LAAC,mJAAA,CAAA,UAAiB;gBAChB,QAAQ;gBACR,SAAS;gBACT,MAAM;gBACN,mBAAmB;;;;;;;;;;;;AAI3B;GA5dM;;QAc0E,oIAAA,CAAA,oBAAiB;;;KAd3F;uCA8dS", "debugId": null}}, {"offset": {"line": 2883, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/tasks/TaskModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Task, CreateTaskDto, UpdateTaskDto, TaskType, TaskStatus, TaskPriority, taskService } from '../../services/task-assignment';\nimport { User } from '../../services/userService';\nimport Select from '../common/Select';\n\ninterface TaskModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onSave: (task: Task) => void;\n  task?: Task | null;\n}\n\nconst TaskModal = ({ isOpen, onClose, onSave, task }: TaskModalProps) => {\n  const [formData, setFormData] = useState<CreateTaskDto>({\n    task_type: TaskType.APPLICATION,\n    title: '',\n    description: '',\n    priority: TaskPriority.MEDIUM,\n    status: TaskStatus.PENDING,\n    entity_type: '',\n    entity_id: '',\n    due_date: '',\n    assigned_to: '',\n  });\n  const [users, setUsers] = useState<User[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState<Record<string, string>>({});\n\n  useEffect(() => {\n    if (isOpen) {\n      loadUsers();\n      if (task) {\n        // Edit mode - populate form with task data\n        setFormData({\n          task_type: task.task_type,\n          title: task.title,\n          description: task.description,\n          priority: task.priority,\n          status: task.status,\n          entity_type: task.entity_type || '',\n          entity_id: task.entity_id || '',\n          due_date: task.due_date ? task.due_date.split('T')[0] : '', // Format for date input\n          assigned_to: task.assigned_to || '',\n        });\n      } else {\n        // Create mode - reset form\n        setFormData({\n          task_type: TaskType.APPLICATION,\n          title: '',\n          description: '',\n          priority: TaskPriority.MEDIUM,\n          status: TaskStatus.PENDING,\n          entity_type: '',\n          entity_id: '',\n          due_date: '',\n          assigned_to: '',\n        });\n      }\n      setErrors({});\n    }\n  }, [isOpen, task]);\n\n  const loadUsers = async () => {\n    try {\n      const response = await taskService.getOfficers();\n      setUsers(response.data);\n    } catch (error) {\n      console.error('Error loading users:', error);\n      setUsers([]);\n    }\n  };\n\n  const handleInputChange = (field: keyof CreateTaskDto, value: string) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: ''\n      }));\n    }\n  };\n\n  const validateForm = (): boolean => {\n    const newErrors: Record<string, string> = {};\n\n    if (!formData.title.trim()) {\n      newErrors.title = 'Title is required';\n    }\n\n    if (!formData.description.trim()) {\n      newErrors.description = 'Description is required';\n    }\n\n    if (!formData.task_type) {\n      newErrors.task_type = 'Task type is required';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    setLoading(true);\n    try {\n      let savedTask: Task;\n      \n      if (task) {\n        // Update existing task\n        const updateData: UpdateTaskDto = {\n          title: formData.title,\n          description: formData.description,\n          priority: formData.priority,\n          status: formData.status,\n          entity_type: formData.entity_type || undefined,\n          entity_id: formData.entity_id || undefined,\n          due_date: formData.due_date || undefined,\n        };\n        savedTask = await taskService.updateTask(task.task_id, updateData);\n      } else {\n        // Create new task\n        const createData: CreateTaskDto = {\n          ...formData,\n          entity_type: formData.entity_type || undefined,\n          entity_id: formData.entity_id || undefined,\n          due_date: formData.due_date || undefined,\n          assigned_to: formData.assigned_to || undefined,\n        };\n        savedTask = await taskService.createTask(createData);\n      }\n\n      onSave(savedTask);\n      onClose();\n    } catch (error: any) {\n      console.error('Error saving task:', error);\n      if (error.response?.data?.message) {\n        setErrors({ submit: error.response.data.message });\n      } else {\n        setErrors({ submit: 'Failed to save task. Please try again.' });\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleClose = () => {\n    if (!loading) {\n      onClose();\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl mx-4 transform transition-all max-h-[90vh] overflow-hidden\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\n            {task ? 'Edit Task' : 'Create New Task'}\n          </h3>\n          <button\n            type=\"button\"\n            onClick={handleClose}\n            disabled={loading}\n            className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 rounded-md p-1\"\n            aria-label=\"Close modal\"\n          >\n            <i className=\"ri-close-line text-xl\"></i>\n          </button>\n        </div>\n\n        {/* Content */}\n        <div className=\"overflow-y-auto max-h-[calc(90vh-120px)]\">\n          <div className=\"p-6\">\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n        {errors.submit && (\n          <div className=\"bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded\">\n            {errors.submit}\n          </div>\n        )}\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          {/* Title */}\n          <div className=\"md:col-span-2\">\n            <label htmlFor=\"title\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Title *\n            </label>\n            <input\n              type=\"text\"\n              id=\"title\"\n              value={formData.title}\n              onChange={(e) => handleInputChange('title', e.target.value)}\n              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                errors.title ? 'border-red-300 dark:border-red-600' : 'border-gray-300 dark:border-gray-600'\n              }`}\n              placeholder=\"Enter task title\"\n            />\n            {errors.title && (\n              <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.title}</p>\n            )}\n          </div>\n\n          {/* Task Type */}\n          <div>\n            <Select\n              label=\"Task Type *\"\n              value={formData.task_type}\n              onChange={(value) => handleInputChange('task_type', value)}\n              options={[\n                { value: TaskType.APPLICATION, label: 'Application' },\n                { value: TaskType.COMPLAINT, label: 'Complaint' },\n                { value: TaskType.DATA_BREACH, label: 'Data Breach' },\n                { value: TaskType.EVALUATION, label: 'Evaluation' },\n                { value: TaskType.INSPECTION, label: 'Inspection' },\n                { value: TaskType.DOCUMENT_REVIEW, label: 'Document Review' },\n                { value: TaskType.COMPLIANCE_CHECK, label: 'Compliance Check' },\n                { value: TaskType.FOLLOW_UP, label: 'Follow Up' },\n              ]}\n              error={errors.task_type}\n            />\n          </div>\n\n          {/* Priority */}\n          <div>\n            <Select\n              label=\"Priority\"\n              value={formData.priority}\n              onChange={(value) => handleInputChange('priority', value)}\n              options={[\n                { value: TaskPriority.LOW, label: 'Low' },\n                { value: TaskPriority.MEDIUM, label: 'Medium' },\n                { value: TaskPriority.HIGH, label: 'High' },\n                { value: TaskPriority.URGENT, label: 'Urgent' },\n              ]}\n            />\n          </div>\n\n          {/* Status */}\n          <div>\n            <Select\n              label=\"Status\"\n              value={formData.status}\n              onChange={(value) => handleInputChange('status', value)}\n              options={[\n                { value: TaskStatus.PENDING, label: 'Pending' },\n                { value: TaskStatus.IN_PROGRESS, label: 'In Progress' },\n                { value: TaskStatus.COMPLETED, label: 'Completed' },\n                { value: TaskStatus.CANCELLED, label: 'Cancelled' },\n                { value: TaskStatus.ON_HOLD, label: 'On Hold' },\n              ]}\n            />\n          </div>\n\n          {/* Assigned To */}\n          {users.length > 0 && (\n            <div>\n              <Select\n                label=\"Assigned To\"\n                value={formData.assigned_to}\n                onChange={(value) => handleInputChange('assigned_to', value)}\n                options={[\n                  { value: '', label: 'Unassigned' },\n                  ...users.map((user) => ({\n                    value: user.user_id,\n                    label: `${user.first_name} ${user.last_name}`\n                  }))\n                ]}\n              />\n            </div>\n          )}\n\n          {/* Due Date */}\n          <div>\n            <label htmlFor=\"due_date\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Due Date\n            </label>\n            <input\n              type=\"date\"\n              id=\"due_date\"\n              value={formData.due_date}\n              onChange={(e) => handleInputChange('due_date', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white\"\n            />\n          </div>\n\n\n        </div>\n\n        {/* Description */}\n        <div>\n          <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            Description *\n          </label>\n          <textarea\n            id=\"description\"\n            rows={4}\n            value={formData.description}\n            onChange={(e) => handleInputChange('description', e.target.value)}\n            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n              errors.description ? 'border-red-300 dark:border-red-600' : 'border-gray-300 dark:border-gray-600'\n            }`}\n            placeholder=\"Enter task description\"\n          />\n          {errors.description && (\n            <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.description}</p>\n          )}\n        </div>\n\n        {/* Form Actions */}\n        <div className=\"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700\">\n          <button\n            type=\"button\"\n            onClick={handleClose}\n            disabled={loading}\n            className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            Cancel\n          </button>\n          <button\n            type=\"submit\"\n            disabled={loading}\n            className=\"px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed dark:focus:ring-offset-gray-900\"\n          >\n            {loading ? (\n              <div className=\"flex items-center\">\n                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                {task ? 'Updating...' : 'Creating...'}\n              </div>\n            ) : (\n              task ? 'Update Task' : 'Create Task'\n            )}\n          </button>\n            </div>\n            </form>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TaskModal;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;;;AALA;;;;AAcA,MAAM,YAAY,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAkB;;IAClE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QACtD,WAAW,wIAAA,CAAA,WAAQ,CAAC,WAAW;QAC/B,OAAO;QACP,aAAa;QACb,UAAU,wIAAA,CAAA,eAAY,CAAC,MAAM;QAC7B,QAAQ,wIAAA,CAAA,aAAU,CAAC,OAAO;QAC1B,aAAa;QACb,WAAW;QACX,UAAU;QACV,aAAa;IACf;IACA,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,QAAQ;gBACV;gBACA,IAAI,MAAM;oBACR,2CAA2C;oBAC3C,YAAY;wBACV,WAAW,KAAK,SAAS;wBACzB,OAAO,KAAK,KAAK;wBACjB,aAAa,KAAK,WAAW;wBAC7B,UAAU,KAAK,QAAQ;wBACvB,QAAQ,KAAK,MAAM;wBACnB,aAAa,KAAK,WAAW,IAAI;wBACjC,WAAW,KAAK,SAAS,IAAI;wBAC7B,UAAU,KAAK,QAAQ,GAAG,KAAK,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;wBACxD,aAAa,KAAK,WAAW,IAAI;oBACnC;gBACF,OAAO;oBACL,2BAA2B;oBAC3B,YAAY;wBACV,WAAW,wIAAA,CAAA,WAAQ,CAAC,WAAW;wBAC/B,OAAO;wBACP,aAAa;wBACb,UAAU,wIAAA,CAAA,eAAY,CAAC,MAAM;wBAC7B,QAAQ,wIAAA,CAAA,aAAU,CAAC,OAAO;wBAC1B,aAAa;wBACb,WAAW;wBACX,UAAU;wBACV,aAAa;oBACf;gBACF;gBACA,UAAU,CAAC;YACb;QACF;8BAAG;QAAC;QAAQ;KAAK;IAEjB,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,wIAAA,CAAA,cAAW,CAAC,WAAW;YAC9C,SAAS,SAAS,IAAI;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS,EAAE;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC,OAA4B;QACrD,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;QACD,sCAAsC;QACtC,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAA,OAAQ,CAAC;oBACjB,GAAG,IAAI;oBACP,CAAC,MAAM,EAAE;gBACX,CAAC;QACH;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,IAAI;YAChC,UAAU,WAAW,GAAG;QAC1B;QAEA,IAAI,CAAC,SAAS,SAAS,EAAE;YACvB,UAAU,SAAS,GAAG;QACxB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,WAAW;QACX,IAAI;YACF,IAAI;YAEJ,IAAI,MAAM;gBACR,uBAAuB;gBACvB,MAAM,aAA4B;oBAChC,OAAO,SAAS,KAAK;oBACrB,aAAa,SAAS,WAAW;oBACjC,UAAU,SAAS,QAAQ;oBAC3B,QAAQ,SAAS,MAAM;oBACvB,aAAa,SAAS,WAAW,IAAI;oBACrC,WAAW,SAAS,SAAS,IAAI;oBACjC,UAAU,SAAS,QAAQ,IAAI;gBACjC;gBACA,YAAY,MAAM,wIAAA,CAAA,cAAW,CAAC,UAAU,CAAC,KAAK,OAAO,EAAE;YACzD,OAAO;gBACL,kBAAkB;gBAClB,MAAM,aAA4B;oBAChC,GAAG,QAAQ;oBACX,aAAa,SAAS,WAAW,IAAI;oBACrC,WAAW,SAAS,SAAS,IAAI;oBACjC,UAAU,SAAS,QAAQ,IAAI;oBAC/B,aAAa,SAAS,WAAW,IAAI;gBACvC;gBACA,YAAY,MAAM,wIAAA,CAAA,cAAW,CAAC,UAAU,CAAC;YAC3C;YAEA,OAAO;YACP;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,sBAAsB;YACpC,IAAI,MAAM,QAAQ,EAAE,MAAM,SAAS;gBACjC,UAAU;oBAAE,QAAQ,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;gBAAC;YAClD,OAAO;gBACL,UAAU;oBAAE,QAAQ;gBAAyC;YAC/D;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;YACZ;QACF;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,OAAO,cAAc;;;;;;sCAExB,6LAAC;4BACC,MAAK;4BACL,SAAS;4BACT,UAAU;4BACV,WAAU;4BACV,cAAW;sCAEX,cAAA,6LAAC;gCAAE,WAAU;;;;;;;;;;;;;;;;;8BAKjB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;gCAC3C,OAAO,MAAM,kBACZ,6LAAC;oCAAI,WAAU;8CACZ,OAAO,MAAM;;;;;;8CAIlB,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,SAAQ;oDAAQ,WAAU;8DAAkE;;;;;;8DAGnG,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,OAAO,SAAS,KAAK;oDACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;oDAC1D,WAAW,CAAC,2KAA2K,EACrL,OAAO,KAAK,GAAG,uCAAuC,wCACtD;oDACF,aAAY;;;;;;gDAEb,OAAO,KAAK,kBACX,6LAAC;oDAAE,WAAU;8DAA+C,OAAO,KAAK;;;;;;;;;;;;sDAK5E,6LAAC;sDACC,cAAA,6LAAC,yIAAA,CAAA,UAAM;gDACL,OAAM;gDACN,OAAO,SAAS,SAAS;gDACzB,UAAU,CAAC,QAAU,kBAAkB,aAAa;gDACpD,SAAS;oDACP;wDAAE,OAAO,wIAAA,CAAA,WAAQ,CAAC,WAAW;wDAAE,OAAO;oDAAc;oDACpD;wDAAE,OAAO,wIAAA,CAAA,WAAQ,CAAC,SAAS;wDAAE,OAAO;oDAAY;oDAChD;wDAAE,OAAO,wIAAA,CAAA,WAAQ,CAAC,WAAW;wDAAE,OAAO;oDAAc;oDACpD;wDAAE,OAAO,wIAAA,CAAA,WAAQ,CAAC,UAAU;wDAAE,OAAO;oDAAa;oDAClD;wDAAE,OAAO,wIAAA,CAAA,WAAQ,CAAC,UAAU;wDAAE,OAAO;oDAAa;oDAClD;wDAAE,OAAO,wIAAA,CAAA,WAAQ,CAAC,eAAe;wDAAE,OAAO;oDAAkB;oDAC5D;wDAAE,OAAO,wIAAA,CAAA,WAAQ,CAAC,gBAAgB;wDAAE,OAAO;oDAAmB;oDAC9D;wDAAE,OAAO,wIAAA,CAAA,WAAQ,CAAC,SAAS;wDAAE,OAAO;oDAAY;iDACjD;gDACD,OAAO,OAAO,SAAS;;;;;;;;;;;sDAK3B,6LAAC;sDACC,cAAA,6LAAC,yIAAA,CAAA,UAAM;gDACL,OAAM;gDACN,OAAO,SAAS,QAAQ;gDACxB,UAAU,CAAC,QAAU,kBAAkB,YAAY;gDACnD,SAAS;oDACP;wDAAE,OAAO,wIAAA,CAAA,eAAY,CAAC,GAAG;wDAAE,OAAO;oDAAM;oDACxC;wDAAE,OAAO,wIAAA,CAAA,eAAY,CAAC,MAAM;wDAAE,OAAO;oDAAS;oDAC9C;wDAAE,OAAO,wIAAA,CAAA,eAAY,CAAC,IAAI;wDAAE,OAAO;oDAAO;oDAC1C;wDAAE,OAAO,wIAAA,CAAA,eAAY,CAAC,MAAM;wDAAE,OAAO;oDAAS;iDAC/C;;;;;;;;;;;sDAKL,6LAAC;sDACC,cAAA,6LAAC,yIAAA,CAAA,UAAM;gDACL,OAAM;gDACN,OAAO,SAAS,MAAM;gDACtB,UAAU,CAAC,QAAU,kBAAkB,UAAU;gDACjD,SAAS;oDACP;wDAAE,OAAO,wIAAA,CAAA,aAAU,CAAC,OAAO;wDAAE,OAAO;oDAAU;oDAC9C;wDAAE,OAAO,wIAAA,CAAA,aAAU,CAAC,WAAW;wDAAE,OAAO;oDAAc;oDACtD;wDAAE,OAAO,wIAAA,CAAA,aAAU,CAAC,SAAS;wDAAE,OAAO;oDAAY;oDAClD;wDAAE,OAAO,wIAAA,CAAA,aAAU,CAAC,SAAS;wDAAE,OAAO;oDAAY;oDAClD;wDAAE,OAAO,wIAAA,CAAA,aAAU,CAAC,OAAO;wDAAE,OAAO;oDAAU;iDAC/C;;;;;;;;;;;wCAKJ,MAAM,MAAM,GAAG,mBACd,6LAAC;sDACC,cAAA,6LAAC,yIAAA,CAAA,UAAM;gDACL,OAAM;gDACN,OAAO,SAAS,WAAW;gDAC3B,UAAU,CAAC,QAAU,kBAAkB,eAAe;gDACtD,SAAS;oDACP;wDAAE,OAAO;wDAAI,OAAO;oDAAa;uDAC9B,MAAM,GAAG,CAAC,CAAC,OAAS,CAAC;4DACtB,OAAO,KAAK,OAAO;4DACnB,OAAO,GAAG,KAAK,UAAU,CAAC,CAAC,EAAE,KAAK,SAAS,EAAE;wDAC/C,CAAC;iDACF;;;;;;;;;;;sDAMP,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAW,WAAU;8DAAkE;;;;;;8DAGtG,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,OAAO,SAAS,QAAQ;oDACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC7D,WAAU;;;;;;;;;;;;;;;;;;8CAQhB,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAc,WAAU;sDAAkE;;;;;;sDAGzG,6LAAC;4CACC,IAAG;4CACH,MAAM;4CACN,OAAO,SAAS,WAAW;4CAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;4CAChE,WAAW,CAAC,2KAA2K,EACrL,OAAO,WAAW,GAAG,uCAAuC,wCAC5D;4CACF,aAAY;;;;;;wCAEb,OAAO,WAAW,kBACjB,6LAAC;4CAAE,WAAU;sDAA+C,OAAO,WAAW;;;;;;;;;;;;8CAKlF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,UAAU;4CACV,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,MAAK;4CACL,UAAU;4CACV,WAAU;sDAET,wBACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;oDACd,OAAO,gBAAgB;;;;;;uDAG1B,OAAO,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrC;GAjVM;KAAA;uCAmVS", "debugId": null}}, {"offset": {"line": 3441, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/tasks/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useToast } from '../../contexts/ToastContext';\r\nimport { Task } from '@/services/task-assignment';\r\nimport TasksTab from '../../components/tasks/TasksTab';\r\nimport TaskModal from '../../components/tasks/TaskModal';\r\n\r\nexport default function TasksPage() {\r\n  const { showSuccess } = useToast();\r\n  const [isModalOpen, setIsModalOpen] = useState(false);\r\n  const [editingTask, setEditingTask] = useState<Task | null>(null);\r\n\r\n  const handleEditTask = (task: Task) => {\r\n    setEditingTask(task);\r\n    setIsModalOpen(true);\r\n  };\r\n\r\n  const handleCreateTask = () => {\r\n    setEditingTask(null);\r\n    setIsModalOpen(true);\r\n  };\r\n\r\n  const handleModalClose = () => {\r\n    setIsModalOpen(false);\r\n    setEditingTask(null);\r\n  };\r\n\r\n  const handleTaskSaved = () => {\r\n    if (editingTask) {\r\n      showSuccess('Task updated successfully!');\r\n    } else {\r\n      showSuccess('Task created successfully!');\r\n    }\r\n    // The TasksTab component will automatically refresh its data\r\n  };\r\n\r\n  return (\r\n    <div className=\"p-6\">\r\n      {/* Header */}\r\n      <div className=\"mb-6\">\r\n        <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2\">\r\n          Task Management\r\n        </h1>\r\n        <p className=\"text-gray-600 dark:text-gray-400\">\r\n          Manage and track tasks across your organization\r\n        </p>\r\n      </div>\r\n\r\n      {/* Task Content */}\r\n      <TasksTab\r\n        onEditTask={handleEditTask}\r\n        onCreateTask={handleCreateTask}\r\n      />\r\n      {/* Task Modal */}\r\n      <TaskModal\r\n        isOpen={isModalOpen}\r\n        onClose={handleModalClose}\r\n        onSave={handleTaskSaved}\r\n        task={editingTask}\r\n      />\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;;;AANA;;;;;AAQe,SAAS;;IACtB,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAC/B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAE5D,MAAM,iBAAiB,CAAC;QACtB,eAAe;QACf,eAAe;IACjB;IAEA,MAAM,mBAAmB;QACvB,eAAe;QACf,eAAe;IACjB;IAEA,MAAM,mBAAmB;QACvB,eAAe;QACf,eAAe;IACjB;IAEA,MAAM,kBAAkB;QACtB,IAAI,aAAa;YACf,YAAY;QACd,OAAO;YACL,YAAY;QACd;IACA,6DAA6D;IAC/D;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAGzE,6LAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;0BAMlD,6LAAC,0IAAA,CAAA,UAAQ;gBACP,YAAY;gBACZ,cAAc;;;;;;0BAGhB,6LAAC,2IAAA,CAAA,UAAS;gBACR,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,MAAM;;;;;;;;;;;;AAId;GAvDwB;;QACE,mIAAA,CAAA,WAAQ;;;KADV", "debugId": null}}]}
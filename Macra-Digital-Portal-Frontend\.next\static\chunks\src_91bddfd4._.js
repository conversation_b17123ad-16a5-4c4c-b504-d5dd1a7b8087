(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/types/license.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ApplicationStatus": (()=>ApplicationStatus)
});
var ApplicationStatus = /*#__PURE__*/ function(ApplicationStatus) {
    ApplicationStatus["DRAFT"] = "draft";
    ApplicationStatus["SUBMITTED"] = "submitted";
    ApplicationStatus["UNDER_REVIEW"] = "under_review";
    ApplicationStatus["EVALUATION"] = "evaluation";
    ApplicationStatus["APPROVED"] = "approved";
    ApplicationStatus["REJECTED"] = "rejected";
    ApplicationStatus["WITHDRAWN"] = "withdrawn";
    return ApplicationStatus;
}({});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/applicationService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "applicationService": (()=>applicationService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/authUtils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiClient.ts [app-client] (ecmascript)");
;
;
const applicationService = {
    // Get all applications with pagination and filters
    async getApplications (params) {
        const queryParams = new URLSearchParams();
        if (params?.page) queryParams.append('page', params.page.toString());
        if (params?.limit) queryParams.append('limit', params.limit.toString());
        if (params?.search) queryParams.append('search', params.search);
        if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
        if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);
        // Add filters
        if (params?.filters?.licenseTypeId) {
            queryParams.append('filter.license_category.license_type_id', params.filters.licenseTypeId);
        }
        if (params?.filters?.licenseCategoryId) {
            queryParams.append('filter.license_category_id', params.filters.licenseCategoryId);
        }
        if (params?.filters?.status) {
            queryParams.append('filter.status', params.filters.status);
        }
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/applications?${queryParams.toString()}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get applications by license type (through license category)
    async getApplicationsByLicenseType (licenseTypeId, params) {
        const queryParams = new URLSearchParams();
        if (params?.page) queryParams.append('page', params.page.toString());
        if (params?.limit) queryParams.append('limit', params.limit.toString());
        if (params?.search) queryParams.append('search', params.search);
        if (params?.status) queryParams.append('filter.status', params.status);
        // Filter by license type through license category
        queryParams.append('filter.license_category.license_type_id', licenseTypeId);
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/applications?${queryParams.toString()}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get single application by ID
    async getApplication (id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/applications/${id}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get applications by applicant
    async getApplicationsByApplicant (applicantId) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/applications/by-applicant/${applicantId}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get applications by status
    async getApplicationsByStatus (status) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/applications/by-status/${status}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Update application status
    async updateApplicationStatus (id, status) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].put(`/applications/${id}/status?status=${status}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Update application progress
    async updateApplicationProgress (id, currentStep, progressPercentage) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].put(`/applications/${id}/progress?currentStep=${currentStep}&progressPercentage=${progressPercentage}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get application statistics
    async getApplicationStats () {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get('/applications/stats');
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Create new application
    async createApplication (data) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post('/applications', data);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            throw error;
        }
    },
    // Update application with improved error handling
    async updateApplication (id, data) {
        try {
            console.log('Updating application:', id, 'with data:', data);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].put(`/applications/${id}`, data, {
                timeout: 30000
            });
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('Error updating application:', error);
            // Handle specific error cases
            if (error.code === 'ECONNABORTED') {
                throw new Error('Request timeout - please try again');
            }
            if (error.response?.status === 400) {
                const message = error.response?.data?.message || 'Invalid application data';
                console.error('400 Bad Request details:', error.response?.data);
                throw new Error(`Bad Request: ${message}`);
            }
            if (error.response?.status === 429) {
                throw new Error('Too many requests - please wait a moment and try again');
            }
            throw error;
        }
    },
    // Delete application
    async deleteApplication (id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].delete(`/applications/${id}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Create new application with applicant data
    async createApplicationWithApplicant (data) {
        try {
            // Generate proper application number (format: APP-YYYYMMDD-HHMMSS-XXX)
            const now = new Date();
            const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');
            const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');
            const randomStr = Math.random().toString(36).substr(2, 3).toUpperCase();
            const applicationNumber = `APP-${dateStr}-${timeStr}-${randomStr}`;
            // Validate user_id is a proper UUID
            const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
            if (!uuidRegex.test(data.user_id)) {
                throw new Error(`Invalid user_id format: ${data.user_id}. Expected UUID format.`);
            }
            // Create application using user_id as applicant_id
            // In most systems, the authenticated user is the applicant
            const application = await this.createApplication({
                application_number: applicationNumber,
                applicant_id: data.user_id,
                license_category_id: data.license_category_id,
                current_step: 1,
                progress_percentage: 0 // Start with 0% progress
            });
            return application;
        } catch (error) {
            console.error('Error creating application with applicant:', error);
            throw error;
        }
    },
    // Save application section data
    async saveApplicationSection (applicationId, sectionName, sectionData) {
        try {
            // Try to save using form data service, but continue if it fails
            let completedSections = 1; // At least one section is being saved
            // Estimate progress based on section name
            const sectionOrder = [
                'applicantInfo',
                'companyProfile',
                'businessInfo',
                'serviceScope',
                'businessPlan',
                'legalHistory',
                'reviewSubmit'
            ];
            const sectionIndex = sectionOrder.indexOf(sectionName);
            completedSections = sectionIndex >= 0 ? sectionIndex + 1 : 1;
            // Calculate progress based on completed sections (excluding reviewSubmit from total)
            const totalSections = 6; // Total number of form sections (excluding reviewSubmit)
            const progressPercentage = Math.min(Math.round(completedSections / totalSections * 100), 100);
            // Update the application progress
            await this.updateApplication(applicationId, {
                progress_percentage: progressPercentage,
                current_step: completedSections
            });
        } catch (error) {
            throw error;
        }
    },
    // Get application section data
    async getApplicationSection (applicationId, sectionName) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/applications/${applicationId}/sections/${sectionName}`);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            throw error;
        }
    },
    // Submit application for review
    async submitApplication (applicationId) {
        try {
            // Update application status to submitted and set submission date
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].put(`/applications/${applicationId}`, {
                status: 'submitted',
                submitted_at: new Date().toISOString(),
                progress_percentage: 100,
                current_step: 7
            });
            console.log('Application submitted successfully:', (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response));
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('Error submitting application:', error);
            throw error;
        }
    },
    // Get user's applications (filtered by authenticated user)
    async getUserApplications () {
        try {
            // Use dedicated endpoint that explicitly filters by current user
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get('/applications/user-applications');
            const processedResponse = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
            // Handle paginated response structure
            let applications = [];
            if (processedResponse?.data) {
                applications = Array.isArray(processedResponse.data) ? processedResponse.data : [];
            } else if (Array.isArray(processedResponse)) {
                applications = processedResponse;
            } else if (processedResponse) {
                // Single application or other structure
                applications = [
                    processedResponse
                ];
            }
            return applications;
        } catch (error) {
            throw error;
        }
    },
    // Save application as draft
    async saveAsDraft (applicationId, formData) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].put(`/applications/${applicationId}`, {
                form_data: formData,
                status: 'draft'
            });
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('Error saving application as draft:', error);
            throw error;
        }
    },
    // Validate application before submission
    async validateApplication (applicationId) {
        try {
            // Get data from entity-specific APIs for validation
            let formData = {};
            const errors = [];
            const requiredSections = [
                'applicantInfo',
                'companyProfile',
                'businessInfo',
                'legalHistory'
            ];
            // Check if all required sections are completed
            for (const section of requiredSections){
                if (!formData[section] || Object.keys(formData[section]).length === 0) {
                    errors.push(`${section} section is incomplete`);
                }
            }
            return {
                isValid: errors.length === 0,
                errors
            };
        } catch (error) {
            console.error('Error validating application:', error);
            throw error;
        }
    },
    // Update application status
    async updateStatus (applicationId, status) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].patch(`/applications/${applicationId}/status`, {
                status
            });
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('Error updating application status:', error);
            throw error;
        }
    },
    // Assign application to an officer
    async assignApplication (applicationId, assignedTo) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].patch(`/applications/${applicationId}/assign`, {
                assignedTo
            });
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('Error assigning application:', error);
            throw error;
        }
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/licenseCategoryService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "addCodesToCategories": (()=>addCodesToCategories),
    "findCategoryByCode": (()=>findCategoryByCode),
    "findCategoryById": (()=>findCategoryById),
    "generateCategoryCode": (()=>generateCategoryCode),
    "licenseCategoryService": (()=>licenseCategoryService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiClient.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/cacheService.ts [app-client] (ecmascript)");
;
;
const generateCategoryCode = (name)=>{
    return name.toLowerCase().replace(/[^a-z0-9\s]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
    .substring(0, 50); // Limit length
};
const addCodesToCategories = (categories)=>{
    return categories.map((category)=>({
            ...category,
            code: generateCategoryCode(category.name),
            children: category.children ? addCodesToCategories(category.children) : undefined
        }));
};
const findCategoryByCode = (categories, code)=>{
    for (const category of categories){
        if (category.code === code) {
            return category;
        }
        if (category.children) {
            const found = findCategoryByCode(category.children, code);
            if (found) return found;
        }
    }
    return null;
};
const findCategoryById = (categories, id)=>{
    for (const category of categories){
        if (category.license_category_id === id) {
            return category;
        }
        if (category.children) {
            const found = findCategoryById(category.children, id);
            if (found) return found;
        }
    }
    return null;
};
const licenseCategoryService = {
    // Get all license categories with pagination
    async getLicenseCategories (query = {}) {
        const params = new URLSearchParams();
        if (query.page) params.set('page', query.page.toString());
        if (query.limit) params.set('limit', query.limit.toString());
        if (query.search) params.set('search', query.search);
        if (query.sortBy) {
            query.sortBy.forEach((sort)=>params.append('sortBy', sort));
        }
        if (query.searchBy) {
            query.searchBy.forEach((search)=>params.append('searchBy', search));
        }
        if (query.filter) {
            Object.entries(query.filter).forEach(([key, value])=>{
                if (Array.isArray(value)) {
                    value.forEach((v)=>params.append(`filter.${key}`, v));
                } else {
                    params.set(`filter.${key}`, value);
                }
            });
        }
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories?${params.toString()}`);
        return response.data;
    },
    // Get license category by ID with timeout and retry handling
    async getLicenseCategory (id) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/${id}`, {
                timeout: 30000
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching license category:', error);
            if (error.code === 'ECONNABORTED') {
                throw new Error('Request timeout - please try again');
            }
            throw error;
        }
    },
    // Get license categories by license type with improved error handling
    async getLicenseCategoriesByType (licenseTypeId) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/by-license-type/${licenseTypeId}`, {
                timeout: 30000
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching license categories by type:', error);
            if (error.code === 'ECONNABORTED') {
                throw new Error('Request timeout - please try again');
            }
            if (error.response?.status === 429) {
                throw new Error('Too many requests - please wait a moment and try again');
            }
            throw error;
        }
    },
    // Create new license category
    async createLicenseCategory (licenseCategoryData) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post('/license-categories', licenseCategoryData);
        return response.data;
    },
    // Update license category
    async updateLicenseCategory (id, licenseCategoryData) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].put(`/license-categories/${id}`, licenseCategoryData);
        return response.data;
    },
    // Delete license category
    async deleteLicenseCategory (id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].delete(`/license-categories/${id}`);
        return response.data;
    },
    // Get all license categories (simple list for dropdowns) with caching
    async getAllLicenseCategories () {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheService"].getOrSet(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CACHE_KEYS"].LICENSE_CATEGORIES, async ()=>{
            console.log('Fetching license categories from API...');
            // Reduce limit to avoid rate limiting
            const response = await this.getLicenseCategories({
                limit: 100
            });
            return addCodesToCategories(response.data);
        }, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CACHE_TTL"].LONG // Cache for 15 minutes
        );
    },
    // Get hierarchical tree of categories for a license type with caching
    async getCategoryTree (licenseTypeId) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheService"].getOrSet(`category-tree-${licenseTypeId}`, async ()=>{
            console.log(`Fetching category tree for license type: ${licenseTypeId}`);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/license-type/${licenseTypeId}/tree`);
            return addCodesToCategories(response.data);
        }, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CACHE_TTL"].MEDIUM // Cache for 5 minutes
        );
    },
    // Get root categories (no parent) for a license type with caching
    async getRootCategories (licenseTypeId) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheService"].getOrSet(`root-categories-${licenseTypeId}`, async ()=>{
            console.log(`Fetching root categories for license type: ${licenseTypeId}`);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/license-type/${licenseTypeId}/root`);
            return response.data;
        }, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CACHE_TTL"].MEDIUM // Cache for 5 minutes
        );
    },
    // Get license categories for parent selection dropdown
    async getCategoriesForParentSelection (licenseTypeId, excludeId) {
        try {
            const params = excludeId ? {
                excludeId
            } : {};
            console.log('🚀 Fetching parent categories for license type:', licenseTypeId, 'with params:', params);
            // Try the new endpoint first
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/license-type/${licenseTypeId}/for-parent-selection`, {
                    params
                });
                if (response.data && Array.isArray(response.data.data)) {
                    console.log('✅ Valid array response with', response.data.data.length, 'items');
                    return response.data.data;
                } else {
                    console.warn('⚠️ API returned non-array data:', response.data);
                    return [];
                }
            } catch (newEndpointError) {
                console.warn('🔄 New endpoint failed, trying fallback:', newEndpointError);
                // Fallback to existing endpoint
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/by-license-type/${licenseTypeId}`);
                console.log('🔄 Fallback response:', response.data);
                if (response.data && Array.isArray(response.data)) {
                    // Filter out the excluded category if specified
                    let categories = response.data;
                    if (excludeId) {
                        categories = categories.filter((cat)=>cat.license_category_id !== excludeId);
                    }
                    console.log('✅ Fallback successful with', categories.length, 'items');
                    return categories;
                } else {
                    console.warn('⚠️ Fallback also returned non-array data:', response.data);
                    return [];
                }
            }
        } catch (error) {
            return [];
        }
    },
    // Get potential parent categories for a license type
    async getPotentialParents (licenseTypeId, excludeId) {
        const params = excludeId ? {
            excludeId
        } : {};
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/license-type/${licenseTypeId}/potential-parents`, {
            params
        });
        return response.data;
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/task-assignment.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "TaskPriority": (()=>TaskPriority),
    "TaskStatus": (()=>TaskStatus),
    "TaskType": (()=>TaskType),
    "taskAssignmentService": (()=>taskAssignmentService),
    "taskService": (()=>taskService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/authUtils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiClient.ts [app-client] (ecmascript)");
;
;
var TaskType = /*#__PURE__*/ function(TaskType) {
    TaskType["APPLICATION"] = "application";
    TaskType["COMPLAINT"] = "complaint";
    TaskType["DATA_BREACH"] = "data_breach";
    TaskType["EVALUATION"] = "evaluation";
    TaskType["INSPECTION"] = "inspection";
    TaskType["DOCUMENT_REVIEW"] = "document_review";
    TaskType["COMPLIANCE_CHECK"] = "compliance_check";
    TaskType["FOLLOW_UP"] = "follow_up";
    return TaskType;
}({});
var TaskStatus = /*#__PURE__*/ function(TaskStatus) {
    TaskStatus["PENDING"] = "pending";
    TaskStatus["IN_PROGRESS"] = "in_progress";
    TaskStatus["COMPLETED"] = "completed";
    TaskStatus["CANCELLED"] = "cancelled";
    TaskStatus["ON_HOLD"] = "on_hold";
    return TaskStatus;
}({});
var TaskPriority = /*#__PURE__*/ function(TaskPriority) {
    TaskPriority["LOW"] = "low";
    TaskPriority["MEDIUM"] = "medium";
    TaskPriority["HIGH"] = "high";
    TaskPriority["URGENT"] = "urgent";
    return TaskPriority;
}({});
const taskService = {
    // Main task CRUD operations
    getTasks: async (params)=>{
        // Handle assignment_status filter by using different endpoints or filters
        if (params?.assignment_status === 'unassigned') {
            // Use the unassigned tasks endpoint
            return taskService.getUnassignedTasks(params);
        } else if (params?.assignment_status === 'assigned') {
            // Use the assigned tasks endpoint instead of recursive call
            return taskService.getAssignedTasks(params);
        }
        const searchParams = new URLSearchParams();
        if (params?.page) searchParams.append('page', params.page.toString());
        if (params?.limit) searchParams.append('limit', params.limit.toString());
        if (params?.search) searchParams.append('search', params.search);
        if (params?.task_type) searchParams.append('task_type', params.task_type);
        if (params?.status) searchParams.append('status', params.status);
        if (params?.priority) searchParams.append('priority', params.priority);
        if (params?.assigned_to) searchParams.append('assigned_to', params.assigned_to);
        if (params?.created_by) searchParams.append('created_by', params.created_by);
        const queryString = searchParams.toString();
        const url = `/tasks${queryString ? `?${queryString}` : ''}`;
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(url);
        return response.data;
    },
    getUnassignedTasks: async (params)=>{
        const searchParams = new URLSearchParams();
        if (params?.page) searchParams.append('page', params.page.toString());
        if (params?.limit) searchParams.append('limit', params.limit.toString());
        if (params?.search) searchParams.append('search', params.search);
        if (params?.task_type) searchParams.append('task_type', params.task_type);
        if (params?.status) searchParams.append('status', params.status);
        if (params?.priority) searchParams.append('priority', params.priority);
        const queryString = searchParams.toString();
        const url = `/tasks/unassigned${queryString ? `?${queryString}` : ''}`;
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(url);
        return response.data;
    },
    getAssignedTasks: async (params)=>{
        const searchParams = new URLSearchParams();
        if (params?.page) searchParams.append('page', params.page.toString());
        if (params?.limit) searchParams.append('limit', params.limit.toString());
        if (params?.search) searchParams.append('search', params.search);
        if (params?.task_type) searchParams.append('task_type', params.task_type);
        if (params?.status) searchParams.append('status', params.status);
        if (params?.priority) searchParams.append('priority', params.priority);
        const queryString = searchParams.toString();
        const url = `/tasks/assigned${queryString ? `?${queryString}` : ''}`;
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(url);
        return response.data;
    },
    getMyTasks: async (params)=>{
        const searchParams = new URLSearchParams();
        if (params?.page) searchParams.append('page', params.page.toString());
        if (params?.limit) searchParams.append('limit', params.limit.toString());
        if (params?.search) searchParams.append('search', params.search);
        if (params?.task_type) searchParams.append('task_type', params.task_type);
        if (params?.status) searchParams.append('status', params.status);
        if (params?.priority) searchParams.append('priority', params.priority);
        const queryString = searchParams.toString();
        const url = `/tasks/assigned/me${queryString ? `?${queryString}` : ''}`;
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(url);
        return response.data;
    },
    getTaskStats: async ()=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get('/tasks/stats');
        return response.data;
    },
    getTask: async (taskId)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/tasks/${taskId}`);
        return response.data;
    },
    createTask: async (taskData)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post('/tasks', taskData);
        return response.data;
    },
    updateTask: async (taskId, taskData)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].patch(`/tasks/${taskId}`, taskData);
        return response.data;
    },
    assignTask: async (taskId, assignData)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].put(`/tasks/${taskId}/assign`, assignData);
        return response.data;
    },
    reassignTask: async (taskId, assignData)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].put(`/tasks/${taskId}/reassign`, assignData);
        return response.data;
    },
    deleteTask: async (taskId)=>{
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].delete(`/tasks/${taskId}`);
    },
    // Get users for assignment (officers only, exclude customers)
    getUsers: async ()=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get('/users', {
                params: {
                    limit: 100,
                    filter: {
                        exclude_customers: true
                    }
                }
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching users:', error);
            return {
                data: [],
                meta: {
                    itemCount: 0,
                    totalItems: 0,
                    itemsPerPage: 10,
                    totalPages: 0,
                    currentPage: 1
                },
                links: {
                    first: '',
                    previous: '',
                    next: '',
                    last: ''
                }
            };
        }
    },
    // Get officers specifically (non-customer users)
    getOfficers: async ()=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get('/users/list/officers');
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('Error fetching officers:', error);
            // Fallback to regular users endpoint with filtering
            try {
                const fallbackResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get('/users', {
                    params: {
                        limit: 100,
                        filter: {
                            exclude_customers: true
                        }
                    }
                });
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(fallbackResponse);
            } catch (fallbackError) {
                console.error('Error fetching users as fallback:', fallbackError);
                return {
                    data: [],
                    meta: {
                        itemCount: 0,
                        totalItems: 0,
                        itemsPerPage: 10,
                        totalPages: 0,
                        currentPage: 1
                    },
                    links: {
                        first: '',
                        previous: '',
                        next: '',
                        last: ''
                    }
                };
            }
        }
    }
};
const taskAssignmentService = {
    // Generic task management methods
    getUnassignedTasks: taskService.getUnassignedTasks,
    getAssignedTasks: taskService.getAssignedTasks,
    assignTask: taskService.assignTask,
    getTaskById: taskService.getTask,
    // Legacy application-specific methods (for backward compatibility)
    getUnassignedApplications: async (params)=>{
        const searchParams = new URLSearchParams();
        if (params?.page) searchParams.append('page', params.page.toString());
        if (params?.limit) searchParams.append('limit', params.limit.toString());
        if (params?.search) searchParams.append('search', params.search);
        const queryString = searchParams.toString();
        const url = `/applications/unassigned${queryString ? `?${queryString}` : ''}`;
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(url);
        return response.data;
    },
    // Get all applications (including assigned)
    getAllApplications: async (params)=>{
        const searchParams = new URLSearchParams();
        if (params?.page) searchParams.append('page', params.page.toString());
        if (params?.limit) searchParams.append('limit', params.limit.toString());
        if (params?.search) searchParams.append('search', params.search);
        const queryString = searchParams.toString();
        const url = `/applications${queryString ? `?${queryString}` : ''}`;
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(url);
        return response.data;
    },
    // Get applications assigned to current user
    getMyAssignedApplications: async (params)=>{
        const searchParams = new URLSearchParams();
        if (params?.page) searchParams.append('page', params.page.toString());
        if (params?.limit) searchParams.append('limit', params.limit.toString());
        if (params?.search) searchParams.append('search', params.search);
        const queryString = searchParams.toString();
        const url = `/applications/assigned/me${queryString ? `?${queryString}` : ''}`;
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(url);
        return response.data;
    },
    // Get officers for assignment
    getOfficers: async ()=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get('/users');
            return response.data;
        } catch (error) {
            console.error('Error fetching officers:', error);
            return {
                data: []
            };
        }
    },
    // Assign application to officer
    assignApplication: async (applicationId, assignData)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].put(`/applications/${applicationId}/assign`, assignData);
        return response.data;
    },
    // Get application details
    getApplication: async (applicationId)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/applications/${applicationId}`);
        return response.data;
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/common/AssignModal.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ToastContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/ToastContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$task$2d$assignment$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/task-assignment.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/applicationService.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
const AssignModal = ({ isOpen, onClose, itemId, itemType, itemTitle, onAssignSuccess, mode = 'assign', task, onReassignSuccess })=>{
    _s();
    const { showSuccess, showError } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ToastContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"])();
    const [officers, setOfficers] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [filteredOfficers, setFilteredOfficers] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [assigning, setAssigning] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [searchQuery, setSearchQuery] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [selectedOfficer, setSelectedOfficer] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [comment, setComment] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const isReassignMode = mode === 'reassign' && task;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AssignModal.useEffect": ()=>{
            if (isOpen) {
                fetchOfficers();
                setSearchQuery('');
                // For reassignment, pre-select the current assignee
                setSelectedOfficer(isReassignMode ? task?.assigned_to || '' : '');
                setComment('');
            }
        }
    }["AssignModal.useEffect"], [
        isOpen,
        isReassignMode,
        task
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AssignModal.useEffect": ()=>{
            // Filter officers based on search query
            if (searchQuery.trim() === '') {
                setFilteredOfficers(officers);
            } else {
                const filtered = officers.filter({
                    "AssignModal.useEffect.filtered": (officer)=>`${officer.first_name} ${officer.last_name}`.toLowerCase().includes(searchQuery.toLowerCase()) || officer.email.toLowerCase().includes(searchQuery.toLowerCase()) || officer.department?.toLowerCase().includes(searchQuery.toLowerCase())
                }["AssignModal.useEffect.filtered"]);
                setFilteredOfficers(filtered);
            }
        }
    }["AssignModal.useEffect"], [
        officers,
        searchQuery
    ]);
    const fetchOfficers = async ()=>{
        setLoading(true);
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$task$2d$assignment$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskService"].getOfficers();
            const officersData = response.data || [];
            setOfficers(officersData);
            if (officersData.length === 0) {
                console.warn('No officers found for task assignment');
            }
        } catch (error) {
            console.error('Error fetching officers:', error);
            setOfficers([]);
            showError('Failed to load officers. Please try again.');
        } finally{
            setLoading(false);
        }
    };
    const getTaskTypeFromItemType = (itemType)=>{
        switch(itemType){
            case 'application':
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$task$2d$assignment$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TaskType"].EVALUATION;
            case 'data_breach':
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$task$2d$assignment$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TaskType"].DATA_BREACH;
            case 'complaint':
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$task$2d$assignment$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TaskType"].COMPLAINT;
            case 'inspection':
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$task$2d$assignment$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TaskType"].INSPECTION;
            case 'document_review':
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$task$2d$assignment$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TaskType"].DOCUMENT_REVIEW;
            case 'task':
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$task$2d$assignment$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TaskType"].APPLICATION; // Default for existing tasks
            default:
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$task$2d$assignment$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TaskType"].APPLICATION;
        }
    };
    const getTaskTitle = (itemType, itemTitle)=>{
        const baseTitle = itemTitle || 'Untitled';
        switch(itemType){
            case 'application':
                return `Application Evaluation: ${baseTitle}`;
            case 'data_breach':
                return `Data Breach Investigation: ${baseTitle}`;
            case 'complaint':
                return `Complaint Review: ${baseTitle}`;
            case 'inspection':
                return `Inspection Task: ${baseTitle}`;
            case 'document_review':
                return `Document Review: ${baseTitle}`;
            default:
                return `Task: ${baseTitle}`;
        }
    };
    const getTaskDescription = (itemType, itemTitle)=>{
        const baseTitle = itemTitle || 'item';
        switch(itemType){
            case 'application':
                return `Evaluate and review application ${baseTitle} for compliance and approval.`;
            case 'data_breach':
                return `Investigate and assess data breach report ${baseTitle} for regulatory compliance.`;
            case 'complaint':
                return `Review and resolve complaint ${baseTitle} according to regulatory procedures.`;
            case 'inspection':
                return `Conduct inspection for ${baseTitle} to ensure regulatory compliance.`;
            case 'document_review':
                return `Review and validate document ${baseTitle} for accuracy and compliance.`;
            default:
                return `Process and review ${baseTitle}.`;
        }
    };
    const handleAssign = async ()=>{
        if (!selectedOfficer) {
            showError('Please select an officer');
            return;
        }
        if (isReassignMode && !task) {
            showError('Task information is missing');
            return;
        }
        if (!isReassignMode && !itemId) {
            showError('Item ID is missing');
            return;
        }
        setAssigning(true);
        try {
            if (isReassignMode && task) {
                // Reassign existing task
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$task$2d$assignment$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskService"].reassignTask(task.task_id, {
                    assignedTo: selectedOfficer,
                    comment: comment.trim() || undefined
                });
                showSuccess('Task reassigned successfully');
                onReassignSuccess?.();
            } else {
                // Create a new polymorphic task based on item type
                const taskData = {
                    task_type: getTaskTypeFromItemType(itemType),
                    title: getTaskTitle(itemType, itemTitle),
                    description: getTaskDescription(itemType, itemTitle),
                    priority: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$task$2d$assignment$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TaskPriority"].MEDIUM,
                    status: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$task$2d$assignment$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TaskStatus"].PENDING,
                    entity_type: itemType,
                    entity_id: itemId,
                    assigned_to: selectedOfficer,
                    metadata: {
                        comment: comment.trim() || undefined,
                        original_item_title: itemTitle,
                        assignment_context: 'manual_assignment'
                    }
                };
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$task$2d$assignment$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskService"].createTask(taskData);
                // If this is an application, update its status to evaluation
                if (itemType === 'application') {
                    try {
                        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["applicationService"].updateStatus(itemId, 'evaluation');
                    } catch (statusError) {
                        console.warn('Task created but failed to update application status:', statusError);
                    // Don't fail the entire operation if status update fails
                    }
                }
                showSuccess(`Successfully assigned ${itemType.replace('_', ' ')} to officer`);
                onAssignSuccess?.();
            }
            onClose();
        } catch (error) {
            console.error(`Error ${isReassignMode ? 'reassigning' : 'creating assignment'} task:`, error);
            showError(`Failed to ${isReassignMode ? 'reassign' : 'assign'} task`);
        } finally{
            setAssigning(false);
        }
    };
    const getSelectedOfficerDetails = ()=>{
        return officers.find((officer)=>officer.user_id === selectedOfficer);
    };
    if (!isOpen) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-3",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between mb-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-lg font-medium text-gray-900 dark:text-gray-100",
                                children: isReassignMode ? 'Reassign Task' : `Create Task for ${itemType.replace('_', ' ').toUpperCase()}`
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/AssignModal.tsx",
                                lineNumber: 231,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                type: "button",
                                onClick: onClose,
                                className: "text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                    className: "ri-close-line text-xl"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/AssignModal.tsx",
                                    lineNumber: 239,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/AssignModal.tsx",
                                lineNumber: 234,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/AssignModal.tsx",
                        lineNumber: 230,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                className: "text-sm font-medium text-blue-900 dark:text-blue-100 mb-2 flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                        className: "ri-task-line mr-2"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/AssignModal.tsx",
                                        lineNumber: 246,
                                        columnNumber: 15
                                    }, this),
                                    isReassignMode ? 'Task Details:' : 'Task to be Created:'
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/AssignModal.tsx",
                                lineNumber: 245,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-sm text-blue-700 dark:text-blue-300 font-medium",
                                children: isReassignMode ? task?.title : getTaskTitle(itemType, itemTitle)
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/AssignModal.tsx",
                                lineNumber: 249,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-xs text-blue-600 dark:text-blue-400 mt-1",
                                children: isReassignMode ? task?.description : getTaskDescription(itemType, itemTitle)
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/AssignModal.tsx",
                                lineNumber: 252,
                                columnNumber: 13
                            }, this),
                            isReassignMode && task?.task_number && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-xs text-blue-600 dark:text-blue-400 mt-1",
                                children: [
                                    "Task #",
                                    task.task_number
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/AssignModal.tsx",
                                lineNumber: 256,
                                columnNumber: 15
                            }, this),
                            isReassignMode && task?.assignee && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-xs text-blue-600 dark:text-blue-400 mt-1",
                                children: [
                                    "Currently assigned to: ",
                                    task.assignee.first_name,
                                    " ",
                                    task.assignee.last_name
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/AssignModal.tsx",
                                lineNumber: 261,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/AssignModal.tsx",
                        lineNumber: 244,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                children: "Search Officers"
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/AssignModal.tsx",
                                lineNumber: 269,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                type: "text",
                                placeholder: "Search by name, email, or department...",
                                value: searchQuery,
                                onChange: (e)=>setSearchQuery(e.target.value),
                                className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/AssignModal.tsx",
                                lineNumber: 272,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/AssignModal.tsx",
                        lineNumber: 268,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                children: [
                                    "Select Officer (",
                                    filteredOfficers.length,
                                    " found)"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/AssignModal.tsx",
                                lineNumber: 283,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "max-h-64 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md",
                                children: loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "p-4 text-center text-gray-500 dark:text-gray-400",
                                    children: "Loading officers..."
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/AssignModal.tsx",
                                    lineNumber: 288,
                                    columnNumber: 17
                                }, this) : filteredOfficers.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "p-6 text-center text-gray-500 dark:text-gray-400",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                            className: "ri-user-line text-2xl mb-2 block"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/common/AssignModal.tsx",
                                            lineNumber: 293,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "font-medium",
                                            children: "No officers found"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/common/AssignModal.tsx",
                                            lineNumber: 294,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm mt-1",
                                            children: officers.length === 0 ? 'No active officers available for task assignment.' : 'Try adjusting your search criteria.'
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/common/AssignModal.tsx",
                                            lineNumber: 295,
                                            columnNumber: 19
                                        }, this),
                                        officers.length === 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            type: "button",
                                            onClick: fetchOfficers,
                                            className: "mt-3 text-sm text-primary hover:text-primary-dark underline",
                                            children: "Retry loading officers"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/common/AssignModal.tsx",
                                            lineNumber: 302,
                                            columnNumber: 21
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/common/AssignModal.tsx",
                                    lineNumber: 292,
                                    columnNumber: 17
                                }, this) : filteredOfficers.map((officer)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: `p-3 border-b border-gray-200 dark:border-gray-600 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 ${selectedOfficer === officer.user_id ? 'bg-blue-50 dark:bg-blue-900' : ''}`,
                                        onClick: ()=>setSelectedOfficer(officer.user_id),
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                    type: "radio",
                                                    name: "officer",
                                                    value: officer.user_id,
                                                    checked: selectedOfficer === officer.user_id,
                                                    onChange: ()=>setSelectedOfficer(officer.user_id),
                                                    className: "mr-3"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/common/AssignModal.tsx",
                                                    lineNumber: 321,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex-1",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "text-sm font-medium text-gray-900 dark:text-gray-100",
                                                            children: [
                                                                officer.first_name,
                                                                " ",
                                                                officer.last_name
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/common/AssignModal.tsx",
                                                            lineNumber: 330,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "text-sm text-gray-500 dark:text-gray-400",
                                                            children: officer.email
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/common/AssignModal.tsx",
                                                            lineNumber: 333,
                                                            columnNumber: 25
                                                        }, this),
                                                        officer.department && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "text-xs text-gray-400 dark:text-gray-500",
                                                            children: officer.department
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/common/AssignModal.tsx",
                                                            lineNumber: 337,
                                                            columnNumber: 27
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/common/AssignModal.tsx",
                                                    lineNumber: 329,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/common/AssignModal.tsx",
                                            lineNumber: 320,
                                            columnNumber: 21
                                        }, this)
                                    }, officer.user_id, false, {
                                        fileName: "[project]/src/components/common/AssignModal.tsx",
                                        lineNumber: 313,
                                        columnNumber: 19
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/AssignModal.tsx",
                                lineNumber: 286,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/AssignModal.tsx",
                        lineNumber: 282,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                children: isReassignMode ? 'Reassignment Notes (Optional)' : 'Task Notes (Optional)'
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/AssignModal.tsx",
                                lineNumber: 351,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                value: comment,
                                onChange: (e)=>setComment(e.target.value),
                                placeholder: isReassignMode ? 'Add any notes about this reassignment...' : 'Add any specific instructions or context for this task...',
                                rows: 3,
                                className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/AssignModal.tsx",
                                lineNumber: 354,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/AssignModal.tsx",
                        lineNumber: 350,
                        columnNumber: 11
                    }, this),
                    selectedOfficer && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-6 p-4 bg-green-50 dark:bg-green-900 rounded-lg",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                className: "text-sm font-medium text-green-900 dark:text-green-100 mb-2",
                                children: "Selected Officer:"
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/AssignModal.tsx",
                                lineNumber: 366,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-sm text-green-700 dark:text-green-200",
                                children: [
                                    getSelectedOfficerDetails()?.first_name,
                                    " ",
                                    getSelectedOfficerDetails()?.last_name,
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                        fileName: "[project]/src/components/common/AssignModal.tsx",
                                        lineNumber: 371,
                                        columnNumber: 17
                                    }, this),
                                    getSelectedOfficerDetails()?.email,
                                    getSelectedOfficerDetails()?.department && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                                fileName: "[project]/src/components/common/AssignModal.tsx",
                                                lineNumber: 375,
                                                columnNumber: 21
                                            }, this),
                                            "Department: ",
                                            getSelectedOfficerDetails()?.department
                                        ]
                                    }, void 0, true)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/AssignModal.tsx",
                                lineNumber: 369,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/AssignModal.tsx",
                        lineNumber: 365,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex justify-end space-x-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                type: "button",
                                onClick: onClose,
                                className: "px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",
                                children: "Cancel"
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/AssignModal.tsx",
                                lineNumber: 385,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                type: "button",
                                onClick: handleAssign,
                                disabled: !selectedOfficer || assigning,
                                className: "px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center",
                                children: assigning ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/common/AssignModal.tsx",
                                            lineNumber: 400,
                                            columnNumber: 19
                                        }, this),
                                        isReassignMode ? 'Reassigning...' : 'Creating Task...'
                                    ]
                                }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                            className: isReassignMode ? "ri-user-shared-line mr-2" : "ri-task-line mr-2"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/common/AssignModal.tsx",
                                            lineNumber: 405,
                                            columnNumber: 19
                                        }, this),
                                        isReassignMode ? 'Reassign Task' : 'Create Task'
                                    ]
                                }, void 0, true)
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/AssignModal.tsx",
                                lineNumber: 392,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/AssignModal.tsx",
                        lineNumber: 384,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/AssignModal.tsx",
                lineNumber: 228,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/common/AssignModal.tsx",
            lineNumber: 227,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/common/AssignModal.tsx",
        lineNumber: 226,
        columnNumber: 5
    }, this);
};
_s(AssignModal, "fV12nvzoR9GoAKk1yOSsQmL7mhU=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ToastContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"]
    ];
});
_c = AssignModal;
const __TURBOPACK__default__export__ = AssignModal;
var _c;
__turbopack_context__.k.register(_c, "AssignModal");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/common/AssignButton.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$AssignModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/AssignModal.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
const AssignButton = ({ itemId, itemType, itemTitle, onAssignSuccess, className = '', size = 'sm', variant = 'success', disabled = false, children })=>{
    _s();
    const [showAssignModal, setShowAssignModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const handleAssignClick = ()=>{
        setShowAssignModal(true);
    };
    const handleCloseModal = ()=>{
        setShowAssignModal(false);
    };
    const handleAssignSuccess = ()=>{
        setShowAssignModal(false);
        onAssignSuccess?.();
    };
    // Size classes
    const sizeClasses = {
        sm: 'px-3 py-1 text-xs',
        md: 'px-4 py-2 text-sm',
        lg: 'px-6 py-3 text-base'
    };
    // Variant classes
    const variantClasses = {
        primary: 'text-white bg-primary hover:bg-primary-dark focus:ring-primary',
        secondary: 'text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 focus:ring-gray-500',
        success: 'text-white bg-green-600 hover:bg-green-700 focus:ring-green-500'
    };
    // Base classes
    const baseClasses = 'inline-flex items-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed';
    // Combine all classes
    const buttonClasses = `${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${className}`;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                type: "button",
                onClick: handleAssignClick,
                disabled: disabled,
                className: buttonClasses,
                title: `Create task for ${itemType.replace('_', ' ')} assignment`,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                        className: "ri-task-line mr-1"
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/AssignButton.tsx",
                        lineNumber: 73,
                        columnNumber: 9
                    }, this),
                    children || 'Assign'
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/AssignButton.tsx",
                lineNumber: 66,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$AssignModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                isOpen: showAssignModal,
                onClose: handleCloseModal,
                itemId: itemId,
                itemType: itemType,
                itemTitle: itemTitle,
                onAssignSuccess: handleAssignSuccess
            }, void 0, false, {
                fileName: "[project]/src/components/common/AssignButton.tsx",
                lineNumber: 77,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
};
_s(AssignButton, "fGDY/mcdnjsGGT/tJcUmZk8XcXM=");
_c = AssignButton;
const __TURBOPACK__default__export__ = AssignButton;
var _c;
__turbopack_context__.k.register(_c, "AssignButton");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/applicantService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "applicantService": (()=>applicantService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/authUtils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiClient.ts [app-client] (ecmascript)");
;
;
const applicantService = {
    // Create new applicant
    async createApplicant (data) {
        try {
            console.log('Creating applicant with data:', data);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post('/applicants', data);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
            "TURBOPACK unreachable";
        } catch (error) {
            console.error('Error creating applicant:', error);
            console.error('Error details:', error?.response?.data);
            throw error;
        }
    },
    // Get applicant by ID
    async getApplicant (id) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/applicants/${id}`);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('Error fetching applicant:', error);
            throw error;
        }
    },
    // Update applicant
    async updateApplicant (id, data) {
        try {
            console.log('Updating applicant:', id, data);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].put(`/applicants/${id}`, data);
            console.log('Applicant updated successfully:', response.data);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('Error updating applicant:', error);
            throw error;
        }
    },
    // Get applicants by user (if user can have multiple applicants)
    async getApplicantsByUser () {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get('/applicants/by-user');
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('Error fetching user applicants:', error);
            throw error;
        }
    },
    // Delete applicant
    async deleteApplicant (id) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].delete(`/applicants/${id}`);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('Error deleting applicant:', error);
            throw error;
        }
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/userService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "userService": (()=>userService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiClient.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/authUtils.ts [app-client] (ecmascript)");
;
;
const userService = {
    // Get all users with pagination
    async getUsers (query = {}) {
        const params = new URLSearchParams();
        if (query.page) params.set('page', query.page.toString());
        if (query.limit) params.set('limit', query.limit.toString());
        if (query.search) params.set('search', query.search);
        if (query.sortBy) {
            query.sortBy.forEach((sort)=>params.append('sortBy', sort));
        }
        if (query.searchBy) {
            query.searchBy.forEach((search)=>params.append('searchBy', search));
        }
        if (query.filter) {
            Object.entries(query.filter).forEach(([key, value])=>{
                if (Array.isArray(value)) {
                    value.forEach((v)=>params.append(`filter.${key}`, v));
                } else {
                    params.set(`filter.${key}`, value);
                }
            });
        }
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usersApiClient"].get(`?${params.toString()}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get user by ID
    async getUser (id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usersApiClient"].get(`/${id}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get user by ID (alias for consistency)
    async getUserById (id) {
        return this.getUser(id);
    },
    // Get current user profile
    async getProfile () {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usersApiClient"].get('/profile');
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Create new user
    async createUser (userData) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usersApiClient"].post('', userData);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Update user
    async updateUser (id, userData) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usersApiClient"].put(`/${id}`, userData);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Update current user profile
    async updateProfile (userData) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usersApiClient"].put('/profile', userData);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Change password
    async changePassword (passwordData) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usersApiClient"].put('/profile/password', passwordData);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Upload avatar
    async uploadAvatar (file) {
        console.log('userService: uploadAvatar called', {
            fileName: file.name,
            fileSize: file.size,
            fileType: file.type
        });
        const formData = new FormData();
        formData.append('avatar', file);
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usersApiClient"].post('/profile/avatar', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            const axiosError = error;
            console.error('userService: Upload failed', {
                status: axiosError.response?.status,
                statusText: axiosError.response?.statusText,
                data: axiosError.response?.data,
                message: axiosError.message
            });
            throw error;
        }
    },
    // Remove avatar
    async removeAvatar () {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usersApiClient"].delete('/profile/avatar');
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Delete user
    async deleteUser (id) {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usersApiClient"].delete(`/${id}`);
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/license/ApplicationViewModal.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/applicationService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicantService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/applicantService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/userService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ToastContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/ToastContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/license.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Loader$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/Loader.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
const ApplicationViewModal = ({ isOpen, onClose, applicationId, departmentType, licenseTypeCode, onUpdate })=>{
    _s();
    const { showSuccess, showError } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ToastContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const [application, setApplication] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [applicantDetails, setApplicantDetails] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ApplicationViewModal.useEffect": ()=>{
            if (isOpen && applicationId) {
                fetchApplicationDetails();
            }
        }
    }["ApplicationViewModal.useEffect"], [
        isOpen,
        applicationId
    ]);
    const fetchApplicationDetails = async ()=>{
        if (!applicationId) return;
        setLoading(true);
        setError(null);
        try {
            // Fetch application details
            const applicationResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["applicationService"].getApplication(applicationId);
            setApplication(applicationResponse);
            // Fetch applicant details separately if applicant_id exists
            if (applicationResponse.applicant_id) {
                try {
                    const applicantResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicantService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["applicantService"].getApplicant(applicationResponse.applicant_id);
                    setApplicantDetails(applicantResponse);
                    console.log('✅ Applicant details fetched from database:', applicantResponse);
                } catch (applicantError) {
                    console.warn('⚠️ Could not fetch applicant details from database:', applicantError);
                    // Try to get user details if applicant fetch fails
                    try {
                        const userResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["userService"].getUser(applicationResponse.applicant_id);
                        // Convert user data to applicant-like structure
                        setApplicantDetails({
                            applicant_id: userResponse.user_id,
                            name: `${userResponse.first_name} ${userResponse.last_name}`,
                            email: userResponse.email,
                            phone: userResponse.phone,
                            business_registration_number: '',
                            tpin: '',
                            website: '',
                            fax: '',
                            date_incorporation: '',
                            place_incorporation: '',
                            created_at: userResponse.created_at,
                            updated_at: userResponse.updated_at
                        });
                        console.log('✅ User details fetched as fallback:', userResponse);
                    } catch (userError) {
                        console.warn('⚠️ Could not fetch user details either:', userError);
                    }
                }
            }
        } catch (err) {
            console.error('Error fetching application details:', err);
            const errorMessage = err instanceof Error ? err.message : 'Unknown error';
            setError(`Failed to load application details: ${errorMessage}`);
        } finally{
            setLoading(false);
        }
    };
    const handleEvaluate = ()=>{
        if (!application) return;
        // Get the license type code from the application data or use departmentType/licenseTypeCode
        const resolvedLicenseTypeCode = application?.license_category?.license_type?.code || licenseTypeCode || departmentType || 'telecommunications'; // fallback
        // Create URL with both application_id and license_category_id
        const params = new URLSearchParams();
        params.set('application_id', applicationId);
        // Get license category ID from application
        const licenseCategoryId = application?.license_category_id;
        if (licenseCategoryId) {
            params.set('license_category_id', licenseCategoryId);
        }
        // Close modal and navigate to evaluation page
        onClose();
        router.push(`/applications/${resolvedLicenseTypeCode}/evaluate?${params.toString()}`);
    };
    const getStatusColor = (status)=>{
        switch(status){
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].SUBMITTED:
                return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].UNDER_REVIEW:
                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].EVALUATION:
                return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].APPROVED:
                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].REJECTED:
                return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].PENDING_PAYMENT:
                return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].ISSUED:
                return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200';
            default:
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
        }
    };
    const formatDate = (dateString)=>{
        return new Date(dateString).toLocaleString();
    };
    if (!isOpen) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white dark:bg-gray-800",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-3",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between mb-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-lg font-medium text-gray-900 dark:text-gray-100",
                                children: "Application Details"
                            }, void 0, false, {
                                fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                lineNumber: 144,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                type: "button",
                                onClick: onClose,
                                className: "text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",
                                "aria-label": "Close modal",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                    className: "ri-close-line text-xl"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                    lineNumber: 153,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                lineNumber: 147,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                        lineNumber: 143,
                        columnNumber: 11
                    }, this),
                    loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "py-8",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Loader$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            message: "Loading application details..."
                        }, void 0, false, {
                            fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                            lineNumber: 160,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                        lineNumber: 159,
                        columnNumber: 13
                    }, this) : error ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                    className: "ri-error-warning-line text-red-400 mr-2"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                    lineNumber: 165,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-red-700 dark:text-red-200",
                                    children: error
                                }, void 0, false, {
                                    fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                    lineNumber: 166,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                            lineNumber: 164,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                        lineNumber: 163,
                        columnNumber: 13
                    }, this) : application ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "grid grid-cols-1 md:grid-cols-2 gap-6",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                className: "text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",
                                                children: "Application Number"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                lineNumber: 174,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm text-gray-600 dark:text-gray-400",
                                                children: application.application_number
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                lineNumber: 177,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                        lineNumber: 173,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                className: "text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",
                                                children: "Status"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                lineNumber: 182,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(application.status)}`,
                                                children: application.status.replace('_', ' ').toUpperCase()
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                lineNumber: 185,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                        lineNumber: 181,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                className: "text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",
                                                children: "License Category"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                lineNumber: 190,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm text-gray-600 dark:text-gray-400",
                                                children: application.license_category?.name || 'N/A'
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                lineNumber: 193,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                        lineNumber: 189,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                className: "text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",
                                                children: "License Type"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                lineNumber: 198,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm text-gray-600 dark:text-gray-400",
                                                children: application.license_category?.license_type?.name || 'N/A'
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                lineNumber: 201,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                        lineNumber: 197,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                lineNumber: 172,
                                columnNumber: 15
                            }, this),
                            (applicantDetails || application.applicant || application.application_data?.step_2_applicant_details) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                        className: "text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",
                                        children: "Applicant Information"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                        lineNumber: 210,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "bg-gray-50 dark:bg-gray-700 rounded-lg p-4",
                                        children: applicantDetails ? // Use database-fetched applicant details (preferred)
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-sm text-gray-600 dark:text-gray-400",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                            children: "Company/Organization:"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                            lineNumber: 218,
                                                            columnNumber: 27
                                                        }, this),
                                                        " ",
                                                        applicantDetails.name
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                    lineNumber: 217,
                                                    columnNumber: 25
                                                }, this),
                                                applicantDetails.email && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-sm text-gray-600 dark:text-gray-400",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                            children: "Email:"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                            lineNumber: 223,
                                                            columnNumber: 29
                                                        }, this),
                                                        " ",
                                                        applicantDetails.email
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                    lineNumber: 222,
                                                    columnNumber: 27
                                                }, this),
                                                applicantDetails.phone && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-sm text-gray-600 dark:text-gray-400",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                            children: "Phone:"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                            lineNumber: 229,
                                                            columnNumber: 29
                                                        }, this),
                                                        " ",
                                                        applicantDetails.phone
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                    lineNumber: 228,
                                                    columnNumber: 27
                                                }, this),
                                                applicantDetails.business_registration_number && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-sm text-gray-600 dark:text-gray-400",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                            children: "Registration Number:"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                            lineNumber: 235,
                                                            columnNumber: 29
                                                        }, this),
                                                        " ",
                                                        applicantDetails.business_registration_number
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                    lineNumber: 234,
                                                    columnNumber: 27
                                                }, this),
                                                applicantDetails.tpin && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-sm text-gray-600 dark:text-gray-400",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                            children: "TPIN:"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                            lineNumber: 241,
                                                            columnNumber: 29
                                                        }, this),
                                                        " ",
                                                        applicantDetails.tpin
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                    lineNumber: 240,
                                                    columnNumber: 27
                                                }, this),
                                                applicantDetails.website && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-sm text-gray-600 dark:text-gray-400",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                            children: "Website:"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                            lineNumber: 247,
                                                            columnNumber: 29
                                                        }, this),
                                                        " ",
                                                        applicantDetails.website
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                    lineNumber: 246,
                                                    columnNumber: 27
                                                }, this),
                                                applicantDetails.fax && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-sm text-gray-600 dark:text-gray-400",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                            children: "Fax:"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                            lineNumber: 253,
                                                            columnNumber: 29
                                                        }, this),
                                                        " ",
                                                        applicantDetails.fax
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                    lineNumber: 252,
                                                    columnNumber: 27
                                                }, this),
                                                applicantDetails.date_incorporation && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-sm text-gray-600 dark:text-gray-400",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                            children: "Date of Incorporation:"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                            lineNumber: 259,
                                                            columnNumber: 29
                                                        }, this),
                                                        " ",
                                                        new Date(applicantDetails.date_incorporation).toLocaleDateString()
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                    lineNumber: 258,
                                                    columnNumber: 27
                                                }, this),
                                                applicantDetails.place_incorporation && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-sm text-gray-600 dark:text-gray-400",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                            children: "Place of Incorporation:"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                            lineNumber: 265,
                                                            columnNumber: 29
                                                        }, this),
                                                        " ",
                                                        applicantDetails.place_incorporation
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                    lineNumber: 264,
                                                    columnNumber: 27
                                                }, this)
                                            ]
                                        }, void 0, true) : // Fallback to application data (legacy)
                                        (()=>{
                                            const applicantData = application.applicant || {};
                                            const applicationDetails = application.application_data?.step_2_applicant_details || {};
                                            const firstName = applicantData.first_name || applicationDetails.contact_name?.split(' ')[0] || '';
                                            const lastName = applicantData.last_name || applicationDetails.contact_name?.split(' ').slice(1).join(' ') || '';
                                            const fullName = applicantData.name || applicationDetails.contact_name || `${firstName} ${lastName}`.trim();
                                            const companyName = applicantData.company_name || applicantData.organization_name || applicationDetails.company_name || applicationDetails.organization_name || applicantData.name;
                                            const email = applicantData.email || applicationDetails.contact_email || applicationDetails.company_email;
                                            const phone = applicantData.phone || applicationDetails.contact_phone || applicationDetails.company_phone;
                                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "mb-2 p-2 bg-yellow-50 dark:bg-yellow-900 rounded text-xs",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                children: "Note:"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                                lineNumber: 291,
                                                                columnNumber: 31
                                                            }, this),
                                                            " Using fallback data from application. Database applicant details not available."
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                        lineNumber: 290,
                                                        columnNumber: 29
                                                    }, this),
                                                    fullName && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-sm text-gray-600 dark:text-gray-400",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                children: "Contact Person:"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                                lineNumber: 296,
                                                                columnNumber: 33
                                                            }, this),
                                                            " ",
                                                            fullName
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                        lineNumber: 295,
                                                        columnNumber: 31
                                                    }, this),
                                                    companyName && companyName !== fullName && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-sm text-gray-600 dark:text-gray-400",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                children: "Company/Organization:"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                                lineNumber: 302,
                                                                columnNumber: 33
                                                            }, this),
                                                            " ",
                                                            companyName
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                        lineNumber: 301,
                                                        columnNumber: 31
                                                    }, this),
                                                    email && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-sm text-gray-600 dark:text-gray-400",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                children: "Email:"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                                lineNumber: 308,
                                                                columnNumber: 33
                                                            }, this),
                                                            " ",
                                                            email
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                        lineNumber: 307,
                                                        columnNumber: 31
                                                    }, this),
                                                    phone && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-sm text-gray-600 dark:text-gray-400",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                children: "Phone:"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                                lineNumber: 314,
                                                                columnNumber: 33
                                                            }, this),
                                                            " ",
                                                            phone
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                        lineNumber: 313,
                                                        columnNumber: 31
                                                    }, this)
                                                ]
                                            }, void 0, true);
                                        })()
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                        lineNumber: 213,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                lineNumber: 209,
                                columnNumber: 17
                            }, this),
                            application.application_data && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                        className: "text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",
                                        children: "Application Data"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                        lineNumber: 328,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "bg-gray-50 dark:bg-gray-700 rounded-lg p-4",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("pre", {
                                            className: "text-sm text-gray-600 dark:text-gray-400 whitespace-pre-wrap",
                                            children: JSON.stringify(application.application_data, null, 2)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                            lineNumber: 332,
                                            columnNumber: 21
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                        lineNumber: 331,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                lineNumber: 327,
                                columnNumber: 17
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "grid grid-cols-1 md:grid-cols-3 gap-6 pt-4 border-t border-gray-200 dark:border-gray-600",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                className: "text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",
                                                children: "Submitted"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                lineNumber: 342,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm text-gray-600 dark:text-gray-400",
                                                children: formatDate(application.created_at)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                lineNumber: 345,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                        lineNumber: 341,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                className: "text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",
                                                children: "Last Updated"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                lineNumber: 350,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm text-gray-600 dark:text-gray-400",
                                                children: formatDate(application.updated_at)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                lineNumber: 353,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                        lineNumber: 349,
                                        columnNumber: 17
                                    }, this),
                                    application.submitted_at && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                className: "text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",
                                                children: "Submitted At"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                lineNumber: 359,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm text-gray-600 dark:text-gray-400",
                                                children: formatDate(application.submitted_at)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                                lineNumber: 362,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                        lineNumber: 358,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                lineNumber: 340,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                        lineNumber: 170,
                        columnNumber: 13
                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center py-8",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-500 dark:text-gray-400",
                            children: "No application data available"
                        }, void 0, false, {
                            fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                            lineNumber: 371,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                        lineNumber: 370,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                type: "button",
                                onClick: onClose,
                                className: "px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",
                                children: "Close"
                            }, void 0, false, {
                                fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                lineNumber: 377,
                                columnNumber: 13
                            }, this),
                            application && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                type: "button",
                                onClick: handleEvaluate,
                                className: "px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                        className: "ri-clipboard-line mr-2"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                        lineNumber: 390,
                                        columnNumber: 17
                                    }, this),
                                    "Evaluate"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                                lineNumber: 385,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                        lineNumber: 376,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
                lineNumber: 141,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
            lineNumber: 140,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/license/ApplicationViewModal.tsx",
        lineNumber: 139,
        columnNumber: 5
    }, this);
};
_s(ApplicationViewModal, "Z+ov9mT6m0sv7GAgzG79/iTD0ho=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ToastContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
_c = ApplicationViewModal;
const __TURBOPACK__default__export__ = ApplicationViewModal;
var _c;
__turbopack_context__.k.register(_c, "ApplicationViewModal");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/common/Pagination.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
'use client';
;
;
const Pagination = ({ meta, onPageChange, onPageSizeChange, showFirstLast = true, showPageSizeSelector = true, showInfo = true, maxVisiblePages = 7, pageSizeOptions = [
    10,
    25,
    50,
    100
], className = '' })=>{
    const { currentPage, totalPages, totalItems, itemsPerPage } = meta;
    // Don't render if there's only one page or no pages and no additional features
    if (totalPages <= 1 && !showPageSizeSelector && !showInfo) {
        return null;
    }
    // Calculate current items range
    const startItem = totalItems > 0 ? (currentPage - 1) * itemsPerPage + 1 : 0;
    const endItem = Math.min(currentPage * itemsPerPage, totalItems);
    // Calculate which pages to show
    const getVisiblePages = ()=>{
        const pages = [];
        // If total pages is less than or equal to maxVisiblePages, show all
        if (totalPages <= maxVisiblePages) {
            for(let i = 1; i <= totalPages; i++){
                pages.push(i);
            }
            return pages;
        }
        // Always show first page
        pages.push(1);
        // Calculate start and end of the visible range around current page
        const sidePages = Math.floor((maxVisiblePages - 3) / 2); // -3 for first, last, and current
        let startPage = Math.max(2, currentPage - sidePages);
        let endPage = Math.min(totalPages - 1, currentPage + sidePages);
        // Adjust if we're near the beginning
        if (currentPage <= sidePages + 2) {
            startPage = 2;
            endPage = Math.min(totalPages - 1, maxVisiblePages - 1);
        }
        // Adjust if we're near the end
        if (currentPage >= totalPages - sidePages - 1) {
            startPage = Math.max(2, totalPages - maxVisiblePages + 2);
            endPage = totalPages - 1;
        }
        // Add ellipsis after first page if needed
        if (startPage > 2) {
            pages.push('...');
        }
        // Add pages in the visible range
        for(let i = startPage; i <= endPage; i++){
            pages.push(i);
        }
        // Add ellipsis before last page if needed
        if (endPage < totalPages - 1) {
            pages.push('...');
        }
        // Always show last page (if it's not already included)
        if (totalPages > 1) {
            pages.push(totalPages);
        }
        return pages;
    };
    const visiblePages = getVisiblePages();
    const handlePageClick = (page)=>{
        if (typeof page === 'number' && page !== currentPage) {
            onPageChange(page);
        }
    };
    const handlePageSizeChange = (event)=>{
        const newPageSize = parseInt(event.target.value);
        if (onPageSizeChange) {
            onPageSizeChange(newPageSize);
        }
    };
    const handlePrevious = ()=>{
        if (currentPage > 1) {
            onPageChange(currentPage - 1);
        }
    };
    const handleNext = ()=>{
        if (currentPage < totalPages) {
            onPageChange(currentPage + 1);
        }
    };
    const handleFirst = ()=>{
        if (currentPage !== 1) {
            onPageChange(1);
        }
    };
    const handleLast = ()=>{
        if (currentPage !== totalPages) {
            onPageChange(totalPages);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0 px-6 py-3 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 ${className}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4",
                children: [
                    showInfo && totalItems > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-sm text-gray-700 dark:text-gray-300",
                        children: [
                            "Showing ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: startItem
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/Pagination.tsx",
                                lineNumber: 152,
                                columnNumber: 21
                            }, this),
                            " to",
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: endItem
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/Pagination.tsx",
                                lineNumber: 153,
                                columnNumber: 13
                            }, this),
                            " of",
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: totalItems
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/Pagination.tsx",
                                lineNumber: 154,
                                columnNumber: 13
                            }, this),
                            " results"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/Pagination.tsx",
                        lineNumber: 151,
                        columnNumber: 11
                    }, this),
                    showPageSizeSelector && onPageSizeChange && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center space-x-2",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                            value: itemsPerPage,
                            onChange: handlePageSizeChange,
                            className: "px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",
                            children: pageSizeOptions.map((size)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                    value: size,
                                    children: [
                                        size,
                                        " per page"
                                    ]
                                }, size, true, {
                                    fileName: "[project]/src/components/common/Pagination.tsx",
                                    lineNumber: 167,
                                    columnNumber: 17
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/Pagination.tsx",
                            lineNumber: 161,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/Pagination.tsx",
                        lineNumber: 160,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/Pagination.tsx",
                lineNumber: 148,
                columnNumber: 7
            }, this),
            totalPages > 1 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                className: "flex items-center space-x-1",
                "aria-label": "Pagination",
                children: [
                    showFirstLast && currentPage > 1 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: handleFirst,
                        className: "inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300",
                        "aria-label": "Go to first page",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                            className: "ri-skip-back-line"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/Pagination.tsx",
                            lineNumber: 186,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/Pagination.tsx",
                        lineNumber: 181,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: handlePrevious,
                        disabled: currentPage === 1,
                        className: `inline-flex items-center px-2 py-2 text-sm font-medium border ${currentPage === 1 ? 'text-gray-300 bg-gray-100 border-gray-300 cursor-not-allowed dark:bg-gray-700 dark:border-gray-600 dark:text-gray-500' : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300'} ${showFirstLast && currentPage > 1 ? '' : 'rounded-l-md'}`,
                        "aria-label": "Go to previous page",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                            className: "ri-arrow-left-s-line"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/Pagination.tsx",
                            lineNumber: 201,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/Pagination.tsx",
                        lineNumber: 191,
                        columnNumber: 11
                    }, this),
                    visiblePages.map((page, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Fragment, {
                            children: page === '...' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400",
                                children: "..."
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/Pagination.tsx",
                                lineNumber: 208,
                                columnNumber: 17
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>handlePageClick(page),
                                className: `inline-flex items-center px-4 py-2 text-sm font-medium border ${page === currentPage ? 'text-white bg-red-600 border-red-600 hover:bg-red-700' : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300'}`,
                                "aria-label": `Go to page ${page}`,
                                "aria-current": page === currentPage ? 'page' : undefined,
                                children: page
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/Pagination.tsx",
                                lineNumber: 212,
                                columnNumber: 17
                            }, this)
                        }, index, false, {
                            fileName: "[project]/src/components/common/Pagination.tsx",
                            lineNumber: 206,
                            columnNumber: 13
                        }, this)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: handleNext,
                        disabled: currentPage === totalPages,
                        className: `inline-flex items-center px-2 py-2 text-sm font-medium border ${currentPage === totalPages ? 'text-gray-300 bg-gray-100 border-gray-300 cursor-not-allowed dark:bg-gray-700 dark:border-gray-600 dark:text-gray-500' : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300'} ${showFirstLast && currentPage < totalPages ? '' : 'rounded-r-md'}`,
                        "aria-label": "Go to next page",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                            className: "ri-arrow-right-s-line"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/Pagination.tsx",
                            lineNumber: 239,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/Pagination.tsx",
                        lineNumber: 229,
                        columnNumber: 11
                    }, this),
                    showFirstLast && currentPage < totalPages && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: handleLast,
                        className: "inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300",
                        "aria-label": "Go to last page",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                            className: "ri-skip-forward-line"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/Pagination.tsx",
                            lineNumber: 249,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/Pagination.tsx",
                        lineNumber: 244,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/Pagination.tsx",
                lineNumber: 178,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/Pagination.tsx",
        lineNumber: 146,
        columnNumber: 5
    }, this);
};
_c = Pagination;
const __TURBOPACK__default__export__ = Pagination;
var _c;
__turbopack_context__.k.register(_c, "Pagination");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/common/DataTable.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>DataTable)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Pagination$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/Pagination.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
function DataTable({ columns, data, loading = false, onQueryChange, searchPlaceholder = "Search...", className = "" }) {
    _s();
    const [query, setQuery] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        page: 1,
        limit: 10,
        search: '',
        sortBy: []
    });
    const [searchInput, setSearchInput] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    // Debounced search effect
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DataTable.useEffect": ()=>{
            const timeoutId = setTimeout({
                "DataTable.useEffect.timeoutId": ()=>{
                    if (searchInput !== query.search) {
                        handleSearch(searchInput);
                    }
                }
            }["DataTable.useEffect.timeoutId"], 300);
            return ({
                "DataTable.useEffect": ()=>clearTimeout(timeoutId)
            })["DataTable.useEffect"];
        }
    }["DataTable.useEffect"], [
        searchInput
    ]);
    const handleSearch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "DataTable.useCallback[handleSearch]": (search)=>{
            try {
                const newQuery = {
                    ...query,
                    search,
                    page: 1
                };
                setQuery(newQuery);
                onQueryChange(newQuery);
            } catch (error) {
                console.error('Error handling search:', error);
            }
        }
    }["DataTable.useCallback[handleSearch]"], [
        query,
        onQueryChange
    ]);
    const handleSort = (columnKey)=>{
        const currentSort = query.sortBy?.find((sort)=>sort.startsWith(columnKey));
        let newSortBy = [];
        if (!currentSort) {
            newSortBy = [
                `${columnKey}:ASC`
            ];
        } else if (currentSort.endsWith(':ASC')) {
            newSortBy = [
                `${columnKey}:DESC`
            ];
        } else {
            newSortBy = [];
        }
        const newQuery = {
            ...query,
            sortBy: newSortBy,
            page: 1
        };
        setQuery(newQuery);
        onQueryChange(newQuery);
    };
    const handlePageChange = (page)=>{
        const newQuery = {
            ...query,
            page
        };
        setQuery(newQuery);
        onQueryChange(newQuery);
    };
    const handleLimitChange = (limit)=>{
        const newQuery = {
            ...query,
            limit,
            page: 1
        };
        setQuery(newQuery);
        onQueryChange(newQuery);
    };
    const getSortDirection = (columnKey)=>{
        const currentSort = query.sortBy?.find((sort)=>sort.startsWith(columnKey));
        if (!currentSort) return null;
        return currentSort.endsWith(':ASC') ? 'asc' : 'desc';
    };
    // Handle null data case early
    if (!data) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden ${className}`,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "p-6 text-center text-gray-500 dark:text-gray-400",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center justify-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "animate-spin rounded-full h-6 w-6 border-b-2 border-red-600"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/DataTable.tsx",
                            lineNumber: 103,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "ml-2",
                            children: "Loading..."
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/DataTable.tsx",
                            lineNumber: 104,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/DataTable.tsx",
                    lineNumber: 102,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/common/DataTable.tsx",
                lineNumber: 101,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/common/DataTable.tsx",
            lineNumber: 100,
            columnNumber: 7
        }, this);
    }
    const handlePageSizeChange = (newPageSize)=>{
        const newQuery = {
            ...query,
            limit: newPageSize,
            page: 1
        };
        setQuery(newQuery);
        onQueryChange(newQuery);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden ${className}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "p-6 border-b border-gray-200 dark:border-gray-700",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                className: "ri-search-line text-gray-400 dark:text-gray-500"
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/DataTable.tsx",
                                lineNumber: 123,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/DataTable.tsx",
                            lineNumber: 122,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                            type: "text",
                            placeholder: searchPlaceholder,
                            value: searchInput,
                            onChange: (e)=>setSearchInput(e.target.value),
                            className: "block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-gray-900 dark:text-gray-100"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/DataTable.tsx",
                            lineNumber: 125,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/DataTable.tsx",
                    lineNumber: 121,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/common/DataTable.tsx",
                lineNumber: 120,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "overflow-x-auto",
                style: {
                    minHeight: '500px'
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("table", {
                    className: "min-w-full divide-y divide-gray-200 dark:divide-gray-700",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("thead", {
                            className: "bg-gray-50 dark:bg-gray-900",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                children: columns.map((column)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                        className: `px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${column.sortable ? 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800' : ''} ${column.className || ''}`,
                                        onClick: ()=>column.sortable && handleSort(String(column.key)),
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center space-x-1",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: column.label
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/common/DataTable.tsx",
                                                    lineNumber: 149,
                                                    columnNumber: 21
                                                }, this),
                                                column.sortable && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex flex-col",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                            className: `ri-arrow-up-s-line text-xs ${getSortDirection(String(column.key)) === 'asc' ? 'text-red-600' : 'text-gray-400'}`
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/common/DataTable.tsx",
                                                            lineNumber: 152,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                            className: `ri-arrow-down-s-line text-xs -mt-1 ${getSortDirection(String(column.key)) === 'desc' ? 'text-red-600' : 'text-gray-400'}`
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/common/DataTable.tsx",
                                                            lineNumber: 155,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/common/DataTable.tsx",
                                                    lineNumber: 151,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/common/DataTable.tsx",
                                            lineNumber: 148,
                                            columnNumber: 19
                                        }, this)
                                    }, String(column.key), false, {
                                        fileName: "[project]/src/components/common/DataTable.tsx",
                                        lineNumber: 141,
                                        columnNumber: 17
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/DataTable.tsx",
                                lineNumber: 139,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/DataTable.tsx",
                            lineNumber: 138,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
                            className: "bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",
                            children: loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    colSpan: columns.length,
                                    className: "px-6 py-4 text-center text-gray-500 dark:text-gray-400",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center justify-center",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "animate-spin rounded-full h-6 w-6 border-b-2 border-red-600"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/common/DataTable.tsx",
                                                lineNumber: 170,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "ml-2",
                                                children: "Loading..."
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/common/DataTable.tsx",
                                                lineNumber: 171,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/DataTable.tsx",
                                        lineNumber: 169,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/DataTable.tsx",
                                    lineNumber: 168,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/DataTable.tsx",
                                lineNumber: 167,
                                columnNumber: 15
                            }, this) : !data?.data || data.data.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    colSpan: columns.length,
                                    className: "px-6 py-4 text-center text-gray-500 dark:text-gray-400",
                                    children: "No data found"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/DataTable.tsx",
                                    lineNumber: 177,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/DataTable.tsx",
                                lineNumber: 176,
                                columnNumber: 15
                            }, this) : data.data.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                    className: "hover:bg-gray-50 dark:hover:bg-gray-700",
                                    children: columns.map((column)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                            className: "px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100",
                                            children: column.render ? column.render(item[column.key], item) : String(item[column.key] || '')
                                        }, String(column.key), false, {
                                            fileName: "[project]/src/components/common/DataTable.tsx",
                                            lineNumber: 185,
                                            columnNumber: 21
                                        }, this))
                                }, index, false, {
                                    fileName: "[project]/src/components/common/DataTable.tsx",
                                    lineNumber: 183,
                                    columnNumber: 17
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/DataTable.tsx",
                            lineNumber: 165,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/DataTable.tsx",
                    lineNumber: 137,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/common/DataTable.tsx",
                lineNumber: 136,
                columnNumber: 7
            }, this),
            data?.meta && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Pagination$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                meta: data.meta,
                onPageChange: handlePageChange,
                onPageSizeChange: handlePageSizeChange,
                showFirstLast: true,
                showPageSizeSelector: true,
                showInfo: true,
                maxVisiblePages: 7,
                pageSizeOptions: [
                    10,
                    25,
                    50,
                    100
                ]
            }, void 0, false, {
                fileName: "[project]/src/components/common/DataTable.tsx",
                lineNumber: 201,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/DataTable.tsx",
        lineNumber: 118,
        columnNumber: 5
    }, this);
}
_s(DataTable, "sNfh2crk4ePM2af6kiFf95YHCBM=");
_c = DataTable;
var _c;
__turbopack_context__.k.register(_c, "DataTable");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/common/Select.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
'use client';
;
;
const Select = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c = ({ label, error, helperText, required = false, options = [], placeholder = 'Select an option...', className = '', containerClassName = '', onChange, id, value, ...props }, ref)=>{
    const selectId = id || `select-${Math.random().toString(36).substr(2, 9)}`;
    const baseSelectClasses = `
    w-full px-3 py-2 border rounded-md shadow-sm 
    focus:outline-none focus:ring-2 focus:ring-offset-2 
    disabled:opacity-50 disabled:cursor-not-allowed
    dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600
    transition-colors duration-200
    appearance-none bg-white
    bg-no-repeat bg-right bg-[length:16px_16px]
    pr-10
  `;
    const selectClasses = error ? `${baseSelectClasses} border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600` : `${baseSelectClasses} border-gray-300 focus:border-primary focus:ring-primary dark:focus:border-primary dark:focus:ring-primary`;
    const handleChange = (e)=>{
        if (onChange) {
            onChange(e.target.value);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `space-y-1 ${containerClassName}`,
        children: [
            label && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                htmlFor: selectId,
                className: "block text-sm font-medium text-gray-700 dark:text-gray-300",
                children: [
                    label,
                    required && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "text-red-500 ml-1",
                        children: "*"
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/Select.tsx",
                        lineNumber: 68,
                        columnNumber: 24
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/Select.tsx",
                lineNumber: 63,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                        ref: ref,
                        id: selectId,
                        value: value || '',
                        onChange: handleChange,
                        className: `${selectClasses} ${className}`,
                        ...props,
                        children: [
                            placeholder && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                value: "",
                                disabled: true,
                                children: placeholder
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/Select.tsx",
                                lineNumber: 82,
                                columnNumber: 13
                            }, this),
                            options.map((option)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                    value: option.value,
                                    disabled: option.disabled,
                                    children: option.label
                                }, option.value, false, {
                                    fileName: "[project]/src/components/common/Select.tsx",
                                    lineNumber: 88,
                                    columnNumber: 13
                                }, this))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/Select.tsx",
                        lineNumber: 73,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                            className: "ri-arrow-down-s-line text-gray-400 dark:text-gray-500"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/Select.tsx",
                            lineNumber: 100,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/Select.tsx",
                        lineNumber: 99,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/Select.tsx",
                lineNumber: 72,
                columnNumber: 7
            }, this),
            error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "text-sm text-red-600 dark:text-red-400 flex items-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                        className: "ri-error-warning-line mr-1"
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/Select.tsx",
                        lineNumber: 106,
                        columnNumber: 11
                    }, this),
                    error
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/Select.tsx",
                lineNumber: 105,
                columnNumber: 9
            }, this),
            helperText && !error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "text-sm text-gray-500 dark:text-gray-400",
                children: helperText
            }, void 0, false, {
                fileName: "[project]/src/components/common/Select.tsx",
                lineNumber: 112,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/Select.tsx",
        lineNumber: 61,
        columnNumber: 5
    }, this);
});
_c1 = Select;
Select.displayName = 'Select';
const __TURBOPACK__default__export__ = Select;
var _c, _c1;
__turbopack_context__.k.register(_c, "Select$forwardRef");
__turbopack_context__.k.register(_c1, "Select");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/license/LicenseManagementTable.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>LicenseManagementTable)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/license.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/applicationService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$licenseCategoryService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/licenseCategoryService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$licenseTypeService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/licenseTypeService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$AssignButton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/AssignButton.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$license$2f$ApplicationViewModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/license/ApplicationViewModal.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$DataTable$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/DataTable.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/Select.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
;
;
function LicenseManagementTable({ licenseTypeId, licenseTypeCode, licenseTypeFilter, title, description, searchPlaceholder, emptyStateIcon, emptyStateMessage, departmentType }) {
    _s();
    const { user } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const [applicationsData, setApplicationsData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [licenseCategories, setLicenseCategories] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [licenseTypes, setLicenseTypes] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [resolvedLicenseTypeId, setResolvedLicenseTypeId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(undefined);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [selectedLicenseCategory, setSelectedLicenseCategory] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [statusFilter, setStatusFilter] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [dateRangeFilter, setDateRangeFilter] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    // View modal state
    const [showViewModal, setShowViewModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [selectedApplicationId, setSelectedApplicationId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const isAdmin = user?.isAdmin;
    // Function for viewing applications - opens modal
    const handleViewApplication = (applicationId)=>{
        setSelectedApplicationId(applicationId);
        setShowViewModal(true);
    };
    const handleCloseViewModal = ()=>{
        setShowViewModal(false);
        setSelectedApplicationId(null);
    };
    const handleViewModalUpdate = ()=>{
        // Refresh the applications list when modal updates data
        loadApplications({
            page: 1,
            limit: 10
        });
    };
    // Load applications function for DataTable
    const loadApplications = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "LicenseManagementTable.useCallback[loadApplications]": async (query)=>{
            try {
                setLoading(true);
                setError(null);
                const effectiveLicenseTypeId = licenseTypeId || resolvedLicenseTypeId;
                // Build filters object
                const filters = {};
                // Add license category filter if selected
                if (selectedLicenseCategory) {
                    filters.licenseCategoryId = selectedLicenseCategory;
                }
                // Add license type filter - prioritize licenseTypeCode search
                if (effectiveLicenseTypeId) {
                    filters.licenseTypeId = effectiveLicenseTypeId;
                } else if (licenseTypeCode) {
                    // If we have a license type code but no resolved ID yet, add it as a filter
                    filters.licenseTypeCode = licenseTypeCode;
                }
                // Add status filter if selected
                if (statusFilter) {
                    filters.status = statusFilter;
                }
                // Handle draft inclusion logic
                if (statusFilter === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].DRAFT) {
                    // If user specifically selected "Draft" status, include drafts
                    filters.include_draft = 'true';
                } else {
                    // For all other cases, exclude drafts by default
                    filters.include_draft = 'false';
                }
                const params = {
                    page: query.page,
                    limit: query.limit,
                    search: query.search || undefined,
                    filters: Object.keys(filters).length > 0 ? filters : undefined
                };
                console.log('Loading applications with params:', params);
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["applicationService"].getApplications(params);
                setApplicationsData(response);
            } catch (err) {
                console.error('Error loading applications:', err);
                setError('Failed to load applications');
                // Set empty data structure to prevent undefined errors
                setApplicationsData({
                    data: [],
                    meta: {
                        itemsPerPage: query.limit || 10,
                        totalItems: 0,
                        currentPage: query.page || 1,
                        totalPages: 0,
                        sortBy: [],
                        searchBy: [],
                        search: '',
                        select: []
                    },
                    links: {
                        current: ''
                    }
                });
            } finally{
                setLoading(false);
            }
        }
    }["LicenseManagementTable.useCallback[loadApplications]"], [
        licenseTypeId,
        resolvedLicenseTypeId,
        selectedLicenseCategory,
        statusFilter,
        licenseTypeCode
    ]);
    // Load license types and resolve license type ID from code or filter name
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "LicenseManagementTable.useEffect": ()=>{
            const fetchLicenseTypes = {
                "LicenseManagementTable.useEffect.fetchLicenseTypes": async ()=>{
                    try {
                        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$licenseTypeService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["licenseTypeService"].getAllLicenseTypes();
                        const types = Array.isArray(response) ? response : response?.data || [];
                        if (!Array.isArray(types)) {
                            console.warn('License types response is not an array:', types);
                            setLicenseTypes([]);
                            return;
                        }
                        setLicenseTypes(types);
                        // Priority 1: If we have a licenseTypeCode, find the matching license type ID
                        if (licenseTypeCode && types.length > 0) {
                            const matchingType = types.find({
                                "LicenseManagementTable.useEffect.fetchLicenseTypes.matchingType": (type)=>type.code === licenseTypeCode
                            }["LicenseManagementTable.useEffect.fetchLicenseTypes.matchingType"]);
                            if (matchingType) {
                                setResolvedLicenseTypeId(matchingType.license_type_id);
                            }
                        } else if (licenseTypeFilter && types.length > 0) {
                            const matchingType = types.find({
                                "LicenseManagementTable.useEffect.fetchLicenseTypes.matchingType": (type)=>type.name.toLowerCase().includes(licenseTypeFilter.toLowerCase())
                            }["LicenseManagementTable.useEffect.fetchLicenseTypes.matchingType"]);
                            if (matchingType) {
                                setResolvedLicenseTypeId(matchingType.license_type_id);
                            }
                        }
                    } catch (error) {
                        console.error('Error fetching license types:', error);
                        setLicenseTypes([]);
                    }
                }
            }["LicenseManagementTable.useEffect.fetchLicenseTypes"];
            fetchLicenseTypes();
        }
    }["LicenseManagementTable.useEffect"], [
        licenseTypeCode,
        licenseTypeFilter
    ]);
    // Fetch license categories for the dropdown
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "LicenseManagementTable.useEffect": ()=>{
            const fetchLicenseCategories = {
                "LicenseManagementTable.useEffect.fetchLicenseCategories": async ()=>{
                    try {
                        let categories = [];
                        const effectiveLicenseTypeId = licenseTypeId || resolvedLicenseTypeId;
                        if (effectiveLicenseTypeId) {
                            // Fetch categories for specific license type
                            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$licenseCategoryService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["licenseCategoryService"].getLicenseCategoriesByType(effectiveLicenseTypeId);
                            categories = Array.isArray(response) ? response : response?.data || [];
                        } else {
                            // Fetch all categories
                            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$licenseCategoryService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["licenseCategoryService"].getAllLicenseCategories();
                            categories = Array.isArray(response) ? response : response?.data || [];
                        }
                        if (!Array.isArray(categories)) {
                            console.warn('License categories response is not an array:', categories);
                            categories = [];
                        }
                        setLicenseCategories(categories);
                    } catch (error) {
                        console.error('Error fetching license categories:', error);
                        setLicenseCategories([]);
                    }
                }
            }["LicenseManagementTable.useEffect.fetchLicenseCategories"];
            if (resolvedLicenseTypeId || licenseTypeId) {
                fetchLicenseCategories();
            }
        }
    }["LicenseManagementTable.useEffect"], [
        licenseTypeId,
        resolvedLicenseTypeId
    ]);
    // Load applications on component mount and when filters change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "LicenseManagementTable.useEffect": ()=>{
            if (resolvedLicenseTypeId || licenseTypeId) {
                loadApplications({
                    page: 1,
                    limit: 10
                });
            }
        }
    }["LicenseManagementTable.useEffect"], [
        loadApplications,
        resolvedLicenseTypeId,
        licenseTypeId
    ]);
    const getStatusBadge = (status)=>{
        const statusClasses = {
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].DRAFT]: 'bg-gray-100 text-gray-800',
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].SUBMITTED]: 'bg-blue-100 text-blue-800',
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].UNDER_REVIEW]: 'bg-yellow-100 text-yellow-800',
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].EVALUATION]: 'bg-purple-100 text-purple-800',
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].APPROVED]: 'bg-green-100 text-green-800',
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].REJECTED]: 'bg-red-100 text-red-800',
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].WITHDRAWN]: 'bg-gray-100 text-gray-800'
        };
        const statusLabels = {
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].DRAFT]: 'Draft',
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].SUBMITTED]: 'Submitted',
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].UNDER_REVIEW]: 'Under Review',
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].EVALUATION]: 'Evaluation',
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].APPROVED]: 'Approved',
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].REJECTED]: 'Rejected',
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].WITHDRAWN]: 'Withdrawn'
        };
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
            className: `px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClasses[status]}`,
            children: statusLabels[status]
        }, void 0, false, {
            fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
            lineNumber: 254,
            columnNumber: 7
        }, this);
    };
    // Define columns for applications table
    const applicationColumns = [
        {
            key: 'application_number',
            label: 'Application Number',
            sortable: true,
            render: (value)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-sm font-medium text-gray-900 dark:text-gray-100",
                    children: value
                }, void 0, false, {
                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                    lineNumber: 267,
                    columnNumber: 9
                }, this)
        },
        {
            key: 'applicant',
            label: 'Applicant',
            render: (value, application)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex-shrink-0 h-10 w-10",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                    className: "ri-building-line text-blue-600 dark:text-blue-400"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                                    lineNumber: 279,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                                lineNumber: 278,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                            lineNumber: 277,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "ml-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-sm font-medium text-gray-900 dark:text-gray-100",
                                    children: application.applicant?.name || 'N/A'
                                }, void 0, false, {
                                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                                    lineNumber: 283,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-sm text-gray-500 dark:text-gray-400",
                                    children: [
                                        "BRN: ",
                                        application.applicant?.business_registration_number || 'N/A',
                                        " | TPIN: ",
                                        application.applicant?.tpin || 'N/A'
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                                    lineNumber: 286,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                            lineNumber: 282,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                    lineNumber: 276,
                    columnNumber: 9
                }, this)
        },
        {
            key: 'license_category',
            label: 'License Category',
            render: (value, application)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-sm text-gray-900 dark:text-gray-100",
                            children: application.license_category?.name || 'N/A'
                        }, void 0, false, {
                            fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                            lineNumber: 299,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-sm text-gray-500 dark:text-gray-400",
                            children: application.license_category?.license_type?.name || 'N/A'
                        }, void 0, false, {
                            fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                            lineNumber: 302,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                    lineNumber: 298,
                    columnNumber: 9
                }, this)
        },
        {
            key: 'status',
            label: 'Status',
            render: (value)=>getStatusBadge(value)
        },
        {
            key: 'progress_percentage',
            label: 'Progress',
            render: (value)=>getProgressBar(value)
        },
        {
            key: 'submitted_at',
            label: 'Submitted Date',
            sortable: true,
            render: (value, application)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    className: "text-sm text-gray-500 dark:text-gray-400",
                    children: application.submitted_at ? new Date(application.submitted_at).toLocaleDateString() : new Date(application.created_at).toLocaleDateString()
                }, void 0, false, {
                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                    lineNumber: 323,
                    columnNumber: 9
                }, this)
        },
        {
            key: 'actions',
            label: 'Actions',
            render: (value, application)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center justify-end space-x-2",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            type: "button",
                            onClick: ()=>handleViewApplication(application.application_id),
                            className: "inline-flex items-center px-3 py-1 text-xs font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/40 transition-colors",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                    className: "ri-eye-line mr-1"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                                    lineNumber: 341,
                                    columnNumber: 13
                                }, this),
                                "View"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                            lineNumber: 336,
                            columnNumber: 11
                        }, this),
                        application.assigned_to ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "inline-flex items-center px-3 py-1 text-xs font-medium text-purple-600 dark:text-purple-400 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-md",
                            title: application.assignee ? `Assigned to: ${application.assignee.first_name} ${application.assignee.last_name}` : 'Assigned',
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                    className: "ri-user-check-line mr-1"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                                    lineNumber: 349,
                                    columnNumber: 15
                                }, this),
                                "Assigned"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                            lineNumber: 345,
                            columnNumber: 13
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$AssignButton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            itemId: application.application_id,
                            itemType: "application",
                            itemTitle: application.application_number,
                            onAssignSuccess: handleAssignSuccess,
                            className: "text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 hover:bg-green-100 dark:hover:bg-green-900/40"
                        }, void 0, false, {
                            fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                            lineNumber: 353,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                    lineNumber: 335,
                    columnNumber: 9
                }, this)
        }
    ];
    const getProgressBar = (percentage)=>{
        const getProgressColor = (percent)=>{
            if (percent >= 100) return 'bg-green-600';
            if (percent >= 75) return 'bg-blue-600';
            if (percent >= 50) return 'bg-yellow-600';
            if (percent >= 25) return 'bg-orange-600';
            return 'bg-red-600';
        };
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "w-16 bg-gray-200 rounded-full h-2 mr-2",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: `h-2 rounded-full ${getProgressColor(percentage)}`,
                        style: {
                            width: `${percentage}%`
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                        lineNumber: 378,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                    lineNumber: 377,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    className: "text-sm text-gray-500",
                    children: [
                        percentage,
                        "%"
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                    lineNumber: 383,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
            lineNumber: 376,
            columnNumber: 7
        }, this);
    };
    const handleAssignSuccess = ()=>{
        // Refresh the applications list when assignment is successful
        loadApplications({
            page: 1,
            limit: 10
        });
    };
    const handleFilterChange = (filterType, value)=>{
        switch(filterType){
            case 'licenseCategory':
                setSelectedLicenseCategory(value);
                break;
            case 'status':
                setStatusFilter(value);
                break;
            case 'dateRange':
                setDateRangeFilter(value);
                break;
        }
        // Reload applications with new filters
        loadApplications({
            page: 1,
            limit: 10
        });
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-6",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "max-w-7xl mx-auto",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mb-8",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                    className: "text-3xl font-bold text-gray-900 dark:text-gray-100",
                                    children: title
                                }, void 0, false, {
                                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                                    lineNumber: 416,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "mt-2 text-sm text-gray-600 dark:text-gray-400",
                                    children: description
                                }, void 0, false, {
                                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                                    lineNumber: 417,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                            lineNumber: 415,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                        lineNumber: 414,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                    lineNumber: 413,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4",
                            children: "Filters"
                        }, void 0, false, {
                            fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                            lineNumber: 426,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    label: "License Category",
                                    value: selectedLicenseCategory,
                                    onChange: (value)=>handleFilterChange('licenseCategory', value),
                                    options: [
                                        {
                                            value: '',
                                            label: 'All Categories'
                                        },
                                        ...licenseCategories.map((category)=>({
                                                value: category.license_category_id,
                                                label: category.name
                                            }))
                                    ]
                                }, void 0, false, {
                                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                                    lineNumber: 429,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    label: "Application Status",
                                    value: statusFilter,
                                    onChange: (value)=>handleFilterChange('status', value),
                                    options: [
                                        {
                                            value: '',
                                            label: 'All Statuses'
                                        },
                                        {
                                            value: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].DRAFT,
                                            label: 'Draft'
                                        },
                                        {
                                            value: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].SUBMITTED,
                                            label: 'Submitted'
                                        },
                                        {
                                            value: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].UNDER_REVIEW,
                                            label: 'Under Review'
                                        },
                                        {
                                            value: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].EVALUATION,
                                            label: 'Evaluation'
                                        },
                                        {
                                            value: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].APPROVED,
                                            label: 'Approved'
                                        },
                                        {
                                            value: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].REJECTED,
                                            label: 'Rejected'
                                        },
                                        {
                                            value: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApplicationStatus"].WITHDRAWN,
                                            label: 'Withdrawn'
                                        }
                                    ]
                                }, void 0, false, {
                                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                                    lineNumber: 443,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    label: "Date Range",
                                    value: dateRangeFilter,
                                    onChange: (value)=>handleFilterChange('dateRange', value),
                                    options: [
                                        {
                                            value: '',
                                            label: 'All Time'
                                        },
                                        {
                                            value: 'last-30',
                                            label: 'Last 30 Days'
                                        },
                                        {
                                            value: 'last-90',
                                            label: 'Last 90 Days'
                                        },
                                        {
                                            value: 'last-year',
                                            label: 'Last Year'
                                        }
                                    ]
                                }, void 0, false, {
                                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                                    lineNumber: 460,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                            lineNumber: 427,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                    lineNumber: 425,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$DataTable$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    columns: applicationColumns,
                    data: applicationsData,
                    loading: loading,
                    onQueryChange: loadApplications,
                    searchPlaceholder: searchPlaceholder
                }, void 0, false, {
                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                    lineNumber: 477,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$license$2f$ApplicationViewModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    isOpen: showViewModal,
                    onClose: handleCloseViewModal,
                    applicationId: selectedApplicationId,
                    departmentType: departmentType,
                    licenseTypeCode: licenseTypeCode,
                    onUpdate: handleViewModalUpdate
                }, void 0, false, {
                    fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
                    lineNumber: 486,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
            lineNumber: 411,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/license/LicenseManagementTable.tsx",
        lineNumber: 410,
        columnNumber: 5
    }, this);
}
_s(LicenseManagementTable, "6gsNTVW1GX0m53wPLz0SHHd6+xc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
_c = LicenseManagementTable;
var _c;
__turbopack_context__.k.register(_c, "LicenseManagementTable");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/applications/[license-type]/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>LicenseTypeApplicationsPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$license$2f$LicenseManagementTable$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/license/LicenseManagementTable.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$licenseTypeService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/licenseTypeService.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
function LicenseTypeApplicationsPage() {
    _s();
    const params = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useParams"])();
    const licenseTypeCode = params['license-type'];
    const [licenseType, setLicenseType] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Load required documents and existing uploads
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "LicenseTypeApplicationsPage.useEffect": ()=>{
            const fetchLicenseType = {
                "LicenseTypeApplicationsPage.useEffect.fetchLicenseType": async ()=>{
                    try {
                        const licenseTypeResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$licenseTypeService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["licenseTypeService"].getLicenseTypeByCode(licenseTypeCode);
                        setLicenseType(licenseTypeResponse);
                    } catch (error) {
                        console.error('Error fetching license type:', error);
                    }
                }
            }["LicenseTypeApplicationsPage.useEffect.fetchLicenseType"];
            if (licenseTypeCode) {
                fetchLicenseType();
            }
        }
    }["LicenseTypeApplicationsPage.useEffect"], [
        licenseTypeCode
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$license$2f$LicenseManagementTable$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        licenseTypeCode: licenseTypeCode,
        title: `${licenseType?.name || 'License'} Management`,
        description: licenseType?.description || licenseType?.name || 'License management',
        searchPlaceholder: `Search ${licenseType?.name || 'license'} applications...`,
        emptyStateIcon: 'ri-mail-line',
        emptyStateMessage: 'No applications are available.',
        departmentType: licenseType?.name
    }, void 0, false, {
        fileName: "[project]/src/app/applications/[license-type]/page.tsx",
        lineNumber: 31,
        columnNumber: 5
    }, this);
}
_s(LicenseTypeApplicationsPage, "Wm+jTKUqQg3/ptjhL4mEN38XjKU=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useParams"]
    ];
});
_c = LicenseTypeApplicationsPage;
var _c;
__turbopack_context__.k.register(_c, "LicenseTypeApplicationsPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_91bddfd4._.js.map
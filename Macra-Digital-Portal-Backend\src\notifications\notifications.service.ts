import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Notifications, NotificationType, NotificationStatus, RecipientType } from '../entities/notifications.entity';
import { CreateNotificationDto } from '../dto/notifications/create-notification.dto';
import { UpdateNotificationDto } from '../dto/notifications/update-notification.dto';
import { PaginateQuery, PaginateConfig, paginate, Paginated } from 'nestjs-paginate';

@Injectable()
export class NotificationsService {
  constructor(
    @InjectRepository(Notifications)
    private notificationsRepository: Repository<Notifications>,
  ) {}

  private readonly paginateConfig: PaginateConfig<Notifications> = {
    sortableColumns: ['created_at', 'sent_at', 'priority', 'status', 'type'],
    nullSort: 'last',
    defaultSortBy: [['created_at', 'DESC']],
    searchableColumns: ['subject', 'message', 'recipient_email', 'recipient_phone'],
    select: [
      'notification_id',
      'type',
      'status',
      'priority',
      'recipient_type',
      'recipient_id',
      'recipient_email',
      'recipient_phone',
      'subject',
      'message',
      'html_content',
      'entity_type',
      'entity_id',
      'metadata',
      'external_id',
      'error_message',
      'retry_count',
      'is_read',
      'sent_at',
      'delivered_at',
      'read_at',
      'action_url',
      'expires_at',
      'created_at',
      'created_by',
      'updated_at',
      'updated_by',
    ],
    relations: ['recipient', 'creator', 'updater'],
    filterableColumns: {
      type: true,
      status: true,
      priority: true,
      recipient_type: true,
      recipient_id: true,
      entity_type: true,
      entity_id: true,
      is_read: true,
      created_by: true,
    },
    defaultLimit: 20,
    maxLimit: 100,
  };

  async create(createNotificationDto: CreateNotificationDto, createdBy: string): Promise<Notifications> {
    const notification = this.notificationsRepository.create({
      ...createNotificationDto,
      created_by: createdBy,
    });

    return this.notificationsRepository.save(notification);
  }

  async findAll(query: PaginateQuery): Promise<Paginated<Notifications>> {
    return paginate(query, this.notificationsRepository, this.paginateConfig);
  }

  async findByRecipient(recipientId: string, query: PaginateQuery): Promise<Paginated<Notifications>> {
    const config: PaginateConfig<Notifications> = {
      ...this.paginateConfig,
      where: { recipient_id: recipientId },
    };

    return paginate(query, this.notificationsRepository, config);
  }

  async findByType(type: NotificationType, query: PaginateQuery): Promise<Paginated<Notifications>> {
    const config: PaginateConfig<Notifications> = {
      ...this.paginateConfig,
      where: { type },
    };

    return paginate(query, this.notificationsRepository, config);
  }

  async findByStatus(status: NotificationStatus, query: PaginateQuery): Promise<Paginated<Notifications>> {
    const config: PaginateConfig<Notifications> = {
      ...this.paginateConfig,
      where: { status },
    };

    return paginate(query, this.notificationsRepository, config);
  }

  async findByEntity(entityType: string, entityId: string, query: PaginateQuery): Promise<Paginated<Notifications>> {
    const config: PaginateConfig<Notifications> = {
      ...this.paginateConfig,
      where: { entity_type: entityType, entity_id: entityId },
    };

    return paginate(query, this.notificationsRepository, config);
  }

  async findOne(id: string): Promise<Notifications> {
    const notification = await this.notificationsRepository.findOne({
      where: { notification_id: id },
      relations: ['recipient', 'creator', 'updater'],
    });

    if (!notification) {
      throw new NotFoundException(`Notification with ID ${id} not found`);
    }

    return notification;
  }

  async update(id: string, updateNotificationDto: UpdateNotificationDto, updatedBy: string): Promise<Notifications> {
    const notification = await this.findOne(id);

    Object.assign(notification, updateNotificationDto, { updated_by: updatedBy });
    return this.notificationsRepository.save(notification);
  }

  async markAsRead(id: string, updatedBy: string): Promise<Notifications> {
    const notification = await this.findOne(id);
    
    notification.is_read = true;
    notification.read_at = new Date();
    notification.updated_by = updatedBy;

    return this.notificationsRepository.save(notification);
  }

  async markAsSent(id: string, externalId?: string): Promise<Notifications> {
    const notification = await this.findOne(id);
    
    notification.status = NotificationStatus.SENT;
    notification.sent_at = new Date();
    if (externalId) {
      notification.external_id = externalId;
    }

    return this.notificationsRepository.save(notification);
  }

  async markAsDelivered(id: string): Promise<Notifications> {
    const notification = await this.findOne(id);
    
    notification.status = NotificationStatus.DELIVERED;
    notification.delivered_at = new Date();

    return this.notificationsRepository.save(notification);
  }

  async markAsFailed(id: string, errorMessage: string): Promise<Notifications> {
    const notification = await this.findOne(id);
    
    notification.status = NotificationStatus.FAILED;
    notification.error_message = errorMessage;
    notification.retry_count = (notification.retry_count || 0) + 1;

    return this.notificationsRepository.save(notification);
  }

  async remove(id: string): Promise<void> {
    const notification = await this.findOne(id);
    await this.notificationsRepository.softDelete(notification.notification_id);
  }

  // Helper methods for creating specific notification types
  async createEmailNotification(
    recipientId: string,
    recipientEmail: string,
    subject: string,
    message: string,
    htmlContent?: string,
    entityType?: string,
    entityId?: string,
    createdBy?: string
  ): Promise<Notifications> {
    const createDto: CreateNotificationDto = {
      type: NotificationType.EMAIL,
      recipient_type: RecipientType.CUSTOMER, // Default, can be overridden
      recipient_id: recipientId,
      recipient_email: recipientEmail,
      subject,
      message,
      html_content: htmlContent,
      entity_type: entityType,
      entity_id: entityId,
    };

    return this.create(createDto, createdBy || 'system');
  }

  async createSmsNotification(
    recipientId: string,
    recipientPhone: string,
    subject: string,
    message: string,
    entityType?: string,
    entityId?: string,
    createdBy?: string
  ): Promise<Notifications> {
    const createDto: CreateNotificationDto = {
      type: NotificationType.SMS,
      recipient_type: RecipientType.CUSTOMER, // Default, can be overridden
      recipient_id: recipientId,
      recipient_phone: recipientPhone,
      subject,
      message,
      entity_type: entityType,
      entity_id: entityId,
    };

    return this.create(createDto, createdBy || 'system');
  }

  async createInAppNotification(
    recipientId: string,
    subject: string,
    message: string,
    entityType?: string,
    entityId?: string,
    actionUrl?: string,
    createdBy?: string
  ): Promise<Notifications> {
    const createDto: CreateNotificationDto = {
      type: NotificationType.IN_APP,
      recipient_type: RecipientType.CUSTOMER, // Default, can be overridden
      recipient_id: recipientId,
      subject,
      message,
      entity_type: entityType,
      entity_id: entityId,
      action_url: actionUrl,
    };

    return this.create(createDto, createdBy || 'system');
  }

  // Get notification statistics
  async getStats(): Promise<any> {
    const totalNotifications = await this.notificationsRepository.count();
    const sentNotifications = await this.notificationsRepository.count({
      where: { status: NotificationStatus.SENT }
    });
    const failedNotifications = await this.notificationsRepository.count({
      where: { status: NotificationStatus.FAILED }
    });
    const pendingNotifications = await this.notificationsRepository.count({
      where: { status: NotificationStatus.PENDING }
    });

    return {
      total: totalNotifications,
      sent: sentNotifications,
      failed: failedNotifications,
      pending: pendingNotifications,
      success_rate: totalNotifications > 0 ? (sentNotifications / totalNotifications) * 100 : 0
    };
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddManagerIdToDepartments1736252000000 implements MigrationInterface {
  name = 'AddManagerIdToDepartments1736252000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    console.log('🔧 Adding manager_id column to departments table...');

    // Add manager_id column to departments table
    await queryRunner.query(`
      ALTER TABLE "departments"
      ADD COLUMN "manager_id" VARCHAR(36) NULL
    `);

    // Add index for better query performance
    await queryRunner.query(`
      CREATE INDEX "idx_departments_manager_id" ON "departments" ("manager_id")
    `);

    // Add foreign key constraint to ensure manager_id references a valid user
    await queryRunner.query(`
      ALTER TABLE "departments"
      ADD CONSTRAINT "fk_departments_manager_id"
      FOREIGN KEY ("manager_id") REFERENCES "users" ("user_id")
    `);

    console.log('✅ Successfully added manager_id column to departments table');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    console.log('🔄 Removing manager_id column from departments table...');

    // Remove foreign key constraint
    await queryRunner.query(`
      ALTER TABLE "departments"
      DROP CONSTRAINT "fk_departments_manager_id"
    `);

    // Remove index
    await queryRunner.query(`
      DROP INDEX "idx_departments_manager_id"
    `);

    // Remove column
    await queryRunner.query(`
      ALTER TABLE "departments"
      DROP COLUMN "manager_id"
    `);

    console.log('✅ Successfully removed manager_id column from departments table');
  }
}

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Applications } from '../entities/applications.entity';
import { TasksService } from '../tasks/tasks.service';
import { NotificationHelperService } from '../notifications/notification-helper.service';
import { CreateTaskDto } from '../dto/tasks/create-task.dto';
import { TaskType, TaskPriority, TaskStatus } from '../entities/tasks.entity';

@Injectable()
export class ApplicationTaskHelperService {
  constructor(
    @InjectRepository(Applications)
    private applicationsRepository: Repository<Applications>,
    private tasksService: TasksService,
    private notificationHelper: NotificationHelperService,
  ) {}

  /**
   * Centralized method to handle task creation when application status changes to submitted
   * This should be called by any service that updates application status
   */
  async handleApplicationSubmission(
    applicationId: string,
    previousStatus: string,
    newStatus: string,
    updatedBy: string
  ): Promise<void> {

    // Only create task when status changes TO submitted (and wasn't already submitted)
    if (newStatus === 'submitted' && previousStatus !== 'submitted') {

      try {
        // Get the application with all necessary relations
        const application = await this.applicationsRepository.findOne({
          where: { application_id: applicationId },
          relations: ['applicant', 'license_category', 'license_category.license_type'],
        });

        if (!application) {
          console.error(`❌ Application not found: ${applicationId}`);
          return;
        }

        const applicantName = application.applicant 
          ? application.applicant.name
          : 'Unknown Applicant';

        const licenseTypeName = application.license_category?.license_type?.name || 'Unknown License Type';

        const taskData: CreateTaskDto = {
          title: `Review Application - ${application.application_number}`,
          description: `Application submitted by ${applicantName} for ${licenseTypeName} license. Please review and process this application.`,
          task_type: TaskType.APPLICATION,
          priority: TaskPriority.MEDIUM,
          status: TaskStatus.PENDING,
          entity_type: 'application',
          entity_id: application.application_id,
        };

        console.log(`📝 Creating task for application: ${application.application_number}`, taskData);
        const createdTask = await this.tasksService.create(taskData, updatedBy);
        console.log(`✅ Task created successfully for application: ${application.application_number}, Task ID: ${createdTask.task_id}`);

        // Send notification to applicant about submission
        if (application.applicant) {
          try {
            console.log(`📧 Sending notification for application: ${application.application_number}`);
            await this.notificationHelper.notifyApplicationStatus(
              application.application_id,
              application.applicant_id,
              application.applicant.email,
              application.applicant.phone,
              application.application_number,
              'submitted',
              updatedBy,
              application.applicant.name,
              licenseTypeName
            );
            console.log(`✅ Notification sent for submitted application: ${application.application_number}`);
          } catch (notificationError) {
            console.error('❌ Error sending notification for submitted application:', notificationError);
            // Don't fail if notification fails
          }
        }
      } catch (error) {
        console.error('❌ Error creating task for submitted application:', error);
        console.error('Error details:', error.message, error.stack);
        // Don't fail the status update if task creation fails
      }
    } else {
      console.log(`ℹ️ Task creation skipped. Reason: newStatus='${newStatus}', previousStatus='${previousStatus}', applicationId='${applicationId}'`);
      if (newStatus !== 'submitted') {
        console.log(`   → Status is not 'submitted' (it's '${newStatus}')`);
      }
      if (previousStatus === 'submitted') {
        console.log(`   → Previous status was already 'submitted'`);
      }
    }
  }

  /**
   * Check if a task already exists for an application to prevent duplicates
   */
  async taskExistsForApplication(applicationId: string): Promise<boolean> {
    try {
      // For now, we'll skip the duplicate check since the method doesn't exist
      // This can be implemented later if needed
      console.log(`Checking for existing tasks for application: ${applicationId}`);
      return false;
    } catch (error) {
      console.error('Error checking existing tasks:', error);
      return false;
    }
  }

  /**
   * Create task with duplicate prevention
   */
  async createTaskWithDuplicateCheck(
    applicationId: string,
    previousStatus: string,
    newStatus: string,
    updatedBy: string
  ): Promise<void> {
    // Only create task when status changes TO submitted (and wasn't already submitted)
    if (newStatus === 'submitted' && previousStatus !== 'submitted') {
      // Check if task already exists to prevent duplicates
      const taskExists = await this.taskExistsForApplication(applicationId);
      
      if (taskExists) {
        console.log(`ℹ️ Task already exists for application: ${applicationId}, skipping creation`);
        return;
      }

      await this.handleApplicationSubmission(applicationId, previousStatus, newStatus, updatedBy);
    }
  }
}

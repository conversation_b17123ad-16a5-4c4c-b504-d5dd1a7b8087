'use client';

import { useState, useEffect } from 'react';
import { User, Permission, Role } from '../../services/userService';
import { permissionService } from '../../services/permissionService';
import { roleService } from '../../services/roleService';
import { Department, departmentService } from '../../services/departmentService';
import UserModal from '../../components/users/UserModal';
import RoleModal from '../../components/roles/RoleModal';
import DepartmentModal from '../../components/departments/DepartmentModal';
import OrganizationModal from '../../components/organization/OrganizationModal';
import UserTabs from '../../components/users/UserTabs';
import UsersTab from '../../components/users/UsersTab';
import RolesTab from '../../components/roles/RolesTab';
import PermissionsTab from '../../components/permissions/PermissionsTab';
import DepartmentsTab from '../../components/departments/DepartmentsTab';
import { Organization, organizationService } from '@/services/organizationService';
import OrganizationsTab from '@/components/organization/OrganizationsTab';

export default function UsersPage() {
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isRoleModalOpen, setIsRoleModalOpen] = useState(false);
  const [isDepartmentModalOpen, setIsDepartmentModalOpen] = useState(false);
  const [isOrganizationModalOpen, setIsOrganizationModalOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [editingDepartment, setEditingDepartment] = useState<Department | null>(null);
  const [editingOrganization, setEditingOrganization] = useState<Organization | null>(null);
  const [activeTab, setActiveTab] = useState('users');
  const [successMessage, setSuccessMessage] = useState<string>('');
  const [errorMessage, setErrorMessage] = useState<string>('');

  useEffect(() => {
    loadPermissionsForModal();
    loadRolesForModal();
    loadDepartmentsForModal();
    loadOrganizationsForModal();
  }, []);

  const loadPermissionsForModal = async () => {
    try {
      const permissionsData = await permissionService.getAllPermissions();
      setPermissions(permissionsData);
    } catch (err) {
      console.error('Error loading permissions for modal:', err);
    }
  };

  const loadDepartmentsForModal = async () => {
    try {
      const departmentsData = await departmentService.getAllDepartments();
      setDepartments(departmentsData);
    } catch (err) {
      console.error('Error loading departments for modal:', err);
    }
  };

  const loadOrganizationsForModal = async () => {
    try {
      const organizationsData = await organizationService.getAllOrganizations();
      setOrganizations(organizationsData);
    } catch (err) {
      console.error('Error loading organizations for modal:', err);
    }
  };

  const loadRolesForModal = async () => {
    try {
      const rolesResponse = await roleService.getRoles({ page: 1, limit: 100 });
      setRoles(rolesResponse.data);
    } catch (err) {
      console.error('Error loading roles for modal:', err);
    }
  };

  const handleCreateUser = () => {
    setEditingUser(null);
    setIsModalOpen(true);
  };

  const handleEditUser = (user: User) => {
    setEditingUser(user);
    setIsModalOpen(true);
  };

  const handleCreateRole = () => {
    setEditingRole(null);
    setIsRoleModalOpen(true);
  };

  const handleEditRole = async (role: Role) => {
    try {
      // Fetch the role with permissions
      const roleWithPermissions = await roleService.getRoleWithPermissions(role.role_id);
      setEditingRole(roleWithPermissions);
      setIsRoleModalOpen(true);
    } catch (err) {
      console.error('Error loading role with permissions:', err);
      // Fallback to the role without permissions
      setEditingRole(role);
      setIsRoleModalOpen(true);
    }
  };

  const handleCreateDepartment = () => {
    setEditingDepartment(null);
    setIsDepartmentModalOpen(true);
  };

  const handleEditDepartment = (department: Department) => {
    setEditingDepartment(department);
    setIsDepartmentModalOpen(true);
  };

  const handleCreateOrganization = () => {
    setEditingOrganization(null);
    setIsOrganizationModalOpen(true);
  };

  const handleEditOrganization = (organization: Organization) => {
    setEditingOrganization(organization);
    setIsOrganizationModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setEditingUser(null);
  };

  const handleRoleModalClose = () => {
    setIsRoleModalOpen(false);
    setEditingRole(null);
  };

  const handleDepartmentModalClose = () => {
    setIsDepartmentModalOpen(false);
    setEditingDepartment(null);
  };

  const handleOrganizationModalClose = () => {
    setIsOrganizationModalOpen(false);
    setEditingOrganization(null);
  };

  const handleUserSaved = () => {
    handleModalClose();
    // The UsersTab component will handle its own data refresh
  };

  const handleRoleSaved = (roleName: string, isEdit: boolean = false) => {
    handleRoleModalClose();

    // Show success message
    const action = isEdit ? 'updated' : 'created';
    setSuccessMessage(`Role "${roleName}" has been ${action} successfully!`);
    setErrorMessage('');

    // Auto-clear success message after 5 seconds
    setTimeout(() => {
      setSuccessMessage('');
    }, 5000);

    // The RolesTab component will handle its own data refresh
  };

  const handleDepartmentSaved = (department: Department) => {
    handleDepartmentModalClose();

    // Show success message
    const action = editingDepartment ? 'updated' : 'created';
    setSuccessMessage(`Department "${department.name}" has been ${action} successfully!`);
    setErrorMessage('');

    // Auto-clear success message after 5 seconds
    setTimeout(() => {
      setSuccessMessage('');
    }, 5000);

    // The DepartmentsTab component will handle its own data refresh
  };

  const handleOrganizationSaved = (organization: Organization) => {
    handleOrganizationModalClose();

    // Show success message
    const action = editingOrganization ? 'updated' : 'created';
    setSuccessMessage(`Organization "${organization.name}" has been ${action} successfully!`);
    setErrorMessage('');

    // Auto-clear success message after 5 seconds
    setTimeout(() => {
      setSuccessMessage('');
    }, 5000);

    // The OrganizationsTab component will handle its own data refresh
  };

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  const handleNavigateToOrganizations = () => {
    setActiveTab('organizations');
    // Close the user modal if it's open
    if (isModalOpen) {
      setIsModalOpen(false);
      setEditingUser(null);
    }
  };

  // Define tabs for the tab system
  const tabs = [
    {
      id: 'users',
      label: 'Users',
      content: <UsersTab onEditUser={handleEditUser} onCreateUser={handleCreateUser} />
    },
    {
      id: 'roles',
      label: 'Roles',
      content: <RolesTab onEditRole={handleEditRole} onCreateRole={handleCreateRole} />
    },
    {
      id: 'permissions',
      label: 'Permissions',
      content: <PermissionsTab />
    },
    {
      id: 'departments',
      label: 'Departments',
      content: <DepartmentsTab onEditDepartment={handleEditDepartment} onCreateDepartment={handleCreateDepartment} />
    },
    {
      id: 'organizations',
      label: 'Organizations',
      content: <OrganizationsTab onEditOrganization={handleEditOrganization} onCreateOrganization={handleCreateOrganization} />
    }
  ];

  return (
    <main className="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-4 sm:p-6">
      <div className="max-w-7xl mx-auto">
        {/* Success Message */}
        {successMessage && (
          <div className="mb-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 relative animate-in slide-in-from-top-2 duration-300">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <div className="h-5 w-5 text-green-400 dark:text-green-500">
                  <svg fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <div className="ml-3 flex-1">
                <h3 className="text-sm font-medium text-green-800 dark:text-green-200 mb-1">
                  Success
                </h3>
                <p className="text-sm text-green-700 dark:text-green-300">
                  {successMessage}
                </p>
              </div>
              <div className="ml-4 flex-shrink-0">
                <button
                  type="button"
                  onClick={() => setSuccessMessage('')}
                  className="inline-flex text-green-400 hover:text-green-600 dark:text-green-500 dark:hover:text-green-300 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 rounded-md p-1 transition-colors duration-200"
                  aria-label="Dismiss success message"
                >
                  <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Error Message */}
        {errorMessage && (
          <div className="mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 relative animate-in slide-in-from-top-2 duration-300">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <div className="h-5 w-5 text-red-400 dark:text-red-500">
                  <svg fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <div className="ml-3 flex-1">
                <h3 className="text-sm font-medium text-red-800 dark:text-red-200 mb-1">
                  Error
                </h3>
                <p className="text-sm text-red-700 dark:text-red-300">
                  {errorMessage}
                </p>
              </div>
              <div className="ml-4 flex-shrink-0">
                <button
                  type="button"
                  onClick={() => setErrorMessage('')}
                  className="inline-flex text-red-400 hover:text-red-600 dark:text-red-500 dark:hover:text-red-300 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 rounded-md p-1 transition-colors duration-200"
                  aria-label="Dismiss error message"
                >
                  <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        )}

        <UserTabs
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={handleTabChange}
        />
      </div>

      {/* User Modal */}
      <UserModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        onSave={handleUserSaved}
        user={editingUser}
        roles={roles}
        departments={departments}
      />

      {/* Role Modal */}
      <RoleModal
        isOpen={isRoleModalOpen}
        onClose={handleRoleModalClose}
        onSave={handleRoleSaved}
        role={editingRole}
        permissions={permissions}
      />

      {/* Department Modal */}
      <DepartmentModal
        isOpen={isDepartmentModalOpen}
        onClose={handleDepartmentModalClose}
        onSave={handleDepartmentSaved}
        department={editingDepartment}
      />

      {/* Organization Modal */}
      <OrganizationModal
        isOpen={isOrganizationModalOpen}
        onClose={handleOrganizationModalClose}
        onSave={handleOrganizationSaved}
        organization={editingOrganization}
      />
    </main>
  );
}

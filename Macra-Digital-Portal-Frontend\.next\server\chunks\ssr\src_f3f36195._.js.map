{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/types/license.ts"], "sourcesContent": ["export interface LicenseType {\r\n  license_type_id: string;\r\n  name: string;\r\n  code?: string;\r\n  description?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface LicenseCategory {\r\n  license_category_id: string;\r\n  name: string;\r\n  description?: string;\r\n  license_type_id: string;\r\n  license_type?: LicenseType;\r\n  parent_id?: string;\r\n  parent?: LicenseCategory;\r\n  children?: LicenseCategory[];\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface Applicant {\r\n  applicant_id: string;\r\n  name: string;\r\n  business_registration_number: string;\r\n  tpin: string;\r\n  website: string;\r\n  email: string;\r\n  phone: string;\r\n  fax?: string;\r\n  level_of_insurance_cover?: string;\r\n  address_id?: string;\r\n  contact_id?: string;\r\n  date_incorporation: string;\r\n  place_incorporation: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface Application {\r\n  application_id: string;\r\n  application_number: string;\r\n  applicant_id: string;\r\n  license_category_id: string;\r\n  status: ApplicationStatus;\r\n  current_step: number;\r\n  progress_percentage: number;\r\n  submitted_at?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  assigned_to?: string;\r\n  assigned_at?: string;\r\n  application_data?: any;\r\n  applicant?:Applicant;\r\n  license_category?: LicenseCategory;\r\n  assignee?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n}\r\n\r\nexport enum ApplicationStatus {\r\n  DRAFT = 'draft',\r\n  SUBMITTED = 'submitted',\r\n  UNDER_REVIEW = 'under_review',\r\n  EVALUATION = 'evaluation',\r\n  APPROVED = 'approved',\r\n  REJECTED = 'rejected',\r\n  WITHDRAWN = 'withdrawn',\r\n}\r\n\r\nexport interface ApplicationFilters {\r\n  licenseTypeId?: string;\r\n  licenseCategoryId?: string;\r\n  status?: ApplicationStatus | '';\r\n  dateRange?: string;\r\n  search?: string;\r\n}\r\n\r\nexport interface PaginatedResponse<T> {\r\n  data: T[];\r\n  meta: {\r\n    itemsPerPage: number;\r\n    totalItems: number;\r\n    currentPage: number;\r\n    totalPages: number;\r\n    sortBy: [string, string][];\r\n    searchBy: string[];\r\n    search: string;\r\n    filter: Record<string, string | string[]>;\r\n  };\r\n  links: {\r\n    first: string;\r\n    previous: string;\r\n    current: string;\r\n    next: string;\r\n    last: string;\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAgEO,IAAA,AAAK,2CAAA;;;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/applicationService.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\nimport { Application, ApplicationStatus, ApplicationFilters, PaginatedResponse } from '../types/license';\r\n\r\nexport const applicationService = {\r\n  // Get all applications with pagination and filters\r\n  async getApplications(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n    filters?: ApplicationFilters;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);\r\n    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);\r\n    \r\n    // Add filters\r\n    if (params?.filters?.licenseTypeId) {\r\n      queryParams.append('filter.license_category.license_type_id', params.filters.licenseTypeId);\r\n    }\r\n    if (params?.filters?.licenseCategoryId) {\r\n      queryParams.append('filter.license_category_id', params.filters.licenseCategoryId);\r\n    }\r\n    if (params?.filters?.status) {\r\n      queryParams.append('filter.status', params.filters.status);\r\n    }\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by license type (through license category)\r\n  async getApplicationsByLicenseType(licenseTypeId: string, params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    status?: ApplicationStatus;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.status) queryParams.append('filter.status', params.status);\r\n    \r\n    // Filter by license type through license category\r\n    queryParams.append('filter.license_category.license_type_id', licenseTypeId);\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get single application by ID\r\n  async getApplication(id: string): Promise<Application> {\r\n    const response = await apiClient.get(`/applications/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by applicant\r\n  async getApplicationsByApplicant(applicantId: string): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-applicant/${applicantId}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by status\r\n  async getApplicationsByStatus(status: string): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-status/${status}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update application status\r\n  async updateApplicationStatus(id: string, status: string): Promise<Application> {\r\n    const response = await apiClient.put(`/applications/${id}/status?status=${status}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update application progress\r\n  async updateApplicationProgress(id: string, currentStep: number, progressPercentage: number): Promise<Application> {\r\n    const response = await apiClient.put(\r\n      `/applications/${id}/progress?currentStep=${currentStep}&progressPercentage=${progressPercentage}`\r\n    );\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get application statistics\r\n  async getApplicationStats(): Promise<Record<string, number>> {\r\n    const response = await apiClient.get('/applications/stats');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create new application\r\n  async createApplication(data: {\r\n    application_number: string;\r\n    applicant_id: string;\r\n    license_category_id: string;\r\n    status?: ApplicationStatus;\r\n    current_step?: number;\r\n    progress_percentage?: number;\r\n    submitted_at?: Date;\r\n  }): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.post('/applications', data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update application with improved error handling\r\n  async updateApplication(id: string, data: Partial<Application>): Promise<Application> {\r\n    try {\r\n      console.log('Updating application:', id, 'with data:', data);\r\n      const response = await apiClient.put(`/applications/${id}`, data, {\r\n        timeout: 30000, // 30 second timeout\r\n      });\r\n      return processApiResponse(response);\r\n    } catch (error: any) {\r\n      console.error('Error updating application:', error);\r\n\r\n      // Handle specific error cases\r\n      if (error.code === 'ECONNABORTED') {\r\n        throw new Error('Request timeout - please try again');\r\n      }\r\n\r\n      if (error.response?.status === 400) {\r\n        const message = error.response?.data?.message || 'Invalid application data';\r\n        console.error('400 Bad Request details:', error.response?.data);\r\n        throw new Error(`Bad Request: ${message}`);\r\n      }\r\n\r\n      if (error.response?.status === 429) {\r\n        throw new Error('Too many requests - please wait a moment and try again');\r\n      }\r\n\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Delete application\r\n  async deleteApplication(id: string): Promise<{ message: string }> {\r\n    const response = await apiClient.delete(`/applications/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create new application with applicant data\r\n  async createApplicationWithApplicant(data: {\r\n    license_type_id: string;\r\n    license_category_id: string;\r\n    applicant_data: Record<string, any>;\r\n    user_id: string;\r\n  }): Promise<Application> {\r\n    try {\r\n      // Generate proper application number (format: APP-YYYYMMDD-HHMMSS-XXX)\r\n      const now = new Date();\r\n      const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');\r\n      const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');\r\n      const randomStr = Math.random().toString(36).substr(2, 3).toUpperCase();\r\n      const applicationNumber = `APP-${dateStr}-${timeStr}-${randomStr}`;\r\n\r\n      // Validate user_id is a proper UUID\r\n      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\r\n      if (!uuidRegex.test(data.user_id)) {\r\n        throw new Error(`Invalid user_id format: ${data.user_id}. Expected UUID format.`);\r\n      }\r\n\r\n      // Create application using user_id as applicant_id\r\n      // In most systems, the authenticated user is the applicant\r\n      const application = await this.createApplication({\r\n        application_number: applicationNumber,\r\n        applicant_id: data.user_id, // Use user_id as applicant_id\r\n        license_category_id: data.license_category_id,\r\n        current_step: 1,\r\n        progress_percentage: 0 // Start with 0% progress\r\n      });\r\n\r\n      return application;\r\n    } catch (error) {\r\n      console.error('Error creating application with applicant:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application section data\r\n  async saveApplicationSection(\r\n    applicationId: string,\r\n    sectionName: string,\r\n    sectionData: Record<string, any>\r\n  ): Promise<void> {\r\n    try {\r\n      // Try to save using form data service, but continue if it fails\r\n      let completedSections = 1; // At least one section is being saved\r\n\r\n      // Estimate progress based on section name\r\n      const sectionOrder = ['applicantInfo', 'companyProfile', 'businessInfo', 'serviceScope', 'businessPlan', 'legalHistory', 'reviewSubmit'];\r\n      const sectionIndex = sectionOrder.indexOf(sectionName);\r\n      completedSections = sectionIndex >= 0 ? sectionIndex + 1 : 1;\r\n\r\n      // Calculate progress based on completed sections (excluding reviewSubmit from total)\r\n      const totalSections = 6; // Total number of form sections (excluding reviewSubmit)\r\n      const progressPercentage = Math.min(Math.round((completedSections / totalSections) * 100), 100);\r\n\r\n      // Update the application progress\r\n      await this.updateApplication(applicationId, {\r\n        progress_percentage: progressPercentage,\r\n        current_step: completedSections\r\n      });\r\n\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get application section data\r\n  async getApplicationSection(applicationId: string, sectionName: string): Promise<any> {\r\n    try {\r\n      const response = await apiClient.get(`/applications/${applicationId}/sections/${sectionName}`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Submit application for review\r\n  async submitApplication(applicationId: string): Promise<Application> {\r\n    try {\r\n      // Update application status to submitted and set submission date\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        status: 'submitted',\r\n        submitted_at: new Date().toISOString(),\r\n        progress_percentage: 100,\r\n        current_step: 7\r\n      });\r\n\r\n      console.log('Application submitted successfully:', processApiResponse(response));\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error submitting application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get user's applications (filtered by authenticated user)\r\n  async getUserApplications(): Promise<Application[]> {\r\n    try {\r\n      // Use dedicated endpoint that explicitly filters by current user\r\n      const response = await apiClient.get('/applications/user-applications');\r\n      const processedResponse = processApiResponse(response);\r\n\r\n      // Handle paginated response structure\r\n      let applications = [];\r\n      if (processedResponse?.data) {\r\n        applications = Array.isArray(processedResponse.data) ? processedResponse.data : [];\r\n      } else if (Array.isArray(processedResponse)) {\r\n        applications = processedResponse;\r\n      } else if (processedResponse) {\r\n        // Single application or other structure\r\n        applications = [processedResponse];\r\n      }\r\n\r\n      return applications;\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application as draft\r\n  async saveAsDraft(applicationId: string, formData: Record<string, any>): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        form_data: formData,\r\n        status: 'draft'\r\n      });\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error saving application as draft:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Validate application before submission\r\n  async validateApplication(applicationId: string): Promise<{ isValid: boolean; errors: any[] }> {\r\n    try {\r\n      // Get data from entity-specific APIs for validation\r\n      let formData: Record<string, any> = {};\r\n\r\n      const errors: any[] = [];\r\n      const requiredSections = ['applicantInfo', 'companyProfile', 'businessInfo', 'legalHistory'];\r\n\r\n      // Check if all required sections are completed\r\n      for (const section of requiredSections) {\r\n        if (!formData[section] || Object.keys(formData[section]).length === 0) {\r\n          errors.push(`${section} section is incomplete`);\r\n        }\r\n      }\r\n\r\n      return {\r\n        isValid: errors.length === 0,\r\n        errors\r\n      };\r\n    } catch (error) {\r\n      console.error('Error validating application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update application status\r\n  async updateStatus(applicationId: string, status: string): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.patch(`/applications/${applicationId}/status`, { status });\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error updating application status:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Assign application to an officer\r\n  async assignApplication(applicationId: string, assignedTo: string): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.patch(`/applications/${applicationId}/assign`, { assignedTo });\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error assigning application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,MAAM,qBAAqB;IAChC,mDAAmD;IACnD,MAAM,iBAAgB,MAOrB;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,WAAW,YAAY,MAAM,CAAC,aAAa,OAAO,SAAS;QAEvE,cAAc;QACd,IAAI,QAAQ,SAAS,eAAe;YAClC,YAAY,MAAM,CAAC,2CAA2C,OAAO,OAAO,CAAC,aAAa;QAC5F;QACA,IAAI,QAAQ,SAAS,mBAAmB;YACtC,YAAY,MAAM,CAAC,8BAA8B,OAAO,OAAO,CAAC,iBAAiB;QACnF;QACA,IAAI,QAAQ,SAAS,QAAQ;YAC3B,YAAY,MAAM,CAAC,iBAAiB,OAAO,OAAO,CAAC,MAAM;QAC3D;QAEA,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,QAAQ,IAAI;QAC9E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8DAA8D;IAC9D,MAAM,8BAA6B,aAAqB,EAAE,MAKzD;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,iBAAiB,OAAO,MAAM;QAErE,kDAAkD;QAClD,YAAY,MAAM,CAAC,2CAA2C;QAE9D,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,QAAQ,IAAI;QAC9E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,+BAA+B;IAC/B,MAAM,gBAAe,EAAU;QAC7B,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI;QAC1D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gCAAgC;IAChC,MAAM,4BAA2B,WAAmB;QAClD,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,2BAA2B,EAAE,aAAa;QAChF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6BAA6B;IAC7B,MAAM,yBAAwB,MAAc;QAC1C,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,wBAAwB,EAAE,QAAQ;QACxE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,4BAA4B;IAC5B,MAAM,yBAAwB,EAAU,EAAE,MAAc;QACtD,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,GAAG,eAAe,EAAE,QAAQ;QAClF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8BAA8B;IAC9B,MAAM,2BAA0B,EAAU,EAAE,WAAmB,EAAE,kBAA0B;QACzF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAC,cAAc,EAAE,GAAG,sBAAsB,EAAE,YAAY,oBAAoB,EAAE,oBAAoB;QAEpG,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6BAA6B;IAC7B,MAAM;QACJ,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yBAAyB;IACzB,MAAM,mBAAkB,IAQvB;QACC,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,iBAAiB;YACvD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,kDAAkD;IAClD,MAAM,mBAAkB,EAAU,EAAE,IAA0B;QAC5D,IAAI;YACF,QAAQ,GAAG,CAAC,yBAAyB,IAAI,cAAc;YACvD,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI,EAAE,MAAM;gBAChE,SAAS;YACX;YACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,+BAA+B;YAE7C,8BAA8B;YAC9B,IAAI,MAAM,IAAI,KAAK,gBAAgB;gBACjC,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,WAAW;gBACjD,QAAQ,KAAK,CAAC,4BAA4B,MAAM,QAAQ,EAAE;gBAC1D,MAAM,IAAI,MAAM,CAAC,aAAa,EAAE,SAAS;YAC3C;YAEA,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM;QACR;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAkB,EAAU;QAChC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,IAAI;QAC7D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6CAA6C;IAC7C,MAAM,gCAA+B,IAKpC;QACC,IAAI;YACF,uEAAuE;YACvE,MAAM,MAAM,IAAI;YAChB,MAAM,UAAU,IAAI,WAAW,GAAG,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,MAAM;YAC7D,MAAM,UAAU,IAAI,YAAY,GAAG,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM;YAC7D,MAAM,YAAY,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,WAAW;YACrE,MAAM,oBAAoB,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,WAAW;YAElE,oCAAoC;YACpC,MAAM,YAAY;YAClB,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,OAAO,GAAG;gBACjC,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,KAAK,OAAO,CAAC,uBAAuB,CAAC;YAClF;YAEA,mDAAmD;YACnD,2DAA2D;YAC3D,MAAM,cAAc,MAAM,IAAI,CAAC,iBAAiB,CAAC;gBAC/C,oBAAoB;gBACpB,cAAc,KAAK,OAAO;gBAC1B,qBAAqB,KAAK,mBAAmB;gBAC7C,cAAc;gBACd,qBAAqB,EAAE,yBAAyB;YAClD;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,wBACJ,aAAqB,EACrB,WAAmB,EACnB,WAAgC;QAEhC,IAAI;YACF,gEAAgE;YAChE,IAAI,oBAAoB,GAAG,sCAAsC;YAEjE,0CAA0C;YAC1C,MAAM,eAAe;gBAAC;gBAAiB;gBAAkB;gBAAgB;gBAAgB;gBAAgB;gBAAgB;aAAe;YACxI,MAAM,eAAe,aAAa,OAAO,CAAC;YAC1C,oBAAoB,gBAAgB,IAAI,eAAe,IAAI;YAE3D,qFAAqF;YACrF,MAAM,gBAAgB,GAAG,yDAAyD;YAClF,MAAM,qBAAqB,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,AAAC,oBAAoB,gBAAiB,MAAM;YAE3F,kCAAkC;YAClC,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe;gBAC1C,qBAAqB;gBACrB,cAAc;YAChB;QAEF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,+BAA+B;IAC/B,MAAM,uBAAsB,aAAqB,EAAE,WAAmB;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,cAAc,UAAU,EAAE,aAAa;YAC7F,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,mBAAkB,aAAqB;QAC3C,IAAI;YACF,iEAAiE;YACjE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE;gBACrE,QAAQ;gBACR,cAAc,IAAI,OAAO,WAAW;gBACpC,qBAAqB;gBACrB,cAAc;YAChB;YAEA,QAAQ,GAAG,CAAC,uCAAuC,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YACtE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA,2DAA2D;IAC3D,MAAM;QACJ,IAAI;YACF,iEAAiE;YACjE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,MAAM,oBAAoB,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YAE7C,sCAAsC;YACtC,IAAI,eAAe,EAAE;YACrB,IAAI,mBAAmB,MAAM;gBAC3B,eAAe,MAAM,OAAO,CAAC,kBAAkB,IAAI,IAAI,kBAAkB,IAAI,GAAG,EAAE;YACpF,OAAO,IAAI,MAAM,OAAO,CAAC,oBAAoB;gBAC3C,eAAe;YACjB,OAAO,IAAI,mBAAmB;gBAC5B,wCAAwC;gBACxC,eAAe;oBAAC;iBAAkB;YACpC;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,4BAA4B;IAC5B,MAAM,aAAY,aAAqB,EAAE,QAA6B;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE;gBACrE,WAAW;gBACX,QAAQ;YACV;YACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,yCAAyC;IACzC,MAAM,qBAAoB,aAAqB;QAC7C,IAAI;YACF,oDAAoD;YACpD,IAAI,WAAgC,CAAC;YAErC,MAAM,SAAgB,EAAE;YACxB,MAAM,mBAAmB;gBAAC;gBAAiB;gBAAkB;gBAAgB;aAAe;YAE5F,+CAA+C;YAC/C,KAAK,MAAM,WAAW,iBAAkB;gBACtC,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG;oBACrE,OAAO,IAAI,CAAC,GAAG,QAAQ,sBAAsB,CAAC;gBAChD;YACF;YAEA,OAAO;gBACL,SAAS,OAAO,MAAM,KAAK;gBAC3B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA,4BAA4B;IAC5B,MAAM,cAAa,aAAqB,EAAE,MAAc;QACtD,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,KAAK,CAAC,CAAC,cAAc,EAAE,cAAc,OAAO,CAAC,EAAE;gBAAE;YAAO;YACzF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,mCAAmC;IACnC,MAAM,mBAAkB,aAAqB,EAAE,UAAkB;QAC/D,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,KAAK,CAAC,CAAC,cAAc,EAAE,cAAc,OAAO,CAAC,EAAE;gBAAE;YAAW;YAC7F,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 314, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/licenseCategoryService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\r\nimport { LicenseType } from './licenseTypeService';\r\nimport { cacheService, CACHE_KEYS, CACHE_TTL } from './cacheService';\r\n\r\n// Utility functions for category codes\r\nexport const generateCategoryCode = (name: string): string => {\r\n  return name\r\n    .toLowerCase()\r\n    .replace(/[^a-z0-9\\s]/g, '') // Remove special characters\r\n    .replace(/\\s+/g, '-') // Replace spaces with hyphens\r\n    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens\r\n    .substring(0, 50); // Limit length\r\n};\r\n\r\nexport const addCodesToCategories = (categories: LicenseCategory[]): LicenseCategory[] => {\r\n  return categories.map(category => ({\r\n    ...category,\r\n    code: generateCategoryCode(category.name),\r\n    children: category.children ? addCodesToCategories(category.children) : undefined\r\n  }));\r\n};\r\n\r\nexport const findCategoryByCode = (categories: LicenseCategory[], code: string): LicenseCategory | null => {\r\n  for (const category of categories) {\r\n    if (category.code === code) {\r\n      return category;\r\n    }\r\n    if (category.children) {\r\n      const found = findCategoryByCode(category.children, code);\r\n      if (found) return found;\r\n    }\r\n  }\r\n  return null;\r\n};\r\n\r\nexport const findCategoryById = (categories: LicenseCategory[], id: string): LicenseCategory | null => {\r\n  for (const category of categories) {\r\n    if (category.license_category_id === id) {\r\n      return category;\r\n    }\r\n    if (category.children) {\r\n      const found = findCategoryById(category.children, id);\r\n      if (found) return found;\r\n    }\r\n  }\r\n  return null;\r\n};\r\n\r\n// Types\r\nexport interface LicenseCategory {\r\n  license_category_id: string;\r\n  license_type_id: string;\r\n  parent_id?: string;\r\n  name: string;\r\n  fee: string;\r\n  description: string;\r\n  authorizes: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  created_by?: string;\r\n  updated_by?: string;\r\n  license_type?: LicenseType;\r\n  parent?: LicenseCategory;\r\n  children?: LicenseCategory[];\r\n  creator?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n  updater?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n  // Generated code for URL-friendly routing\r\n  code?: string;\r\n}\r\n\r\nexport interface CreateLicenseCategoryDto {\r\n  license_type_id: string;\r\n  parent_id?: string;\r\n  name: string;\r\n  fee: string;\r\n  description: string;\r\n  authorizes: string;\r\n}\r\n\r\nexport interface UpdateLicenseCategoryDto {\r\n  license_type_id?: string;\r\n  parent_id?: string;\r\n  name?: string;\r\n  fee?: string;\r\n  description?: string;\r\n  authorizes?: string;\r\n}\r\n\r\nexport interface PaginatedResponse<T> {\r\n  data: T[];\r\n  meta: {\r\n    itemsPerPage: number;\r\n    totalItems?: number;\r\n    currentPage?: number;\r\n    totalPages?: number;\r\n    sortBy: [string, string][];\r\n    searchBy: string[];\r\n    search: string;\r\n    select: string[];\r\n    filter?: Record<string, string | string[]>;\r\n  };\r\n  links: {\r\n    first?: string;\r\n    previous?: string;\r\n    current: string;\r\n    next?: string;\r\n    last?: string;\r\n  };\r\n}\r\n\r\nexport type LicenseCategoriesResponse = PaginatedResponse<LicenseCategory>;\r\n\r\nexport interface PaginateQuery {\r\n  page?: number;\r\n  limit?: number;\r\n  sortBy?: string[];\r\n  searchBy?: string[];\r\n  search?: string;\r\n  filter?: Record<string, string | string[]>;\r\n}\r\n\r\nexport const licenseCategoryService = {\r\n  // Get all license categories with pagination\r\n  async getLicenseCategories(query: PaginateQuery = {}): Promise<LicenseCategoriesResponse> {\r\n    const params = new URLSearchParams();\r\n\r\n    if (query.page) params.set('page', query.page.toString());\r\n    if (query.limit) params.set('limit', query.limit.toString());\r\n    if (query.search) params.set('search', query.search);\r\n    if (query.sortBy) {\r\n      query.sortBy.forEach(sort => params.append('sortBy', sort));\r\n    }\r\n    if (query.searchBy) {\r\n      query.searchBy.forEach(search => params.append('searchBy', search));\r\n    }\r\n    if (query.filter) {\r\n      Object.entries(query.filter).forEach(([key, value]) => {\r\n        if (Array.isArray(value)) {\r\n          value.forEach(v => params.append(`filter.${key}`, v));\r\n        } else {\r\n          params.set(`filter.${key}`, value);\r\n        }\r\n      });\r\n    }\r\n\r\n    const response = await apiClient.get(`/license-categories?${params.toString()}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Get license category by ID with timeout and retry handling\r\n  async getLicenseCategory(id: string): Promise<LicenseCategory> {\r\n    try {\r\n      const response = await apiClient.get(`/license-categories/${id}`, {\r\n        timeout: 30000, // 30 second timeout for individual requests\r\n      });\r\n      return response.data;\r\n    } catch (error: any) {\r\n      console.error('Error fetching license category:', error);\r\n      if (error.code === 'ECONNABORTED') {\r\n        throw new Error('Request timeout - please try again');\r\n      }\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get license categories by license type with improved error handling\r\n  async getLicenseCategoriesByType(licenseTypeId: string): Promise<any> {\r\n    try {\r\n      const response = await apiClient.get(`/license-categories/by-license-type/${licenseTypeId}`, {\r\n        timeout: 30000, // 30 second timeout\r\n      });\r\n      return response.data;\r\n    } catch (error: any) {\r\n      console.error('Error fetching license categories by type:', error);\r\n      if (error.code === 'ECONNABORTED') {\r\n        throw new Error('Request timeout - please try again');\r\n      }\r\n      if (error.response?.status === 429) {\r\n        throw new Error('Too many requests - please wait a moment and try again');\r\n      }\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Create new license category\r\n  async createLicenseCategory(licenseCategoryData: CreateLicenseCategoryDto): Promise<LicenseCategory> {\r\n    const response = await apiClient.post('/license-categories', licenseCategoryData);\r\n    return response.data;\r\n  },\r\n\r\n  // Update license category\r\n  async updateLicenseCategory(id: string, licenseCategoryData: UpdateLicenseCategoryDto): Promise<LicenseCategory> {\r\n    const response = await apiClient.put(`/license-categories/${id}`, licenseCategoryData);\r\n    return response.data;\r\n  },\r\n\r\n  // Delete license category\r\n  async deleteLicenseCategory(id: string): Promise<{ message: string }> {\r\n    const response = await apiClient.delete(`/license-categories/${id}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Get all license categories (simple list for dropdowns) with caching\r\n  async getAllLicenseCategories(): Promise<any> {\r\n    return cacheService.getOrSet(\r\n      CACHE_KEYS.LICENSE_CATEGORIES,\r\n      async () => {\r\n        console.log('Fetching license categories from API...');\r\n        // Reduce limit to avoid rate limiting\r\n        const response = await this.getLicenseCategories({ limit: 100 });\r\n        return addCodesToCategories(response.data);\r\n      },\r\n      CACHE_TTL.LONG // Cache for 15 minutes\r\n    );\r\n  },\r\n\r\n  // Get hierarchical tree of categories for a license type with caching\r\n  async getCategoryTree(licenseTypeId: string): Promise<LicenseCategory[]> {\r\n    return cacheService.getOrSet(\r\n      `category-tree-${licenseTypeId}`,\r\n      async () => {\r\n        console.log(`Fetching category tree for license type: ${licenseTypeId}`);\r\n        const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/tree`);\r\n        return addCodesToCategories(response.data);\r\n      },\r\n      CACHE_TTL.MEDIUM // Cache for 5 minutes\r\n    );\r\n  },\r\n\r\n  // Get root categories (no parent) for a license type with caching\r\n  async getRootCategories(licenseTypeId: string): Promise<LicenseCategory[]> {\r\n    return cacheService.getOrSet(\r\n      `root-categories-${licenseTypeId}`,\r\n      async () => {\r\n        console.log(`Fetching root categories for license type: ${licenseTypeId}`);\r\n        const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/root`);\r\n        return response.data;\r\n      },\r\n      CACHE_TTL.MEDIUM // Cache for 5 minutes\r\n    );\r\n  },\r\n\r\n  // Get license categories for parent selection dropdown\r\n  async getCategoriesForParentSelection(licenseTypeId: string, excludeId?: string): Promise<LicenseCategory[]> {\r\n    try {\r\n      const params = excludeId ? { excludeId } : {};\r\n      console.log('🚀 Fetching parent categories for license type:', licenseTypeId, 'with params:', params);\r\n\r\n      // Try the new endpoint first\r\n      try {\r\n        const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/for-parent-selection`, { params });\r\n\r\n\r\n        if (response.data && Array.isArray(response.data.data)) {\r\n          console.log('✅ Valid array response with', response.data.data.length, 'items')\r\n          return response.data.data;\r\n        } else {\r\n          console.warn('⚠️ API returned non-array data:', response.data);\r\n          return [];\r\n        }\r\n      } catch (newEndpointError) {\r\n        console.warn('🔄 New endpoint failed, trying fallback:', newEndpointError);\r\n\r\n        // Fallback to existing endpoint\r\n        const response = await apiClient.get(`/license-categories/by-license-type/${licenseTypeId}`);\r\n        console.log('🔄 Fallback response:', response.data);\r\n\r\n        if (response.data && Array.isArray(response.data)) {\r\n          // Filter out the excluded category if specified\r\n          let categories = response.data;\r\n          if (excludeId) {\r\n            categories = categories.filter(cat => cat.license_category_id !== excludeId);\r\n          }\r\n          console.log('✅ Fallback successful with', categories.length, 'items');\r\n          return categories;\r\n        } else {\r\n          console.warn('⚠️ Fallback also returned non-array data:', response.data);\r\n          return [];\r\n        }\r\n      }\r\n    } catch (error) {\r\n\r\n      return [];\r\n    }\r\n  },\r\n\r\n  // Get potential parent categories for a license type\r\n  async getPotentialParents(licenseTypeId: string, excludeId?: string): Promise<LicenseCategory[]> {\r\n    const params = excludeId ? { excludeId } : {};\r\n    const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/potential-parents`, { params });\r\n    return response.data;\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AAEA;;;AAGO,MAAM,uBAAuB,CAAC;IACnC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,gBAAgB,IAAI,4BAA4B;KACxD,OAAO,CAAC,QAAQ,KAAK,8BAA8B;KACnD,OAAO,CAAC,YAAY,IAAI,kCAAkC;KAC1D,SAAS,CAAC,GAAG,KAAK,eAAe;AACtC;AAEO,MAAM,uBAAuB,CAAC;IACnC,OAAO,WAAW,GAAG,CAAC,CAAA,WAAY,CAAC;YACjC,GAAG,QAAQ;YACX,MAAM,qBAAqB,SAAS,IAAI;YACxC,UAAU,SAAS,QAAQ,GAAG,qBAAqB,SAAS,QAAQ,IAAI;QAC1E,CAAC;AACH;AAEO,MAAM,qBAAqB,CAAC,YAA+B;IAChE,KAAK,MAAM,YAAY,WAAY;QACjC,IAAI,SAAS,IAAI,KAAK,MAAM;YAC1B,OAAO;QACT;QACA,IAAI,SAAS,QAAQ,EAAE;YACrB,MAAM,QAAQ,mBAAmB,SAAS,QAAQ,EAAE;YACpD,IAAI,OAAO,OAAO;QACpB;IACF;IACA,OAAO;AACT;AAEO,MAAM,mBAAmB,CAAC,YAA+B;IAC9D,KAAK,MAAM,YAAY,WAAY;QACjC,IAAI,SAAS,mBAAmB,KAAK,IAAI;YACvC,OAAO;QACT;QACA,IAAI,SAAS,QAAQ,EAAE;YACrB,MAAM,QAAQ,iBAAiB,SAAS,QAAQ,EAAE;YAClD,IAAI,OAAO,OAAO;QACpB;IACF;IACA,OAAO;AACT;AAqFO,MAAM,yBAAyB;IACpC,6CAA6C;IAC7C,MAAM,sBAAqB,QAAuB,CAAC,CAAC;QAClD,MAAM,SAAS,IAAI;QAEnB,IAAI,MAAM,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ;QACtD,IAAI,MAAM,KAAK,EAAE,OAAO,GAAG,CAAC,SAAS,MAAM,KAAK,CAAC,QAAQ;QACzD,IAAI,MAAM,MAAM,EAAE,OAAO,GAAG,CAAC,UAAU,MAAM,MAAM;QACnD,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,MAAM,CAAC,OAAO,CAAC,CAAA,OAAQ,OAAO,MAAM,CAAC,UAAU;QACvD;QACA,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,YAAY;QAC7D;QACA,IAAI,MAAM,MAAM,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAChD,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,OAAO,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBACpD,OAAO;oBACL,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBAC9B;YACF;QACF;QAEA,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,OAAO,QAAQ,IAAI;QAC/E,OAAO,SAAS,IAAI;IACtB;IAEA,6DAA6D;IAC7D,MAAM,oBAAmB,EAAU;QACjC,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,IAAI,EAAE;gBAChE,SAAS;YACX;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,oCAAoC;YAClD,IAAI,MAAM,IAAI,KAAK,gBAAgB;gBACjC,MAAM,IAAI,MAAM;YAClB;YACA,MAAM;QACR;IACF;IAEA,sEAAsE;IACtE,MAAM,4BAA2B,aAAqB;QACpD,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oCAAoC,EAAE,eAAe,EAAE;gBAC3F,SAAS;YACX;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,IAAI,MAAM,IAAI,KAAK,gBAAgB;gBACjC,MAAM,IAAI,MAAM;YAClB;YACA,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,MAAM,IAAI,MAAM;YAClB;YACA,MAAM;QACR;IACF;IAEA,8BAA8B;IAC9B,MAAM,uBAAsB,mBAA6C;QACvE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,MAAM,uBAAsB,EAAU,EAAE,mBAA6C;QACnF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,IAAI,EAAE;QAClE,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,MAAM,uBAAsB,EAAU;QACpC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,oBAAoB,EAAE,IAAI;QACnE,OAAO,SAAS,IAAI;IACtB;IAEA,sEAAsE;IACtE,MAAM;QACJ,OAAO,+HAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,+HAAA,CAAA,aAAU,CAAC,kBAAkB,EAC7B;YACE,QAAQ,GAAG,CAAC;YACZ,sCAAsC;YACtC,MAAM,WAAW,MAAM,IAAI,CAAC,oBAAoB,CAAC;gBAAE,OAAO;YAAI;YAC9D,OAAO,qBAAqB,SAAS,IAAI;QAC3C,GACA,+HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;;IAE1C;IAEA,sEAAsE;IACtE,MAAM,iBAAgB,aAAqB;QACzC,OAAO,+HAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,CAAC,cAAc,EAAE,eAAe,EAChC;YACE,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,eAAe;YACvE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,KAAK,CAAC;YAC7F,OAAO,qBAAqB,SAAS,IAAI;QAC3C,GACA,+HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,sBAAsB;;IAE3C;IAEA,kEAAkE;IAClE,MAAM,mBAAkB,aAAqB;QAC3C,OAAO,+HAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,CAAC,gBAAgB,EAAE,eAAe,EAClC;YACE,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,eAAe;YACzE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,KAAK,CAAC;YAC7F,OAAO,SAAS,IAAI;QACtB,GACA,+HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,sBAAsB;;IAE3C;IAEA,uDAAuD;IACvD,MAAM,iCAAgC,aAAqB,EAAE,SAAkB;QAC7E,IAAI;YACF,MAAM,SAAS,YAAY;gBAAE;YAAU,IAAI,CAAC;YAC5C,QAAQ,GAAG,CAAC,mDAAmD,eAAe,gBAAgB;YAE9F,6BAA6B;YAC7B,IAAI;gBACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,qBAAqB,CAAC,EAAE;oBAAE;gBAAO;gBAGxH,IAAI,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,GAAG;oBACtD,QAAQ,GAAG,CAAC,+BAA+B,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;oBACtE,OAAO,SAAS,IAAI,CAAC,IAAI;gBAC3B,OAAO;oBACL,QAAQ,IAAI,CAAC,mCAAmC,SAAS,IAAI;oBAC7D,OAAO,EAAE;gBACX;YACF,EAAE,OAAO,kBAAkB;gBACzB,QAAQ,IAAI,CAAC,4CAA4C;gBAEzD,gCAAgC;gBAChC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oCAAoC,EAAE,eAAe;gBAC3F,QAAQ,GAAG,CAAC,yBAAyB,SAAS,IAAI;gBAElD,IAAI,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;oBACjD,gDAAgD;oBAChD,IAAI,aAAa,SAAS,IAAI;oBAC9B,IAAI,WAAW;wBACb,aAAa,WAAW,MAAM,CAAC,CAAA,MAAO,IAAI,mBAAmB,KAAK;oBACpE;oBACA,QAAQ,GAAG,CAAC,8BAA8B,WAAW,MAAM,EAAE;oBAC7D,OAAO;gBACT,OAAO;oBACL,QAAQ,IAAI,CAAC,6CAA6C,SAAS,IAAI;oBACvE,OAAO,EAAE;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YAEd,OAAO,EAAE;QACX;IACF;IAEA,qDAAqD;IACrD,MAAM,qBAAoB,aAAqB,EAAE,SAAkB;QACjE,MAAM,SAAS,YAAY;YAAE;QAAU,IAAI,CAAC;QAC5C,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,kBAAkB,CAAC,EAAE;YAAE;QAAO;QACrH,OAAO,SAAS,IAAI;IACtB;AACF", "debugId": null}}, {"offset": {"line": 523, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/task-assignment.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\nimport { PaginateQuery } from './licenseTypeService';\r\n\r\n// Task enums matching backend\r\nexport enum TaskType {\r\n  APPLICATION = 'application',\r\n  COMPLAINT = 'complaint',\r\n  DATA_BREACH = 'data_breach',\r\n  EVALUATION = 'evaluation',\r\n  INSPECTION = 'inspection',\r\n  DOCUMENT_REVIEW = 'document_review',\r\n  COMPLIANCE_CHECK = 'compliance_check',\r\n  FOLLOW_UP = 'follow_up',\r\n}\r\n\r\nexport enum TaskStatus {\r\n  PENDING = 'pending',\r\n  IN_PROGRESS = 'in_progress',\r\n  COMPLETED = 'completed',\r\n  CANCELLED = 'cancelled',\r\n  ON_HOLD = 'on_hold',\r\n}\r\n\r\nexport enum TaskPriority {\r\n  LOW = 'low',\r\n  MEDIUM = 'medium',\r\n  HIGH = 'high',\r\n  URGENT = 'urgent',\r\n}\r\n\r\n// User interface for task assignments\r\nexport interface TaskUser {\r\n  user_id: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  email: string;\r\n}\r\n\r\n// Main Task interface matching backend entity\r\nexport interface Task {\r\n  task_id: string;\r\n  task_number: string;\r\n  title: string;\r\n  description: string;\r\n  task_type: TaskType;\r\n  status: TaskStatus;\r\n  priority: TaskPriority;\r\n  entity_type?: string;\r\n  entity_id?: string;\r\n  assigned_to?: string;\r\n  assigned_by: string;\r\n  assigned_at?: string;\r\n  due_date?: string;\r\n  completed_at?: string;\r\n  review?: string;\r\n  review_notes?: string;\r\n  completion_notes?: string;\r\n  created_at: string;\r\n  created_by: string;\r\n  updated_at: string;\r\n  updated_by?: string;\r\n  deleted_at?: string;\r\n  assignee?: TaskUser;\r\n  assigner: TaskUser;\r\n  creator: TaskUser;\r\n  updater?: TaskUser;\r\n}\r\n\r\n// Legacy interface for backward compatibility\r\nexport interface GenericTask extends Task {}\r\n\r\n// Legacy interface for backward compatibility\r\nexport interface TaskAssignmentApplication {\r\n  application_id: string;\r\n  application_number: string;\r\n  status: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  applicant: {\r\n    applicant_id: string;\r\n    company_name: string;\r\n    first_name: string;\r\n    last_name: string;\r\n  };\r\n  license_category: {\r\n    license_category_id: string;\r\n    name: string;\r\n    license_type: {\r\n      license_type_id: string;\r\n      name: string;\r\n    };\r\n  };\r\n  assignee?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n  assigned_at?: string;\r\n}\r\n\r\nexport interface TaskAssignmentOfficer {\r\n  user_id: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  email: string;\r\n  department?: string;\r\n}\r\n\r\n// DTOs for task operations\r\nexport interface CreateTaskDto {\r\n  task_type: TaskType;\r\n  title: string;\r\n  description: string;\r\n  priority?: TaskPriority;\r\n  status?: TaskStatus;\r\n  entity_type?: string;\r\n  entity_id?: string;\r\n  due_date?: string;\r\n  assigned_to?: string;\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\nexport interface UpdateTaskDto {\r\n  title?: string;\r\n  description?: string;\r\n  priority?: TaskPriority;\r\n  status?: TaskStatus;\r\n  entity_type?: string;\r\n  entity_id?: string;\r\n  due_date?: string;\r\n  review?: string;\r\n  review_notes?: string;\r\n  completion_notes?: string;\r\n}\r\n\r\nexport interface AssignTaskDto {\r\n  assignedTo: string;\r\n  comment?: string;\r\n}\r\n\r\nexport interface TaskFilters {\r\n  task_type?: string;\r\n  status?: string;\r\n  priority?: string;\r\n  assigned_to?: string;\r\n  created_by?: string;\r\n  assignment_status?: string;\r\n}\r\n\r\nexport interface TaskStats {\r\n  total: number;\r\n  pending: number;\r\n  in_progress: number;\r\n  completed: number;\r\n  cancelled: number;\r\n  on_hold: number;\r\n  unassigned: number;\r\n  assigned: number;\r\n  overdue: number;\r\n}\r\n\r\n// Legacy interfaces for backward compatibility\r\nexport interface AssignApplicationRequest {\r\n  assignedTo: string;\r\n}\r\n\r\nexport interface PaginatedResponse<T> {\r\n  data: T[];\r\n  meta: {\r\n    itemCount: number;\r\n    totalItems: number;\r\n    itemsPerPage: number;\r\n    totalPages: number;\r\n    currentPage: number;\r\n  };\r\n  links: {\r\n    first: string;\r\n    previous: string;\r\n    next: string;\r\n    last: string;\r\n  };\r\n}\r\n\r\n// Import types from userService for compatibility\r\nexport type { PaginateQuery } from '../services/userService';\r\n\r\nexport const taskService = {\r\n  // Main task CRUD operations\r\n  getTasks: async (params?: PaginateQuery & TaskFilters): Promise<PaginatedResponse<Task>> => {\r\n    // Handle assignment_status filter by using different endpoints or filters\r\n    if (params?.assignment_status === 'unassigned') {\r\n      // Use the unassigned tasks endpoint\r\n      return taskService.getUnassignedTasks(params);\r\n    } else if (params?.assignment_status === 'assigned') {\r\n      // Use the assigned tasks endpoint instead of recursive call\r\n      return taskService.getAssignedTasks(params);\r\n    }\r\n\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n    if (params?.task_type) searchParams.append('task_type', params.task_type);\r\n    if (params?.status) searchParams.append('status', params.status);\r\n    if (params?.priority) searchParams.append('priority', params.priority);\r\n    if (params?.assigned_to) searchParams.append('assigned_to', params.assigned_to);\r\n    if (params?.created_by) searchParams.append('created_by', params.created_by);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/tasks${queryString ? `?${queryString}` : ''}`;\r\n\r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  getUnassignedTasks: async (params?: PaginateQuery & TaskFilters): Promise<PaginatedResponse<Task>> => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n    if (params?.task_type) searchParams.append('task_type', params.task_type);\r\n    if (params?.status) searchParams.append('status', params.status);\r\n    if (params?.priority) searchParams.append('priority', params.priority);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/tasks/unassigned${queryString ? `?${queryString}` : ''}`;\r\n\r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  getAssignedTasks: async (params?: PaginateQuery & TaskFilters): Promise<PaginatedResponse<Task>> => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n    if (params?.task_type) searchParams.append('task_type', params.task_type);\r\n    if (params?.status) searchParams.append('status', params.status);\r\n    if (params?.priority) searchParams.append('priority', params.priority);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/tasks/assigned${queryString ? `?${queryString}` : ''}`;\r\n\r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  getMyTasks: async (params?: PaginateQuery & TaskFilters): Promise<PaginatedResponse<Task>> => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n    if (params?.task_type) searchParams.append('task_type', params.task_type);\r\n    if (params?.status) searchParams.append('status', params.status);\r\n    if (params?.priority) searchParams.append('priority', params.priority);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/tasks/assigned/me${queryString ? `?${queryString}` : ''}`;\r\n\r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  getTaskStats: async (): Promise<TaskStats> => {\r\n    const response = await apiClient.get('/tasks/stats');\r\n    return response.data;\r\n  },\r\n\r\n  getTask: async (taskId: string): Promise<Task> => {\r\n    const response = await apiClient.get(`/tasks/${taskId}`);\r\n    return response.data;\r\n  },\r\n\r\n  createTask: async (taskData: CreateTaskDto): Promise<Task> => {\r\n    const response = await apiClient.post('/tasks', taskData);\r\n    return response.data;\r\n  },\r\n\r\n  updateTask: async (taskId: string, taskData: UpdateTaskDto): Promise<Task> => {\r\n    const response = await apiClient.patch(`/tasks/${taskId}`, taskData);\r\n    return response.data;\r\n  },\r\n\r\n  assignTask: async (taskId: string, assignData: AssignTaskDto): Promise<Task> => {\r\n    const response = await apiClient.put(`/tasks/${taskId}/assign`, assignData);\r\n    return response.data;\r\n  },\r\n\r\n  reassignTask: async (taskId: string, assignData: AssignTaskDto): Promise<Task> => {\r\n    const response = await apiClient.put(`/tasks/${taskId}/reassign`, assignData);\r\n    return response.data;\r\n  },\r\n\r\n  deleteTask: async (taskId: string): Promise<void> => {\r\n    await apiClient.delete(`/tasks/${taskId}`);\r\n  },\r\n\r\n  // Get users for assignment (officers only, exclude customers)\r\n  getUsers: async (): Promise<PaginatedResponse<TaskUser>> => {\r\n    try {\r\n      const response = await apiClient.get('/users', {\r\n        params: {\r\n          limit: 100,\r\n          filter: {\r\n            exclude_customers: true\r\n          }\r\n        }\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error fetching users:', error);\r\n      return {\r\n        data: [],\r\n        meta: {\r\n          itemCount: 0,\r\n          totalItems: 0,\r\n          itemsPerPage: 10,\r\n          totalPages: 0,\r\n          currentPage: 1,\r\n        },\r\n        links: {\r\n          first: '',\r\n          previous: '',\r\n          next: '',\r\n          last: '',\r\n        },\r\n      };\r\n    }\r\n  },\r\n\r\n  // Get officers specifically (non-customer users)\r\n  getOfficers: async (): Promise<PaginatedResponse<any>> => {\r\n    try {\r\n      const response = await apiClient.get('/users/list/officers');\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error fetching officers:', error);\r\n      // Fallback to regular users endpoint with filtering\r\n      try {\r\n        const fallbackResponse = await apiClient.get('/users', {\r\n          params: {\r\n            limit: 100,\r\n            filter: {\r\n              exclude_customers: true\r\n            }\r\n          }\r\n        });\r\n        return processApiResponse(fallbackResponse);\r\n      } catch (fallbackError) {\r\n        console.error('Error fetching users as fallback:', fallbackError);\r\n        return {\r\n          data: [],\r\n          meta: {\r\n            itemCount: 0,\r\n            totalItems: 0,\r\n            itemsPerPage: 10,\r\n            totalPages: 0,\r\n            currentPage: 1,\r\n          },\r\n          links: {\r\n            first: '',\r\n            previous: '',\r\n            next: '',\r\n            last: '',\r\n          },\r\n        };\r\n      }\r\n    }\r\n  },\r\n};\r\n\r\n// Legacy service for backward compatibility\r\nexport const taskAssignmentService = {\r\n  // Generic task management methods\r\n  getUnassignedTasks: taskService.getUnassignedTasks,\r\n  getAssignedTasks: taskService.getAssignedTasks,\r\n  assignTask: taskService.assignTask,\r\n  getTaskById: taskService.getTask,\r\n\r\n  // Legacy application-specific methods (for backward compatibility)\r\n  getUnassignedApplications: async (params?: { page?: number; limit?: number; search?: string }) => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/applications/unassigned${queryString ? `?${queryString}` : ''}`;\r\n    \r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  // Get all applications (including assigned)\r\n  getAllApplications: async (params?: { page?: number; limit?: number; search?: string }) => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/applications${queryString ? `?${queryString}` : ''}`;\r\n    \r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  // Get applications assigned to current user\r\n  getMyAssignedApplications: async (params?: { page?: number; limit?: number; search?: string }) => {\r\n    const searchParams = new URLSearchParams();\r\n    if (params?.page) searchParams.append('page', params.page.toString());\r\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\r\n    if (params?.search) searchParams.append('search', params.search);\r\n\r\n    const queryString = searchParams.toString();\r\n    const url = `/applications/assigned/me${queryString ? `?${queryString}` : ''}`;\r\n    \r\n    const response = await apiClient.get(url);\r\n    return response.data;\r\n  },\r\n\r\n  // Get officers for assignment\r\n  getOfficers: async () => {\r\n    try {\r\n      const response = await apiClient.get('/users');\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error fetching officers:', error);\r\n      return { data: [] };\r\n    }\r\n  },\r\n\r\n  // Assign application to officer\r\n  assignApplication: async (applicationId: string, assignData: AssignApplicationRequest) => {\r\n    const response = await apiClient.put(`/applications/${applicationId}/assign`, assignData);\r\n    return response.data;\r\n  },\r\n\r\n  // Get application details\r\n  getApplication: async (applicationId: string) => {\r\n    const response = await apiClient.get(`/applications/${applicationId}`);\r\n    return response.data;\r\n  },\r\n};"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAIO,IAAA,AAAK,kCAAA;;;;;;;;;WAAA;;AAWL,IAAA,AAAK,oCAAA;;;;;;WAAA;;AAQL,IAAA,AAAK,sCAAA;;;;;WAAA;;AAoKL,MAAM,cAAc;IACzB,4BAA4B;IAC5B,UAAU,OAAO;QACf,0EAA0E;QAC1E,IAAI,QAAQ,sBAAsB,cAAc;YAC9C,oCAAoC;YACpC,OAAO,YAAY,kBAAkB,CAAC;QACxC,OAAO,IAAI,QAAQ,sBAAsB,YAAY;YACnD,4DAA4D;YAC5D,OAAO,YAAY,gBAAgB,CAAC;QACtC;QAEA,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAC/D,IAAI,QAAQ,WAAW,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QACxE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAC/D,IAAI,QAAQ,UAAU,aAAa,MAAM,CAAC,YAAY,OAAO,QAAQ;QACrE,IAAI,QAAQ,aAAa,aAAa,MAAM,CAAC,eAAe,OAAO,WAAW;QAC9E,IAAI,QAAQ,YAAY,aAAa,MAAM,CAAC,cAAc,OAAO,UAAU;QAE3E,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAE3D,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,oBAAoB,OAAO;QACzB,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAC/D,IAAI,QAAQ,WAAW,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QACxE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAC/D,IAAI,QAAQ,UAAU,aAAa,MAAM,CAAC,YAAY,OAAO,QAAQ;QAErE,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAEtE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB,OAAO;QACvB,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAC/D,IAAI,QAAQ,WAAW,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QACxE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAC/D,IAAI,QAAQ,UAAU,aAAa,MAAM,CAAC,YAAY,OAAO,QAAQ;QAErE,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAEpE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY,OAAO;QACjB,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAC/D,IAAI,QAAQ,WAAW,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QACxE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAC/D,IAAI,QAAQ,UAAU,aAAa,MAAM,CAAC,YAAY,OAAO,QAAQ;QAErE,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAEvE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;QACZ,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACvD,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,UAAU;QAChD,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QAC3D,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,OAAO,CAAC,EAAE;QAChE,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc,OAAO,QAAgB;QACnC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,SAAS,CAAC,EAAE;QAClE,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY,OAAO;QACjB,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ;IAC3C;IAEA,8DAA8D;IAC9D,UAAU;QACR,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,UAAU;gBAC7C,QAAQ;oBACN,OAAO;oBACP,QAAQ;wBACN,mBAAmB;oBACrB;gBACF;YACF;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;gBACL,MAAM,EAAE;gBACR,MAAM;oBACJ,WAAW;oBACX,YAAY;oBACZ,cAAc;oBACd,YAAY;oBACZ,aAAa;gBACf;gBACA,OAAO;oBACL,OAAO;oBACP,UAAU;oBACV,MAAM;oBACN,MAAM;gBACR;YACF;QACF;IACF;IAEA,iDAAiD;IACjD,aAAa;QACX,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,oDAAoD;YACpD,IAAI;gBACF,MAAM,mBAAmB,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,UAAU;oBACrD,QAAQ;wBACN,OAAO;wBACP,QAAQ;4BACN,mBAAmB;wBACrB;oBACF;gBACF;gBACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YAC5B,EAAE,OAAO,eAAe;gBACtB,QAAQ,KAAK,CAAC,qCAAqC;gBACnD,OAAO;oBACL,MAAM,EAAE;oBACR,MAAM;wBACJ,WAAW;wBACX,YAAY;wBACZ,cAAc;wBACd,YAAY;wBACZ,aAAa;oBACf;oBACA,OAAO;wBACL,OAAO;wBACP,UAAU;wBACV,MAAM;wBACN,MAAM;oBACR;gBACF;YACF;QACF;IACF;AACF;AAGO,MAAM,wBAAwB;IACnC,kCAAkC;IAClC,oBAAoB,YAAY,kBAAkB;IAClD,kBAAkB,YAAY,gBAAgB;IAC9C,YAAY,YAAY,UAAU;IAClC,aAAa,YAAY,OAAO;IAEhC,mEAAmE;IACnE,2BAA2B,OAAO;QAChC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,wBAAwB,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAE7E,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,4CAA4C;IAC5C,oBAAoB,OAAO;QACzB,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAElE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,4CAA4C;IAC5C,2BAA2B,OAAO;QAChC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,yBAAyB,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAE9E,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,8BAA8B;IAC9B,aAAa;QACX,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;gBAAE,MAAM,EAAE;YAAC;QACpB;IACF;IAEA,gCAAgC;IAChC,mBAAmB,OAAO,eAAuB;QAC/C,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,cAAc,OAAO,CAAC,EAAE;QAC9E,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe;QACrE,OAAO,SAAS,IAAI;IACtB;AACF", "debugId": null}}, {"offset": {"line": 791, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/AssignModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport { taskService, TaskType, TaskPriority, TaskStatus, Task } from '@/services/task-assignment';\r\nimport { applicationService } from '@/services/applicationService';\r\n\r\ninterface Officer {\r\n  user_id: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  email: string;\r\n  department?: string;\r\n}\r\n\r\ninterface AssignModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  itemId: string | null;\r\n  itemType: 'data_breach' | 'application' | 'complaint' | 'inspection' | 'document_review' | 'task';\r\n  itemTitle?: string;\r\n  onAssignSuccess?: () => void;\r\n  // Reassignment mode props\r\n  mode?: 'assign' | 'reassign';\r\n  task?: Task | null; // For reassignment mode\r\n  onReassignSuccess?: () => void;\r\n}\r\n\r\nconst AssignModal: React.FC<AssignModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  itemId,\r\n  itemType,\r\n  itemTitle,\r\n  onAssignSuccess,\r\n  mode = 'assign',\r\n  task,\r\n  onReassignSuccess\r\n}) => {\r\n  const { showSuccess, showError } = useToast();\r\n  const [officers, setOfficers] = useState<Officer[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [assigning, setAssigning] = useState(false);\r\n  const [selectedOfficer, setSelectedOfficer] = useState<string>('');\r\n  const [comment, setComment] = useState('');\r\n  const [dueDate, setDueDate] = useState<string>('');\r\n\r\n  const isReassignMode = mode === 'reassign' && task;\r\n\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      fetchOfficers();\r\n      // For reassignment, pre-select the current assignee\r\n      setSelectedOfficer(isReassignMode ? (task?.assigned_to || '') : '');\r\n      setComment('');\r\n      // For reassignment, pre-fill the current due date if available\r\n      setDueDate(isReassignMode && task?.due_date ? task.due_date.split('T')[0] : '');\r\n    }\r\n  }, [isOpen, isReassignMode, task]);\r\n\r\n  const fetchOfficers = async () => {\r\n    setLoading(true);\r\n    try {\r\n      const response = await taskService.getOfficers();\r\n      const officersData = response.data || [];\r\n      setOfficers(officersData);\r\n\r\n      if (officersData.length === 0) {\r\n        console.warn('No officers found for task assignment');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching officers:', error);\r\n      setOfficers([]);\r\n      showError('Failed to load officers. Please try again.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const getTaskTypeFromItemType = (itemType: string): TaskType => {\r\n    switch (itemType) {\r\n      case 'application':\r\n        return TaskType.EVALUATION;\r\n      case 'data_breach':\r\n        return TaskType.DATA_BREACH;\r\n      case 'complaint':\r\n        return TaskType.COMPLAINT;\r\n      case 'inspection':\r\n        return TaskType.INSPECTION;\r\n      case 'document_review':\r\n        return TaskType.DOCUMENT_REVIEW;\r\n      case 'task':\r\n        return TaskType.APPLICATION; // Default for existing tasks\r\n      default:\r\n        return TaskType.APPLICATION;\r\n    }\r\n  };\r\n\r\n  const getTaskTitle = (itemType: string, itemTitle?: string): string => {\r\n    const baseTitle = itemTitle || 'Untitled';\r\n    switch (itemType) {\r\n      case 'application':\r\n        return `Application Evaluation: ${baseTitle}`;\r\n      case 'data_breach':\r\n        return `Data Breach Investigation: ${baseTitle}`;\r\n      case 'complaint':\r\n        return `Complaint Review: ${baseTitle}`;\r\n      case 'inspection':\r\n        return `Inspection Task: ${baseTitle}`;\r\n      case 'document_review':\r\n        return `Document Review: ${baseTitle}`;\r\n      default:\r\n        return `Task: ${baseTitle}`;\r\n    }\r\n  };\r\n\r\n  const getTaskDescription = (itemType: string, itemTitle?: string): string => {\r\n    const baseTitle = itemTitle || 'item';\r\n    switch (itemType) {\r\n      case 'application':\r\n        return `Evaluate and review application ${baseTitle} for compliance and approval.`;\r\n      case 'data_breach':\r\n        return `Investigate and assess data breach report ${baseTitle} for regulatory compliance.`;\r\n      case 'complaint':\r\n        return `Review and resolve complaint ${baseTitle} according to regulatory procedures.`;\r\n      case 'inspection':\r\n        return `Conduct inspection for ${baseTitle} to ensure regulatory compliance.`;\r\n      case 'document_review':\r\n        return `Review and validate document ${baseTitle} for accuracy and compliance.`;\r\n      default:\r\n        return `Process and review ${baseTitle}.`;\r\n    }\r\n  };\r\n\r\n  const handleAssign = async () => {\r\n    if (!selectedOfficer) {\r\n      showError('Please select an officer');\r\n      return;\r\n    }\r\n\r\n    if (!dueDate) {\r\n      showError('Please select a due date');\r\n      return;\r\n    }\r\n\r\n    if (isReassignMode && !task) {\r\n      showError('Task information is missing');\r\n      return;\r\n    }\r\n\r\n    if (!isReassignMode && !itemId) {\r\n      showError('Item ID is missing');\r\n      return;\r\n    }\r\n\r\n    setAssigning(true);\r\n    try {\r\n      if (isReassignMode && task) {\r\n        // Reassign existing task\r\n        await taskService.reassignTask(task.task_id, {\r\n          assignedTo: selectedOfficer,\r\n          comment: comment.trim() || undefined\r\n        });\r\n\r\n        // Update due date if provided\r\n        if (dueDate) {\r\n          await taskService.updateTask(task.task_id, {\r\n            due_date: dueDate\r\n          });\r\n        }\r\n\r\n        showSuccess('Task reassigned successfully');\r\n        onReassignSuccess?.();\r\n      } else {\r\n        // Create a new polymorphic task based on item type\r\n        const taskData = {\r\n          task_type: getTaskTypeFromItemType(itemType),\r\n          title: getTaskTitle(itemType, itemTitle),\r\n          description: getTaskDescription(itemType, itemTitle),\r\n          priority: TaskPriority.MEDIUM,\r\n          status: TaskStatus.PENDING,\r\n          entity_type: itemType,\r\n          entity_id: itemId!,\r\n          assigned_to: selectedOfficer,\r\n          due_date: dueDate,\r\n          metadata: {\r\n            comment: comment.trim() || undefined,\r\n            original_item_title: itemTitle,\r\n            assignment_context: 'manual_assignment'\r\n          }\r\n        };\r\n\r\n        await taskService.createTask(taskData);\r\n\r\n        // If this is an application, update its status to evaluation and assign it\r\n        if (itemType === 'application') {\r\n          try {\r\n            // First assign the application to the officer\r\n            await applicationService.assignApplication(itemId!, selectedOfficer);\r\n            // Then update its status to evaluation\r\n            await applicationService.updateStatus(itemId!, 'evaluation');\r\n          } catch (statusError) {\r\n            console.warn('Task created but failed to update application status/assignment:', statusError);\r\n            // Don't fail the entire operation if status update fails\r\n          }\r\n        }\r\n\r\n        showSuccess(`Successfully assigned ${itemType.replace('_', ' ')} to officer`);\r\n        onAssignSuccess?.();\r\n      }\r\n\r\n      onClose();\r\n    } catch (error) {\r\n      console.error(`Error ${isReassignMode ? 'reassigning' : 'creating assignment'} task:`, error);\r\n      showError(`Failed to ${isReassignMode ? 'reassign' : 'assign'} task`);\r\n    } finally {\r\n      setAssigning(false);\r\n    }\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\r\n      <div className=\"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800\">\r\n        <div className=\"mt-3\">\r\n          {/* Header */}\r\n          <div className=\"flex items-center justify-between mb-6\">\r\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n              {isReassignMode ? 'Reassign Task' : `Create Task for ${itemType.replace('_', ' ').toUpperCase()}`}\r\n            </h3>\r\n            <button\r\n              type=\"button\"\r\n              onClick={onClose}\r\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\r\n            >\r\n              <i className=\"ri-close-line text-xl\"></i>\r\n            </button>\r\n          </div>\r\n\r\n          {/* Task Details */}\r\n          <div className=\"mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800\">\r\n            <h4 className=\"text-sm font-medium text-blue-900 dark:text-blue-100 mb-2 flex items-center\">\r\n              <i className=\"ri-task-line mr-2\"></i>\r\n              {isReassignMode ? 'Task Details:' : 'Task to be Created:'}\r\n            </h4>\r\n            <p className=\"text-sm text-blue-700 dark:text-blue-300 font-medium\">\r\n              {isReassignMode ? task?.title : getTaskTitle(itemType, itemTitle)}\r\n            </p>\r\n            <p className=\"text-xs text-blue-600 dark:text-blue-400 mt-1\">\r\n              {isReassignMode ? task?.description : getTaskDescription(itemType, itemTitle)}\r\n            </p>\r\n            {isReassignMode && task?.task_number && (\r\n              <p className=\"text-xs text-blue-600 dark:text-blue-400 mt-1\">\r\n                Task #{task.task_number}\r\n              </p>\r\n            )}\r\n            {isReassignMode && task?.assignee && (\r\n              <p className=\"text-xs text-blue-600 dark:text-blue-400 mt-1\">\r\n                Currently assigned to: {task.assignee.first_name} {task.assignee.last_name}\r\n              </p>\r\n            )}\r\n          </div>\r\n\r\n          {/* Officer Selection */}\r\n          <div className=\"mb-6\">\r\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n              {isReassignMode ? 'Reassign to Officer *' : 'Assign to Officer *'}\r\n            </label>\r\n            {loading ? (\r\n              <div className=\"text-center py-4 text-gray-500 dark:text-gray-400\">\r\n                Loading officers...\r\n              </div>\r\n            ) : (\r\n              <select\r\n                value={selectedOfficer}\r\n                onChange={(e) => setSelectedOfficer(e.target.value)}\r\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\r\n              >\r\n                <option value=\"\">Select an officer...</option>\r\n                {officers.map((officer) => (\r\n                  <option key={officer.user_id} value={officer.user_id}>\r\n                    {officer.first_name} {officer.last_name} - {officer.email}\r\n                    {officer.department ? ` (${officer.department})` : ''}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n            )}\r\n          </div>\r\n\r\n          {/* Due Date */}\r\n          <div className=\"mb-6\">\r\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n              Due Date *\r\n            </label>\r\n            <input\r\n              type=\"date\"\r\n              value={dueDate}\r\n              onChange={(e) => setDueDate(e.target.value)}\r\n              min={new Date().toISOString().split('T')[0]} // Prevent past dates\r\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\r\n            />\r\n          </div>\r\n\r\n          {/* Comment */}\r\n          <div className=\"mb-6\">\r\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n              {isReassignMode ? 'Reassignment Notes (Optional)' : 'Task Notes (Optional)'}\r\n            </label>\r\n            <textarea\r\n              value={comment}\r\n              onChange={(e) => setComment(e.target.value)}\r\n              placeholder={isReassignMode ? 'Add any notes about this reassignment...' : 'Add any specific instructions or context for this task...'}\r\n              rows={3}\r\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\r\n            />\r\n          </div>\r\n\r\n          {/* Selected Officer Summary */}\r\n          {selectedOfficer && (\r\n            <div className=\"mb-6 p-4 bg-green-50 dark:bg-green-900 rounded-lg\">\r\n              <h4 className=\"text-sm font-medium text-green-900 dark:text-green-100 mb-2\">\r\n                Selected Officer:\r\n              </h4>\r\n              <div className=\"text-sm text-green-700 dark:text-green-200\">\r\n                {(() => {\r\n                  const officer = officers.find(o => o.user_id === selectedOfficer);\r\n                  return officer ? (\r\n                    <>\r\n                      {officer.first_name} {officer.last_name}\r\n                      <br />\r\n                      {officer.email}\r\n                      {officer.department && (\r\n                        <>\r\n                          <br />\r\n                          Department: {officer.department}\r\n                        </>\r\n                      )}\r\n                    </>\r\n                  ) : 'Officer not found';\r\n                })()}\r\n              </div>\r\n              {dueDate && (\r\n                <div className=\"text-sm text-green-700 dark:text-green-200 mt-2\">\r\n                  <strong>Due Date:</strong> {new Date(dueDate).toLocaleDateString()}\r\n                </div>\r\n              )}\r\n            </div>\r\n          )}\r\n\r\n          {/* Actions */}\r\n          <div className=\"flex justify-end space-x-3\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={onClose}\r\n              className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n            >\r\n              Cancel\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={handleAssign}\r\n              disabled={!selectedOfficer || !dueDate || assigning}\r\n              className=\"px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center\"\r\n            >\r\n              {assigning ? (\r\n                <>\r\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\r\n                  {isReassignMode ? 'Reassigning...' : 'Creating Task...'}\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <i className={isReassignMode ? \"ri-user-shared-line mr-2\" : \"ri-task-line mr-2\"}></i>\r\n                  {isReassignMode ? 'Reassign Task' : 'Create Task'}\r\n                </>\r\n              )}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AssignModal;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AA4BA,MAAM,cAA0C,CAAC,EAC/C,MAAM,EACN,OAAO,EACP,MAAM,EACN,QAAQ,EACR,SAAS,EACT,eAAe,EACf,OAAO,QAAQ,EACf,IAAI,EACJ,iBAAiB,EAClB;IACC,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE/C,MAAM,iBAAiB,SAAS,cAAc;IAE9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV;YACA,oDAAoD;YACpD,mBAAmB,iBAAkB,MAAM,eAAe,KAAM;YAChE,WAAW;YACX,+DAA+D;YAC/D,WAAW,kBAAkB,MAAM,WAAW,KAAK,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;QAC9E;IACF,GAAG;QAAC;QAAQ;QAAgB;KAAK;IAEjC,MAAM,gBAAgB;QACpB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,qIAAA,CAAA,cAAW,CAAC,WAAW;YAC9C,MAAM,eAAe,SAAS,IAAI,IAAI,EAAE;YACxC,YAAY;YAEZ,IAAI,aAAa,MAAM,KAAK,GAAG;gBAC7B,QAAQ,IAAI,CAAC;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,YAAY,EAAE;YACd,UAAU;QACZ,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,OAAQ;YACN,KAAK;gBACH,OAAO,qIAAA,CAAA,WAAQ,CAAC,UAAU;YAC5B,KAAK;gBACH,OAAO,qIAAA,CAAA,WAAQ,CAAC,WAAW;YAC7B,KAAK;gBACH,OAAO,qIAAA,CAAA,WAAQ,CAAC,SAAS;YAC3B,KAAK;gBACH,OAAO,qIAAA,CAAA,WAAQ,CAAC,UAAU;YAC5B,KAAK;gBACH,OAAO,qIAAA,CAAA,WAAQ,CAAC,eAAe;YACjC,KAAK;gBACH,OAAO,qIAAA,CAAA,WAAQ,CAAC,WAAW,EAAE,6BAA6B;YAC5D;gBACE,OAAO,qIAAA,CAAA,WAAQ,CAAC,WAAW;QAC/B;IACF;IAEA,MAAM,eAAe,CAAC,UAAkB;QACtC,MAAM,YAAY,aAAa;QAC/B,OAAQ;YACN,KAAK;gBACH,OAAO,CAAC,wBAAwB,EAAE,WAAW;YAC/C,KAAK;gBACH,OAAO,CAAC,2BAA2B,EAAE,WAAW;YAClD,KAAK;gBACH,OAAO,CAAC,kBAAkB,EAAE,WAAW;YACzC,KAAK;gBACH,OAAO,CAAC,iBAAiB,EAAE,WAAW;YACxC,KAAK;gBACH,OAAO,CAAC,iBAAiB,EAAE,WAAW;YACxC;gBACE,OAAO,CAAC,MAAM,EAAE,WAAW;QAC/B;IACF;IAEA,MAAM,qBAAqB,CAAC,UAAkB;QAC5C,MAAM,YAAY,aAAa;QAC/B,OAAQ;YACN,KAAK;gBACH,OAAO,CAAC,gCAAgC,EAAE,UAAU,6BAA6B,CAAC;YACpF,KAAK;gBACH,OAAO,CAAC,0CAA0C,EAAE,UAAU,2BAA2B,CAAC;YAC5F,KAAK;gBACH,OAAO,CAAC,6BAA6B,EAAE,UAAU,oCAAoC,CAAC;YACxF,KAAK;gBACH,OAAO,CAAC,uBAAuB,EAAE,UAAU,iCAAiC,CAAC;YAC/E,KAAK;gBACH,OAAO,CAAC,6BAA6B,EAAE,UAAU,6BAA6B,CAAC;YACjF;gBACE,OAAO,CAAC,mBAAmB,EAAE,UAAU,CAAC,CAAC;QAC7C;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,iBAAiB;YACpB,UAAU;YACV;QACF;QAEA,IAAI,CAAC,SAAS;YACZ,UAAU;YACV;QACF;QAEA,IAAI,kBAAkB,CAAC,MAAM;YAC3B,UAAU;YACV;QACF;QAEA,IAAI,CAAC,kBAAkB,CAAC,QAAQ;YAC9B,UAAU;YACV;QACF;QAEA,aAAa;QACb,IAAI;YACF,IAAI,kBAAkB,MAAM;gBAC1B,yBAAyB;gBACzB,MAAM,qIAAA,CAAA,cAAW,CAAC,YAAY,CAAC,KAAK,OAAO,EAAE;oBAC3C,YAAY;oBACZ,SAAS,QAAQ,IAAI,MAAM;gBAC7B;gBAEA,8BAA8B;gBAC9B,IAAI,SAAS;oBACX,MAAM,qIAAA,CAAA,cAAW,CAAC,UAAU,CAAC,KAAK,OAAO,EAAE;wBACzC,UAAU;oBACZ;gBACF;gBAEA,YAAY;gBACZ;YACF,OAAO;gBACL,mDAAmD;gBACnD,MAAM,WAAW;oBACf,WAAW,wBAAwB;oBACnC,OAAO,aAAa,UAAU;oBAC9B,aAAa,mBAAmB,UAAU;oBAC1C,UAAU,qIAAA,CAAA,eAAY,CAAC,MAAM;oBAC7B,QAAQ,qIAAA,CAAA,aAAU,CAAC,OAAO;oBAC1B,aAAa;oBACb,WAAW;oBACX,aAAa;oBACb,UAAU;oBACV,UAAU;wBACR,SAAS,QAAQ,IAAI,MAAM;wBAC3B,qBAAqB;wBACrB,oBAAoB;oBACtB;gBACF;gBAEA,MAAM,qIAAA,CAAA,cAAW,CAAC,UAAU,CAAC;gBAE7B,2EAA2E;gBAC3E,IAAI,aAAa,eAAe;oBAC9B,IAAI;wBACF,8CAA8C;wBAC9C,MAAM,qIAAA,CAAA,qBAAkB,CAAC,iBAAiB,CAAC,QAAS;wBACpD,uCAAuC;wBACvC,MAAM,qIAAA,CAAA,qBAAkB,CAAC,YAAY,CAAC,QAAS;oBACjD,EAAE,OAAO,aAAa;wBACpB,QAAQ,IAAI,CAAC,oEAAoE;oBACjF,yDAAyD;oBAC3D;gBACF;gBAEA,YAAY,CAAC,sBAAsB,EAAE,SAAS,OAAO,CAAC,KAAK,KAAK,WAAW,CAAC;gBAC5E;YACF;YAEA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,MAAM,EAAE,iBAAiB,gBAAgB,sBAAsB,MAAM,CAAC,EAAE;YACvF,UAAU,CAAC,UAAU,EAAE,iBAAiB,aAAa,SAAS,KAAK,CAAC;QACtE,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,iBAAiB,kBAAkB,CAAC,gBAAgB,EAAE,SAAS,OAAO,CAAC,KAAK,KAAK,WAAW,IAAI;;;;;;0CAEnG,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;;;;;;;kCAKjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAE,WAAU;;;;;;oCACZ,iBAAiB,kBAAkB;;;;;;;0CAEtC,8OAAC;gCAAE,WAAU;0CACV,iBAAiB,MAAM,QAAQ,aAAa,UAAU;;;;;;0CAEzD,8OAAC;gCAAE,WAAU;0CACV,iBAAiB,MAAM,cAAc,mBAAmB,UAAU;;;;;;4BAEpE,kBAAkB,MAAM,6BACvB,8OAAC;gCAAE,WAAU;;oCAAgD;oCACpD,KAAK,WAAW;;;;;;;4BAG1B,kBAAkB,MAAM,0BACvB,8OAAC;gCAAE,WAAU;;oCAAgD;oCACnC,KAAK,QAAQ,CAAC,UAAU;oCAAC;oCAAE,KAAK,QAAQ,CAAC,SAAS;;;;;;;;;;;;;kCAMhF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;0CACd,iBAAiB,0BAA0B;;;;;;4BAE7C,wBACC,8OAAC;gCAAI,WAAU;0CAAoD;;;;;qDAInE,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;gCAClD,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAG;;;;;;oCAChB,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;4CAA6B,OAAO,QAAQ,OAAO;;gDACjD,QAAQ,UAAU;gDAAC;gDAAE,QAAQ,SAAS;gDAAC;gDAAI,QAAQ,KAAK;gDACxD,QAAQ,UAAU,GAAG,CAAC,EAAE,EAAE,QAAQ,UAAU,CAAC,CAAC,CAAC,GAAG;;2CAFxC,QAAQ,OAAO;;;;;;;;;;;;;;;;;kCAUpC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCACC,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;gCAC1C,KAAK,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gCAC3C,WAAU;;;;;;;;;;;;kCAKd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;0CACd,iBAAiB,kCAAkC;;;;;;0CAEtD,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;gCAC1C,aAAa,iBAAiB,6CAA6C;gCAC3E,MAAM;gCACN,WAAU;;;;;;;;;;;;oBAKb,iCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA8D;;;;;;0CAG5E,8OAAC;gCAAI,WAAU;0CACZ,CAAC;oCACA,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK;oCACjD,OAAO,wBACL;;4CACG,QAAQ,UAAU;4CAAC;4CAAE,QAAQ,SAAS;0DACvC,8OAAC;;;;;4CACA,QAAQ,KAAK;4CACb,QAAQ,UAAU,kBACjB;;kEACE,8OAAC;;;;;oDAAK;oDACO,QAAQ,UAAU;;;;uDAInC;gCACN,CAAC;;;;;;4BAEF,yBACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAO;;;;;;oCAAkB;oCAAE,IAAI,KAAK,SAAS,kBAAkB;;;;;;;;;;;;;kCAOxE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,UAAU,CAAC,mBAAmB,CAAC,WAAW;gCAC1C,WAAU;0CAET,0BACC;;sDACE,8OAAC;4CAAI,WAAU;;;;;;wCACd,iBAAiB,mBAAmB;;iEAGvC;;sDACE,8OAAC;4CAAE,WAAW,iBAAiB,6BAA6B;;;;;;wCAC3D,iBAAiB,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStD;uCAEe", "debugId": null}}, {"offset": {"line": 1338, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/AssignButton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport AssignModal from './AssignModal';\r\n\r\ninterface AssignButtonProps {\r\n  itemId: string;\r\n  itemType: 'data_breach' | 'application' | 'complaint' | 'inspection' | 'document_review';\r\n  itemTitle?: string;\r\n  onAssignSuccess?: () => void;\r\n  className?: string;\r\n  size?: 'sm' | 'md' | 'lg';\r\n  variant?: 'primary' | 'secondary' | 'success';\r\n  disabled?: boolean;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nconst AssignButton: React.FC<AssignButtonProps> = ({\r\n  itemId,\r\n  itemType,\r\n  itemTitle,\r\n  onAssignSuccess,\r\n  className = '',\r\n  size = 'sm',\r\n  variant = 'success',\r\n  disabled = false,\r\n  children\r\n}) => {\r\n  const [showAssignModal, setShowAssignModal] = useState(false);\r\n\r\n  const handleAssignClick = () => {\r\n    setShowAssignModal(true);\r\n  };\r\n\r\n  const handleCloseModal = () => {\r\n    setShowAssignModal(false);\r\n  };\r\n\r\n  const handleAssignSuccess = () => {\r\n    setShowAssignModal(false);\r\n    onAssignSuccess?.();\r\n  };\r\n\r\n  // Size classes\r\n  const sizeClasses = {\r\n    sm: 'px-3 py-1 text-xs',\r\n    md: 'px-4 py-2 text-sm',\r\n    lg: 'px-6 py-3 text-base'\r\n  };\r\n\r\n  // Variant classes\r\n  const variantClasses = {\r\n    primary: 'text-white bg-primary hover:bg-primary-dark focus:ring-primary',\r\n    secondary: 'text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 focus:ring-gray-500',\r\n    success: 'text-white bg-green-600 hover:bg-green-700 focus:ring-green-500'\r\n  };\r\n\r\n  // Base classes\r\n  const baseClasses = 'inline-flex items-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed';\r\n\r\n  // Combine all classes\r\n  const buttonClasses = `${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${className}`;\r\n\r\n  return (\r\n    <>\r\n      <button\r\n        type=\"button\"\r\n        onClick={handleAssignClick}\r\n        disabled={disabled}\r\n        className={buttonClasses}\r\n        title={`Create task for ${itemType.replace('_', ' ')} assignment`}\r\n      >\r\n        <i className=\"ri-task-line mr-1\"></i>\r\n        {children || 'Assign'}\r\n      </button>\r\n\r\n      <AssignModal\r\n        isOpen={showAssignModal}\r\n        onClose={handleCloseModal}\r\n        itemId={itemId}\r\n        itemType={itemType}\r\n        itemTitle={itemTitle}\r\n        onAssignSuccess={handleAssignSuccess}\r\n      />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default AssignButton;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAiBA,MAAM,eAA4C,CAAC,EACjD,MAAM,EACN,QAAQ,EACR,SAAS,EACT,eAAe,EACf,YAAY,EAAE,EACd,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,WAAW,KAAK,EAChB,QAAQ,EACT;IACC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,oBAAoB;QACxB,mBAAmB;IACrB;IAEA,MAAM,mBAAmB;QACvB,mBAAmB;IACrB;IAEA,MAAM,sBAAsB;QAC1B,mBAAmB;QACnB;IACF;IAEA,eAAe;IACf,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,kBAAkB;IAClB,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,SAAS;IACX;IAEA,eAAe;IACf,MAAM,cAAc;IAEpB,sBAAsB;IACtB,MAAM,gBAAgB,GAAG,YAAY,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW;IAEnG,qBACE;;0BACE,8OAAC;gBACC,MAAK;gBACL,SAAS;gBACT,UAAU;gBACV,WAAW;gBACX,OAAO,CAAC,gBAAgB,EAAE,SAAS,OAAO,CAAC,KAAK,KAAK,WAAW,CAAC;;kCAEjE,8OAAC;wBAAE,WAAU;;;;;;oBACZ,YAAY;;;;;;;0BAGf,8OAAC,2IAAA,CAAA,UAAW;gBACV,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,UAAU;gBACV,WAAW;gBACX,iBAAiB;;;;;;;;AAIzB;uCAEe", "debugId": null}}, {"offset": {"line": 1421, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/applicantService.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\nimport { Applicant } from '../types/license';\r\n\r\nexport interface CreateApplicantData {\r\n  name: string;\r\n  business_registration_number: string;\r\n  tpin: string;\r\n  website: string;\r\n  email: string;\r\n  phone: string;\r\n  fax?: string;\r\n  level_of_insurance_cover?: string;\r\n  physical_address_id?: string;\r\n  postal_address_id?: string;\r\n  contact_id?: string;\r\n  date_incorporation: string; // Changed from Date to string to match backend DTO\r\n  place_incorporation: string;\r\n}\r\n\r\nexport const applicantService = {\r\n  // Create new applicant\r\n  async createApplicant(data: CreateApplicantData): Promise<Applicant> {\r\n    try {\r\n      console.log('Creating applicant with data:', data);\r\n\r\n      const response = await apiClient.post('/applicants', data);\r\n      return processApiResponse(response);\r\n      throw new Error('Invalid response format from applicant creation');\r\n    } catch (error) {\r\n      console.error('Error creating applicant:', error);\r\n      console.error('Error details:', (error as any)?.response?.data);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get applicant by ID\r\n  async getApplicant(id: string): Promise<Applicant> {\r\n    try {\r\n      const response = await apiClient.get(`/applicants/${id}`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error fetching applicant:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update applicant\r\n  async updateApplicant(id: string, data: Partial<CreateApplicantData>): Promise<Applicant> {\r\n    try {\r\n      console.log('Updating applicant:', id, data);\r\n      \r\n      const response = await apiClient.put(`/applicants/${id}`, data);\r\n      \r\n      console.log('Applicant updated successfully:', response.data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error updating applicant:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get applicants by user (if user can have multiple applicants)\r\n  async getApplicantsByUser(): Promise<Applicant[]> {\r\n    try {\r\n      const response = await apiClient.get('/applicants/by-user');\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error fetching user applicants:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Delete applicant\r\n  async deleteApplicant(id: string): Promise<{ message: string }> {\r\n    try {\r\n      const response = await apiClient.delete(`/applicants/${id}`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error deleting applicant:', error);\r\n      throw error;\r\n    }\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAmBO,MAAM,mBAAmB;IAC9B,uBAAuB;IACvB,MAAM,iBAAgB,IAAyB;QAC7C,IAAI;YACF,QAAQ,GAAG,CAAC,iCAAiC;YAE7C,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,eAAe;YACrD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;;QAE5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,QAAQ,KAAK,CAAC,kBAAmB,OAAe,UAAU;YAC1D,MAAM;QACR;IACF;IAEA,sBAAsB;IACtB,MAAM,cAAa,EAAU;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI;YACxD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,mBAAmB;IACnB,MAAM,iBAAgB,EAAU,EAAE,IAAkC;QAClE,IAAI;YACF,QAAQ,GAAG,CAAC,uBAAuB,IAAI;YAEvC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI,EAAE;YAE1D,QAAQ,GAAG,CAAC,mCAAmC,SAAS,IAAI;YAC5D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,gEAAgE;IAChE,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACR;IACF;IAEA,mBAAmB;IACnB,MAAM,iBAAgB,EAAU;QAC9B,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,IAAI;YAC3D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 1491, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/userService.ts"], "sourcesContent": ["import axios, { AxiosError } from 'axios';\r\nimport { createAuthenticatedAxios } from '../lib/auth';\r\nimport { usersApiClient, apiClient } from '../lib/apiClient';\r\nimport { processApiResponse } from '@/lib/authUtils';\r\nimport { Department } from '@/types/department';\r\nimport { Organization } from '@/types/organization';\r\n\r\n// Types\r\nexport interface User {\r\n  user_id: string;\r\n  email: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  middle_name?: string;\r\n  phone: string;\r\n  department_id?: string;\r\n  organization_id?: string;\r\n  status: 'active' | 'inactive' | 'suspended';\r\n  profile_image?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  last_login?: string;\r\n  isAdmin?: boolean;\r\n  roles?: Role[];\r\n  department?: {\r\n    department_id: string;\r\n    name: string;\r\n    code: string;\r\n  };\r\n}\r\n\r\nexport interface Role {\r\n  role_id: string;\r\n  name: string;\r\n  description?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  permissions?: Permission[];\r\n}\r\n\r\nexport interface Permission {\r\n  permission_id: string;\r\n  name: string;\r\n  description: string;\r\n  category: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  roles?: Role[];\r\n}\r\n\r\nexport interface CreateUserDto {\r\n  email: string;\r\n  password: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  middle_name?: string;\r\n  phone: string;\r\n  department_id?: string;\r\n  organization_id?: string;\r\n  status?: 'active' | 'inactive' | 'suspended';\r\n  profile_image?: string;\r\n  role_ids?: string[];\r\n}\r\n\r\nexport interface UpdateUserDto {\r\n  email?: string;\r\n  password?: string;\r\n  first_name?: string;\r\n  last_name?: string;\r\n  middle_name?: string;\r\n  phone?: string;\r\n  department_id?: string;\r\n  organization_id?: string;\r\n  status?: 'active' | 'inactive' | 'suspended';\r\n  profile_image?: string;\r\n  role_ids?: string[];\r\n}\r\n\r\nexport interface UpdateProfileDto {\r\n  email?: string;\r\n  first_name?: string;\r\n  last_name?: string;\r\n  middle_name?: string;\r\n  phone?: string;\r\n  profile_image?: string;\r\n}\r\n\r\nexport interface ChangePasswordDto {\r\n  current_password: string;\r\n  new_password: string;\r\n  confirm_password: string;\r\n}\r\n\r\nexport interface PaginatedResponse<T> {\r\n  data: T[];\r\n  meta: {\r\n    itemsPerPage: number;\r\n    totalItems?: number;\r\n    currentPage?: number;\r\n    totalPages?: number;\r\n    sortBy: [string, string][];\r\n    searchBy: string[];\r\n    search: string;\r\n    select: string[];\r\n    filter?: Record<string, string | string[]>;\r\n  };\r\n  links: {\r\n    first?: string;\r\n    previous?: string;\r\n    current: string;\r\n    next?: string;\r\n    last?: string;\r\n  };\r\n}\r\n\r\n\r\nexport type UsersResponse = PaginatedResponse<User>;\r\n\r\nexport interface PaginateQuery {\r\n  page?: number;\r\n  limit?: number;\r\n  sortBy?: string[];\r\n  searchBy?: string[];\r\n  search?: string;\r\n  filter?: Record<string, string | string[]>;\r\n}\r\n\r\nexport interface UserFilters extends Record<string, string | undefined> {\r\n  department_id?: string;\r\n  organization_id?: string;\r\n  role?: string;\r\n  status?: string;\r\n}\r\n\r\n\r\nexport const userService = {\r\n  // Get all users with pagination\r\n  async getUsers(query: PaginateQuery = {}): Promise<UsersResponse> {\r\n    const params = new URLSearchParams();\r\n\r\n    if (query.page) params.set('page', query.page.toString());\r\n    if (query.limit) params.set('limit', query.limit.toString());\r\n    if (query.search) params.set('search', query.search);\r\n    if (query.sortBy) {\r\n      query.sortBy.forEach(sort => params.append('sortBy', sort));\r\n    }\r\n    if (query.searchBy) {\r\n      query.searchBy.forEach(search => params.append('searchBy', search));\r\n    }\r\n    if (query.filter) {\r\n      Object.entries(query.filter).forEach(([key, value]) => {\r\n        if (Array.isArray(value)) {\r\n          value.forEach(v => params.append(`filter.${key}`, v));\r\n        } else {\r\n          params.set(`filter.${key}`, value);\r\n        }\r\n      });\r\n    }\r\n\r\n    const response = await usersApiClient.get(`?${params.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get user by ID\r\n  async getUser(id: string): Promise<User> {\r\n    const response = await usersApiClient.get(`/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get user by ID (alias for consistency)\r\n  async getUserById(id: string): Promise<User> {\r\n    return this.getUser(id);\r\n  },\r\n\r\n  // Get current user profile\r\n  async getProfile(): Promise<User> {\r\n    const response = await usersApiClient.get('/profile');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create new user\r\n  async createUser(userData: CreateUserDto): Promise<User> {\r\n    const response = await usersApiClient.post('', userData);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update user\r\n  async updateUser(id: string, userData: UpdateUserDto): Promise<User> {\r\n    const response = await usersApiClient.put(`/${id}`, userData);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update current user profile\r\n  async updateProfile(userData: UpdateProfileDto): Promise<User> {\r\n    const response = await usersApiClient.put('/profile', userData);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Change password\r\n  async changePassword(passwordData: ChangePasswordDto): Promise<{ message: string }> {\r\n    const response = await usersApiClient.put('/profile/password', passwordData);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Upload avatar\r\n  async uploadAvatar(file: File): Promise<User> {\r\n    console.log('userService: uploadAvatar called', {\r\n      fileName: file.name,\r\n      fileSize: file.size,\r\n      fileType: file.type\r\n    });\r\n\r\n    const formData = new FormData();\r\n    formData.append('avatar', file);\r\n\r\n\r\n    try {\r\n      const response = await usersApiClient.post('/profile/avatar', formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data',\r\n        },\r\n      });\r\n\r\n      return processApiResponse(response);\r\n    } catch (error: unknown) {\r\n      const axiosError = error as AxiosError;\r\n      console.error('userService: Upload failed', {\r\n        status: axiosError.response?.status,\r\n        statusText: axiosError.response?.statusText,\r\n        data: axiosError.response?.data,\r\n        message: axiosError.message\r\n      });\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Remove avatar\r\n  async removeAvatar(): Promise<User> {\r\n    const response = await usersApiClient.delete('/profile/avatar');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete user\r\n  async deleteUser(id: string): Promise<void> {\r\n    await usersApiClient.delete(`/${id}`);\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAEA;AACA;;;AAoIO,MAAM,cAAc;IACzB,gCAAgC;IAChC,MAAM,UAAS,QAAuB,CAAC,CAAC;QACtC,MAAM,SAAS,IAAI;QAEnB,IAAI,MAAM,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ;QACtD,IAAI,MAAM,KAAK,EAAE,OAAO,GAAG,CAAC,SAAS,MAAM,KAAK,CAAC,QAAQ;QACzD,IAAI,MAAM,MAAM,EAAE,OAAO,GAAG,CAAC,UAAU,MAAM,MAAM;QACnD,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,MAAM,CAAC,OAAO,CAAC,CAAA,OAAQ,OAAO,MAAM,CAAC,UAAU;QACvD;QACA,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,YAAY;QAC7D;QACA,IAAI,MAAM,MAAM,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAChD,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,OAAO,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBACpD,OAAO;oBACL,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBAC9B;YACF;QACF;QAEA,MAAM,WAAW,MAAM,uHAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI;QACjE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,iBAAiB;IACjB,MAAM,SAAQ,EAAU;QACtB,MAAM,WAAW,MAAM,uHAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI;QAClD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yCAAyC;IACzC,MAAM,aAAY,EAAU;QAC1B,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB;IAEA,2BAA2B;IAC3B,MAAM;QACJ,MAAM,WAAW,MAAM,uHAAA,CAAA,iBAAc,CAAC,GAAG,CAAC;QAC1C,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,kBAAkB;IAClB,MAAM,YAAW,QAAuB;QACtC,MAAM,WAAW,MAAM,uHAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,IAAI;QAC/C,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,cAAc;IACd,MAAM,YAAW,EAAU,EAAE,QAAuB;QAClD,MAAM,WAAW,MAAM,uHAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE;QACpD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8BAA8B;IAC9B,MAAM,eAAc,QAA0B;QAC5C,MAAM,WAAW,MAAM,uHAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,YAAY;QACtD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,kBAAkB;IAClB,MAAM,gBAAe,YAA+B;QAClD,MAAM,WAAW,MAAM,uHAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,qBAAqB;QAC/D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gBAAgB;IAChB,MAAM,cAAa,IAAU;QAC3B,QAAQ,GAAG,CAAC,oCAAoC;YAC9C,UAAU,KAAK,IAAI;YACnB,UAAU,KAAK,IAAI;YACnB,UAAU,KAAK,IAAI;QACrB;QAEA,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,UAAU;QAG1B,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,mBAAmB,UAAU;gBACtE,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAgB;YACvB,MAAM,aAAa;YACnB,QAAQ,KAAK,CAAC,8BAA8B;gBAC1C,QAAQ,WAAW,QAAQ,EAAE;gBAC7B,YAAY,WAAW,QAAQ,EAAE;gBACjC,MAAM,WAAW,QAAQ,EAAE;gBAC3B,SAAS,WAAW,OAAO;YAC7B;YACA,MAAM;QACR;IACF;IAEA,gBAAgB;IAChB,MAAM;QACJ,MAAM,WAAW,MAAM,uHAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;QAC7C,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,cAAc;IACd,MAAM,YAAW,EAAU;QACzB,MAAM,uHAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI;IACtC;AACF", "debugId": null}}, {"offset": {"line": 1600, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/license/ApplicationViewModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { applicationService } from '@/services/applicationService';\r\nimport { applicantService } from '@/services/applicantService';\r\nimport { userService } from '@/services/userService';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport { Application, ApplicationStatus, Applicant } from '@/types/license';\r\nimport Loader from '@/components/Loader';\r\n\r\ninterface ApplicationViewModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  applicationId: string | null;\r\n  departmentType?: string;\r\n  licenseTypeCode?: string;\r\n  onUpdate?: () => void;\r\n}\r\n\r\nconst ApplicationViewModal: React.FC<ApplicationViewModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  applicationId,\r\n  departmentType,\r\n  licenseTypeCode,\r\n  onUpdate\r\n}) => {\r\n  const { showSuccess, showError } = useToast();\r\n  const router = useRouter();\r\n  const [application, setApplication] = useState<Application | null>(null);\r\n  const [applicantDetails, setApplicantDetails] = useState<Applicant | null>(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  useEffect(() => {\r\n    if (isOpen && applicationId) {\r\n      fetchApplicationDetails();\r\n    }\r\n  }, [isOpen, applicationId]);\r\n\r\n  const fetchApplicationDetails = async () => {\r\n    if (!applicationId) return;\r\n\r\n    setLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      // Fetch application details\r\n      const applicationResponse = await applicationService.getApplication(applicationId);\r\n      setApplication(applicationResponse);\r\n\r\n      // Fetch applicant details separately if applicant_id exists\r\n      if (applicationResponse.applicant_id) {\r\n        try {\r\n          const applicantResponse = await applicantService.getApplicant(applicationResponse.applicant_id);\r\n          setApplicantDetails(applicantResponse);\r\n          console.log('✅ Applicant details fetched from database:', applicantResponse);\r\n        } catch (applicantError) {\r\n          console.warn('⚠️ Could not fetch applicant details from database:', applicantError);\r\n          // Try to get user details if applicant fetch fails\r\n          try {\r\n            const userResponse = await userService.getUser(applicationResponse.applicant_id);\r\n            // Convert user data to applicant-like structure\r\n            setApplicantDetails({\r\n              applicant_id: userResponse.user_id,\r\n              name: `${userResponse.first_name} ${userResponse.last_name}`,\r\n              email: userResponse.email,\r\n              phone: userResponse.phone,\r\n              business_registration_number: '',\r\n              tpin: '',\r\n              website: '',\r\n              fax: '',\r\n              date_incorporation: '',\r\n              place_incorporation: '',\r\n              created_at: userResponse.created_at,\r\n              updated_at: userResponse.updated_at\r\n            } as Applicant);\r\n            console.log('✅ User details fetched as fallback:', userResponse);\r\n          } catch (userError) {\r\n            console.warn('⚠️ Could not fetch user details either:', userError);\r\n          }\r\n        }\r\n      }\r\n    } catch (err: unknown) {\r\n      console.error('Error fetching application details:', err);\r\n      const errorMessage = err instanceof Error ? err.message : 'Unknown error';\r\n      setError(`Failed to load application details: ${errorMessage}`);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleEvaluate = () => {\r\n    if (!application) return;\r\n\r\n    // Get the license type code from the application data or use departmentType/licenseTypeCode\r\n    const resolvedLicenseTypeCode = application?.license_category?.license_type?.code ||\r\n                                   licenseTypeCode ||\r\n                                   departmentType ||\r\n                                   'telecommunications'; // fallback\r\n\r\n    // Create URL with both application_id and license_category_id\r\n    const params = new URLSearchParams();\r\n    params.set('application_id', applicationId!);\r\n\r\n    // Get license category ID from application\r\n    const licenseCategoryId = application?.license_category_id;\r\n\r\n    if (licenseCategoryId) {\r\n      params.set('license_category_id', licenseCategoryId);\r\n    }\r\n\r\n    // Close modal and navigate to evaluation page\r\n    onClose();\r\n    router.push(`/applications/${resolvedLicenseTypeCode}/evaluate?${params.toString()}`);\r\n  };\r\n\r\n  const getStatusColor = (status: ApplicationStatus) => {\r\n    switch (status) {\r\n      case ApplicationStatus.SUBMITTED: return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\r\n      case ApplicationStatus.UNDER_REVIEW: return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\r\n      case ApplicationStatus.EVALUATION: return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';\r\n      case ApplicationStatus.APPROVED: return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\r\n      case ApplicationStatus.REJECTED: return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';\r\n      case ApplicationStatus.PENDING_PAYMENT: return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\r\n      case ApplicationStatus.ISSUED: return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200';\r\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n    }\r\n  };\r\n\r\n  const formatDate = (dateString: string) => {\r\n    return new Date(dateString).toLocaleString();\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\r\n      <div className=\"relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white dark:bg-gray-800\">\r\n        <div className=\"mt-3\">\r\n          {/* Header */}\r\n          <div className=\"flex items-center justify-between mb-6\">\r\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n              Application Details\r\n            </h3>\r\n            <button\r\n              type=\"button\"\r\n              onClick={onClose}\r\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\r\n              aria-label=\"Close modal\"\r\n            >\r\n              <i className=\"ri-close-line text-xl\"></i>\r\n            </button>\r\n          </div>\r\n\r\n          {/* Content */}\r\n          {loading ? (\r\n            <div className=\"py-8\">\r\n              <Loader message=\"Loading application details...\" />\r\n            </div>\r\n          ) : error ? (\r\n            <div className=\"bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4\">\r\n              <div className=\"flex\">\r\n                <i className=\"ri-error-warning-line text-red-400 mr-2\"></i>\r\n                <p className=\"text-red-700 dark:text-red-200\">{error}</p>\r\n              </div>\r\n            </div>\r\n          ) : application ? (\r\n            <div className=\"space-y-6\">\r\n              {/* Basic Information */}\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                <div>\r\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n                    Application Number\r\n                  </h4>\r\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                    {application.application_number}\r\n                  </p>\r\n                </div>\r\n                <div>\r\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n                    Status\r\n                  </h4>\r\n                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(application.status)}`}>\r\n                    {application.status.replace('_', ' ').toUpperCase()}\r\n                  </span>\r\n                </div>\r\n                <div>\r\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n                    License Category\r\n                  </h4>\r\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                    {application.license_category?.name || 'N/A'}\r\n                  </p>\r\n                </div>\r\n                <div>\r\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n                    License Type\r\n                  </h4>\r\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                    {application.license_category?.license_type?.name || 'N/A'}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Applicant Information */}\r\n              {(applicantDetails || application.applicant || application.application_data?.step_2_applicant_details) && (\r\n                <div>\r\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n                    Applicant Information\r\n                  </h4>\r\n                  <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\r\n                    {applicantDetails ? (\r\n                      // Use database-fetched applicant details (preferred)\r\n                      <>\r\n                        <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                          <strong>Company/Organization:</strong> {applicantDetails.name}\r\n                        </p>\r\n\r\n                        {applicantDetails.email && (\r\n                          <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                            <strong>Email:</strong> {applicantDetails.email}\r\n                          </p>\r\n                        )}\r\n\r\n                        {applicantDetails.phone && (\r\n                          <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                            <strong>Phone:</strong> {applicantDetails.phone}\r\n                          </p>\r\n                        )}\r\n\r\n                        {applicantDetails.business_registration_number && (\r\n                          <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                            <strong>Registration Number:</strong> {applicantDetails.business_registration_number}\r\n                          </p>\r\n                        )}\r\n\r\n                        {applicantDetails.tpin && (\r\n                          <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                            <strong>TPIN:</strong> {applicantDetails.tpin}\r\n                          </p>\r\n                        )}\r\n\r\n                        {applicantDetails.website && (\r\n                          <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                            <strong>Website:</strong> {applicantDetails.website}\r\n                          </p>\r\n                        )}\r\n\r\n                        {applicantDetails.fax && (\r\n                          <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                            <strong>Fax:</strong> {applicantDetails.fax}\r\n                          </p>\r\n                        )}\r\n\r\n                        {applicantDetails.date_incorporation && (\r\n                          <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                            <strong>Date of Incorporation:</strong> {new Date(applicantDetails.date_incorporation).toLocaleDateString()}\r\n                          </p>\r\n                        )}\r\n\r\n                        {applicantDetails.place_incorporation && (\r\n                          <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                            <strong>Place of Incorporation:</strong> {applicantDetails.place_incorporation}\r\n                          </p>\r\n                        )}\r\n                      </>\r\n                    ) : (\r\n                      // Fallback to application data (legacy)\r\n                      (() => {\r\n                        const applicantData = application.applicant || {};\r\n                        const applicationDetails = application.application_data?.step_2_applicant_details || {};\r\n\r\n                        const firstName = applicantData.first_name || applicationDetails.contact_name?.split(' ')[0] || '';\r\n                        const lastName = applicantData.last_name || applicationDetails.contact_name?.split(' ').slice(1).join(' ') || '';\r\n                        const fullName = applicantData.name || applicationDetails.contact_name || `${firstName} ${lastName}`.trim();\r\n\r\n                        const companyName = applicantData.company_name ||\r\n                                           applicantData.organization_name ||\r\n                                           applicationDetails.company_name ||\r\n                                           applicationDetails.organization_name ||\r\n                                           applicantData.name;\r\n\r\n                        const email = applicantData.email || applicationDetails.contact_email || applicationDetails.company_email;\r\n                        const phone = applicantData.phone || applicationDetails.contact_phone || applicationDetails.company_phone;\r\n\r\n                        return (\r\n                          <>\r\n                            <div className=\"mb-2 p-2 bg-yellow-50 dark:bg-yellow-900 rounded text-xs\">\r\n                              <strong>Note:</strong> Using fallback data from application. Database applicant details not available.\r\n                            </div>\r\n\r\n                            {fullName && (\r\n                              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                                <strong>Contact Person:</strong> {fullName}\r\n                              </p>\r\n                            )}\r\n\r\n                            {companyName && companyName !== fullName && (\r\n                              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                                <strong>Company/Organization:</strong> {companyName}\r\n                              </p>\r\n                            )}\r\n\r\n                            {email && (\r\n                              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                                <strong>Email:</strong> {email}\r\n                              </p>\r\n                            )}\r\n\r\n                            {phone && (\r\n                              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                                <strong>Phone:</strong> {phone}\r\n                              </p>\r\n                            )}\r\n                          </>\r\n                        );\r\n                      })()\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* Application Data */}\r\n              {application.application_data && (\r\n                <div>\r\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n                    Application Data\r\n                  </h4>\r\n                  <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\r\n                    <pre className=\"text-sm text-gray-600 dark:text-gray-400 whitespace-pre-wrap\">\r\n                      {JSON.stringify(application.application_data, null, 2)}\r\n                    </pre>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* Timestamps */}\r\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 pt-4 border-t border-gray-200 dark:border-gray-600\">\r\n                <div>\r\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n                    Submitted\r\n                  </h4>\r\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                    {formatDate(application.created_at)}\r\n                  </p>\r\n                </div>\r\n                <div>\r\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n                    Last Updated\r\n                  </h4>\r\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                    {formatDate(application.updated_at)}\r\n                  </p>\r\n                </div>\r\n                {application.submitted_at && (\r\n                  <div>\r\n                    <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n                      Submitted At\r\n                    </h4>\r\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                      {formatDate(application.submitted_at)}\r\n                    </p>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"text-center py-8\">\r\n              <p className=\"text-gray-500 dark:text-gray-400\">No application data available</p>\r\n            </div>\r\n          )}\r\n\r\n          {/* Actions */}\r\n          <div className=\"flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={onClose}\r\n              className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n            >\r\n              Close\r\n            </button>\r\n            {application && (\r\n              <button\r\n                type=\"button\"\r\n                onClick={handleEvaluate}\r\n                className=\"px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n              >\r\n                <i className=\"ri-clipboard-line mr-2\"></i>\r\n                Evaluate\r\n              </button>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ApplicationViewModal;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAoBA,MAAM,uBAA4D,CAAC,EACjE,MAAM,EACN,OAAO,EACP,aAAa,EACb,cAAc,EACd,eAAe,EACf,QAAQ,EACT;IACC,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACnE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAC3E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,eAAe;YAC3B;QACF;IACF,GAAG;QAAC;QAAQ;KAAc;IAE1B,MAAM,0BAA0B;QAC9B,IAAI,CAAC,eAAe;QAEpB,WAAW;QACX,SAAS;QAET,IAAI;YACF,4BAA4B;YAC5B,MAAM,sBAAsB,MAAM,qIAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;YACpE,eAAe;YAEf,4DAA4D;YAC5D,IAAI,oBAAoB,YAAY,EAAE;gBACpC,IAAI;oBACF,MAAM,oBAAoB,MAAM,mIAAA,CAAA,mBAAgB,CAAC,YAAY,CAAC,oBAAoB,YAAY;oBAC9F,oBAAoB;oBACpB,QAAQ,GAAG,CAAC,8CAA8C;gBAC5D,EAAE,OAAO,gBAAgB;oBACvB,QAAQ,IAAI,CAAC,uDAAuD;oBACpE,mDAAmD;oBACnD,IAAI;wBACF,MAAM,eAAe,MAAM,8HAAA,CAAA,cAAW,CAAC,OAAO,CAAC,oBAAoB,YAAY;wBAC/E,gDAAgD;wBAChD,oBAAoB;4BAClB,cAAc,aAAa,OAAO;4BAClC,MAAM,GAAG,aAAa,UAAU,CAAC,CAAC,EAAE,aAAa,SAAS,EAAE;4BAC5D,OAAO,aAAa,KAAK;4BACzB,OAAO,aAAa,KAAK;4BACzB,8BAA8B;4BAC9B,MAAM;4BACN,SAAS;4BACT,KAAK;4BACL,oBAAoB;4BACpB,qBAAqB;4BACrB,YAAY,aAAa,UAAU;4BACnC,YAAY,aAAa,UAAU;wBACrC;wBACA,QAAQ,GAAG,CAAC,uCAAuC;oBACrD,EAAE,OAAO,WAAW;wBAClB,QAAQ,IAAI,CAAC,2CAA2C;oBAC1D;gBACF;YACF;QACF,EAAE,OAAO,KAAc;YACrB,QAAQ,KAAK,CAAC,uCAAuC;YACrD,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS,CAAC,oCAAoC,EAAE,cAAc;QAChE,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,aAAa;QAElB,4FAA4F;QAC5F,MAAM,0BAA0B,aAAa,kBAAkB,cAAc,QAC9C,mBACA,kBACA,sBAAsB,WAAW;QAEhE,8DAA8D;QAC9D,MAAM,SAAS,IAAI;QACnB,OAAO,GAAG,CAAC,kBAAkB;QAE7B,2CAA2C;QAC3C,MAAM,oBAAoB,aAAa;QAEvC,IAAI,mBAAmB;YACrB,OAAO,GAAG,CAAC,uBAAuB;QACpC;QAEA,8CAA8C;QAC9C;QACA,OAAO,IAAI,CAAC,CAAC,cAAc,EAAE,wBAAwB,UAAU,EAAE,OAAO,QAAQ,IAAI;IACtF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK,uHAAA,CAAA,oBAAiB,CAAC,SAAS;gBAAE,OAAO;YACzC,KAAK,uHAAA,CAAA,oBAAiB,CAAC,YAAY;gBAAE,OAAO;YAC5C,KAAK,uHAAA,CAAA,oBAAiB,CAAC,UAAU;gBAAE,OAAO;YAC1C,KAAK,uHAAA,CAAA,oBAAiB,CAAC,QAAQ;gBAAE,OAAO;YACxC,KAAK,uHAAA,CAAA,oBAAiB,CAAC,QAAQ;gBAAE,OAAO;YACxC,KAAK,uHAAA,CAAA,oBAAiB,CAAC,eAAe;gBAAE,OAAO;YAC/C,KAAK,uHAAA,CAAA,oBAAiB,CAAC,MAAM;gBAAE,OAAO;YACtC;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,cAAc;IAC5C;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAuD;;;;;;0CAGrE,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEX,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;;;;;;;oBAKhB,wBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4HAAA,CAAA,UAAM;4BAAC,SAAQ;;;;;;;;;;+BAEhB,sBACF,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;;;;;8CACb,8OAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;;;;;+BAGjD,4BACF,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,8OAAC;gDAAE,WAAU;0DACV,YAAY,kBAAkB;;;;;;;;;;;;kDAGnC,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,8OAAC;gDAAK,WAAW,CAAC,yDAAyD,EAAE,eAAe,YAAY,MAAM,GAAG;0DAC9G,YAAY,MAAM,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;;;;;;;kDAGrD,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,8OAAC;gDAAE,WAAU;0DACV,YAAY,gBAAgB,EAAE,QAAQ;;;;;;;;;;;;kDAG3C,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,8OAAC;gDAAE,WAAU;0DACV,YAAY,gBAAgB,EAAE,cAAc,QAAQ;;;;;;;;;;;;;;;;;;4BAM1D,CAAC,oBAAoB,YAAY,SAAS,IAAI,YAAY,gBAAgB,EAAE,wBAAwB,mBACnG,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,8OAAC;wCAAI,WAAU;kDACZ,mBACC,qDAAqD;sDACrD;;8DACE,8OAAC;oDAAE,WAAU;;sEACX,8OAAC;sEAAO;;;;;;wDAA8B;wDAAE,iBAAiB,IAAI;;;;;;;gDAG9D,iBAAiB,KAAK,kBACrB,8OAAC;oDAAE,WAAU;;sEACX,8OAAC;sEAAO;;;;;;wDAAe;wDAAE,iBAAiB,KAAK;;;;;;;gDAIlD,iBAAiB,KAAK,kBACrB,8OAAC;oDAAE,WAAU;;sEACX,8OAAC;sEAAO;;;;;;wDAAe;wDAAE,iBAAiB,KAAK;;;;;;;gDAIlD,iBAAiB,4BAA4B,kBAC5C,8OAAC;oDAAE,WAAU;;sEACX,8OAAC;sEAAO;;;;;;wDAA6B;wDAAE,iBAAiB,4BAA4B;;;;;;;gDAIvF,iBAAiB,IAAI,kBACpB,8OAAC;oDAAE,WAAU;;sEACX,8OAAC;sEAAO;;;;;;wDAAc;wDAAE,iBAAiB,IAAI;;;;;;;gDAIhD,iBAAiB,OAAO,kBACvB,8OAAC;oDAAE,WAAU;;sEACX,8OAAC;sEAAO;;;;;;wDAAiB;wDAAE,iBAAiB,OAAO;;;;;;;gDAItD,iBAAiB,GAAG,kBACnB,8OAAC;oDAAE,WAAU;;sEACX,8OAAC;sEAAO;;;;;;wDAAa;wDAAE,iBAAiB,GAAG;;;;;;;gDAI9C,iBAAiB,kBAAkB,kBAClC,8OAAC;oDAAE,WAAU;;sEACX,8OAAC;sEAAO;;;;;;wDAA+B;wDAAE,IAAI,KAAK,iBAAiB,kBAAkB,EAAE,kBAAkB;;;;;;;gDAI5G,iBAAiB,mBAAmB,kBACnC,8OAAC;oDAAE,WAAU;;sEACX,8OAAC;sEAAO;;;;;;wDAAgC;wDAAE,iBAAiB,mBAAmB;;;;;;;;2DAKpF,wCAAwC;wCACxC,CAAC;4CACC,MAAM,gBAAgB,YAAY,SAAS,IAAI,CAAC;4CAChD,MAAM,qBAAqB,YAAY,gBAAgB,EAAE,4BAA4B,CAAC;4CAEtF,MAAM,YAAY,cAAc,UAAU,IAAI,mBAAmB,YAAY,EAAE,MAAM,IAAI,CAAC,EAAE,IAAI;4CAChG,MAAM,WAAW,cAAc,SAAS,IAAI,mBAAmB,YAAY,EAAE,MAAM,KAAK,MAAM,GAAG,KAAK,QAAQ;4CAC9G,MAAM,WAAW,cAAc,IAAI,IAAI,mBAAmB,YAAY,IAAI,GAAG,UAAU,CAAC,EAAE,UAAU,CAAC,IAAI;4CAEzG,MAAM,cAAc,cAAc,YAAY,IAC3B,cAAc,iBAAiB,IAC/B,mBAAmB,YAAY,IAC/B,mBAAmB,iBAAiB,IACpC,cAAc,IAAI;4CAErC,MAAM,QAAQ,cAAc,KAAK,IAAI,mBAAmB,aAAa,IAAI,mBAAmB,aAAa;4CACzG,MAAM,QAAQ,cAAc,KAAK,IAAI,mBAAmB,aAAa,IAAI,mBAAmB,aAAa;4CAEzG,qBACE;;kEACE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAO;;;;;;4DAAc;;;;;;;oDAGvB,0BACC,8OAAC;wDAAE,WAAU;;0EACX,8OAAC;0EAAO;;;;;;4DAAwB;4DAAE;;;;;;;oDAIrC,eAAe,gBAAgB,0BAC9B,8OAAC;wDAAE,WAAU;;0EACX,8OAAC;0EAAO;;;;;;4DAA8B;4DAAE;;;;;;;oDAI3C,uBACC,8OAAC;wDAAE,WAAU;;0EACX,8OAAC;0EAAO;;;;;;4DAAe;4DAAE;;;;;;;oDAI5B,uBACC,8OAAC;wDAAE,WAAU;;0EACX,8OAAC;0EAAO;;;;;;4DAAe;4DAAE;;;;;;;;;wCAKnC,CAAC;;;;;;;;;;;;4BAOR,YAAY,gBAAgB,kBAC3B,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACZ,KAAK,SAAS,CAAC,YAAY,gBAAgB,EAAE,MAAM;;;;;;;;;;;;;;;;;0CAO5D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,8OAAC;gDAAE,WAAU;0DACV,WAAW,YAAY,UAAU;;;;;;;;;;;;kDAGtC,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,8OAAC;gDAAE,WAAU;0DACV,WAAW,YAAY,UAAU;;;;;;;;;;;;oCAGrC,YAAY,YAAY,kBACvB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,8OAAC;gDAAE,WAAU;0DACV,WAAW,YAAY,YAAY;;;;;;;;;;;;;;;;;;;;;;;6CAO9C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;kCAKpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CACX;;;;;;4BAGA,6BACC,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;;kDAEV,8OAAC;wCAAE,WAAU;;;;;;oCAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1D;uCAEe", "debugId": null}}, {"offset": {"line": 2404, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/Pagination.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\n// Import the pagination response type from the existing structure\ninterface PaginationMeta {\n  itemsPerPage: number;\n  totalItems: number;\n  currentPage: number;\n  totalPages: number;\n  sortBy: [string, string][];\n  searchBy: string[];\n  search: string;\n  filter?: Record<string, string | string[]>;\n}\n\ninterface PaginationProps {\n  meta: PaginationMeta;\n  onPageChange: (page: number) => void;\n  onPageSizeChange?: (pageSize: number) => void;\n  showFirstLast?: boolean;\n  showPageSizeSelector?: boolean;\n  showInfo?: boolean;\n  maxVisiblePages?: number;\n  pageSizeOptions?: number[];\n  className?: string;\n}\n\nconst Pagination: React.FC<PaginationProps> = ({\n  meta,\n  onPageChange,\n  onPageSizeChange,\n  showFirstLast = true,\n  showPageSizeSelector = true,\n  showInfo = true,\n  maxVisiblePages = 7,\n  pageSizeOptions = [10, 25, 50, 100],\n  className = ''\n}) => {\n  const { currentPage, totalPages, totalItems, itemsPerPage } = meta;\n\n  // Don't render if there's only one page or no pages and no additional features\n  if (totalPages <= 1 && !showPageSizeSelector && !showInfo) {\n    return null;\n  }\n\n  // Calculate current items range\n  const startItem = totalItems > 0 ? (currentPage - 1) * itemsPerPage + 1 : 0;\n  const endItem = Math.min(currentPage * itemsPerPage, totalItems);\n\n  // Calculate which pages to show\n  const getVisiblePages = (): (number | string)[] => {\n    const pages: (number | string)[] = [];\n    \n    // If total pages is less than or equal to maxVisiblePages, show all\n    if (totalPages <= maxVisiblePages) {\n      for (let i = 1; i <= totalPages; i++) {\n        pages.push(i);\n      }\n      return pages;\n    }\n\n    // Always show first page\n    pages.push(1);\n\n    // Calculate start and end of the visible range around current page\n    const sidePages = Math.floor((maxVisiblePages - 3) / 2); // -3 for first, last, and current\n    let startPage = Math.max(2, currentPage - sidePages);\n    let endPage = Math.min(totalPages - 1, currentPage + sidePages);\n\n    // Adjust if we're near the beginning\n    if (currentPage <= sidePages + 2) {\n      startPage = 2;\n      endPage = Math.min(totalPages - 1, maxVisiblePages - 1);\n    }\n\n    // Adjust if we're near the end\n    if (currentPage >= totalPages - sidePages - 1) {\n      startPage = Math.max(2, totalPages - maxVisiblePages + 2);\n      endPage = totalPages - 1;\n    }\n\n    // Add ellipsis after first page if needed\n    if (startPage > 2) {\n      pages.push('...');\n    }\n\n    // Add pages in the visible range\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n\n    // Add ellipsis before last page if needed\n    if (endPage < totalPages - 1) {\n      pages.push('...');\n    }\n\n    // Always show last page (if it's not already included)\n    if (totalPages > 1) {\n      pages.push(totalPages);\n    }\n\n    return pages;\n  };\n\n  const visiblePages = getVisiblePages();\n\n  const handlePageClick = (page: number | string) => {\n    if (typeof page === 'number' && page !== currentPage) {\n      onPageChange(page);\n    }\n  };\n\n  const handlePageSizeChange = (event: React.ChangeEvent<HTMLSelectElement>) => {\n    const newPageSize = parseInt(event.target.value);\n    if (onPageSizeChange) {\n      onPageSizeChange(newPageSize);\n    }\n  };\n\n  const handlePrevious = () => {\n    if (currentPage > 1) {\n      onPageChange(currentPage - 1);\n    }\n  };\n\n  const handleNext = () => {\n    if (currentPage < totalPages) {\n      onPageChange(currentPage + 1);\n    }\n  };\n\n  const handleFirst = () => {\n    if (currentPage !== 1) {\n      onPageChange(1);\n    }\n  };\n\n  const handleLast = () => {\n    if (currentPage !== totalPages) {\n      onPageChange(totalPages);\n    }\n  };\n\n  return (\n    <div className={`flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0 px-6 py-3 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 ${className}`}>\n      {/* Left side - Info and page size selector */}\n      <div className=\"flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4\">\n        {/* Items info */}\n        {showInfo && totalItems > 0 && (\n          <div className=\"text-sm text-gray-700 dark:text-gray-300\">\n            Showing <span className=\"font-medium\">{startItem}</span> to{' '}\n            <span className=\"font-medium\">{endItem}</span> of{' '}\n            <span className=\"font-medium\">{totalItems}</span> results\n          </div>\n        )}\n\n        {/* Page size selector */}\n        {showPageSizeSelector && onPageSizeChange && (\n          <div className=\"flex items-center space-x-2\">\n            <select\n              value={itemsPerPage}\n              onChange={handlePageSizeChange}\n              className=\"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent\"\n            >\n              {pageSizeOptions.map((size) => (\n                <option key={size} value={size}>\n                  {size} per page\n                </option>\n              ))}\n            </select>\n          </div>\n        )}\n      </div>\n\n      {/* Right side - Pagination controls */}\n      {totalPages > 1 && (\n        <nav className=\"flex items-center space-x-1\" aria-label=\"Pagination\">\n          {/* First page button */}\n          {showFirstLast && currentPage > 1 && (\n            <button\n              onClick={handleFirst}\n              className=\"inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300\"\n              aria-label=\"Go to first page\"\n            >\n              <i className=\"ri-skip-back-line\"></i>\n            </button>\n          )}\n\n          {/* Previous button */}\n          <button\n            onClick={handlePrevious}\n            disabled={currentPage === 1}\n            className={`inline-flex items-center px-2 py-2 text-sm font-medium border ${\n              currentPage === 1\n                ? 'text-gray-300 bg-gray-100 border-gray-300 cursor-not-allowed dark:bg-gray-700 dark:border-gray-600 dark:text-gray-500'\n                : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300'\n            } ${showFirstLast && currentPage > 1 ? '' : 'rounded-l-md'}`}\n            aria-label=\"Go to previous page\"\n          >\n            <i className=\"ri-arrow-left-s-line\"></i>\n          </button>\n\n          {/* Page numbers */}\n          {visiblePages.map((page, index) => (\n            <React.Fragment key={index}>\n              {page === '...' ? (\n                <span className=\"inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400\">\n                  ...\n                </span>\n              ) : (\n                <button\n                  onClick={() => handlePageClick(page)}\n                  className={`inline-flex items-center px-4 py-2 text-sm font-medium border ${\n                    page === currentPage\n                      ? 'text-white bg-red-600 border-red-600 hover:bg-red-700'\n                      : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300'\n                  }`}\n                  aria-label={`Go to page ${page}`}\n                  aria-current={page === currentPage ? 'page' : undefined}\n                >\n                  {page}\n                </button>\n              )}\n            </React.Fragment>\n          ))}\n\n          {/* Next button */}\n          <button\n            onClick={handleNext}\n            disabled={currentPage === totalPages}\n            className={`inline-flex items-center px-2 py-2 text-sm font-medium border ${\n              currentPage === totalPages\n                ? 'text-gray-300 bg-gray-100 border-gray-300 cursor-not-allowed dark:bg-gray-700 dark:border-gray-600 dark:text-gray-500'\n                : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300'\n            } ${showFirstLast && currentPage < totalPages ? '' : 'rounded-r-md'}`}\n            aria-label=\"Go to next page\"\n          >\n            <i className=\"ri-arrow-right-s-line\"></i>\n          </button>\n\n          {/* Last page button */}\n          {showFirstLast && currentPage < totalPages && (\n            <button\n              onClick={handleLast}\n              className=\"inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300\"\n              aria-label=\"Go to last page\"\n            >\n              <i className=\"ri-skip-forward-line\"></i>\n            </button>\n          )}\n        </nav>\n      )}\n    </div>\n  );\n};\n\nexport default Pagination;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AA4BA,MAAM,aAAwC,CAAC,EAC7C,IAAI,EACJ,YAAY,EACZ,gBAAgB,EAChB,gBAAgB,IAAI,EACpB,uBAAuB,IAAI,EAC3B,WAAW,IAAI,EACf,kBAAkB,CAAC,EACnB,kBAAkB;IAAC;IAAI;IAAI;IAAI;CAAI,EACnC,YAAY,EAAE,EACf;IACC,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG;IAE9D,+EAA+E;IAC/E,IAAI,cAAc,KAAK,CAAC,wBAAwB,CAAC,UAAU;QACzD,OAAO;IACT;IAEA,gCAAgC;IAChC,MAAM,YAAY,aAAa,IAAI,CAAC,cAAc,CAAC,IAAI,eAAe,IAAI;IAC1E,MAAM,UAAU,KAAK,GAAG,CAAC,cAAc,cAAc;IAErD,gCAAgC;IAChC,MAAM,kBAAkB;QACtB,MAAM,QAA6B,EAAE;QAErC,oEAAoE;QACpE,IAAI,cAAc,iBAAiB;YACjC,IAAK,IAAI,IAAI,GAAG,KAAK,YAAY,IAAK;gBACpC,MAAM,IAAI,CAAC;YACb;YACA,OAAO;QACT;QAEA,yBAAyB;QACzB,MAAM,IAAI,CAAC;QAEX,mEAAmE;QACnE,MAAM,YAAY,KAAK,KAAK,CAAC,CAAC,kBAAkB,CAAC,IAAI,IAAI,kCAAkC;QAC3F,IAAI,YAAY,KAAK,GAAG,CAAC,GAAG,cAAc;QAC1C,IAAI,UAAU,KAAK,GAAG,CAAC,aAAa,GAAG,cAAc;QAErD,qCAAqC;QACrC,IAAI,eAAe,YAAY,GAAG;YAChC,YAAY;YACZ,UAAU,KAAK,GAAG,CAAC,aAAa,GAAG,kBAAkB;QACvD;QAEA,+BAA+B;QAC/B,IAAI,eAAe,aAAa,YAAY,GAAG;YAC7C,YAAY,KAAK,GAAG,CAAC,GAAG,aAAa,kBAAkB;YACvD,UAAU,aAAa;QACzB;QAEA,0CAA0C;QAC1C,IAAI,YAAY,GAAG;YACjB,MAAM,IAAI,CAAC;QACb;QAEA,iCAAiC;QACjC,IAAK,IAAI,IAAI,WAAW,KAAK,SAAS,IAAK;YACzC,MAAM,IAAI,CAAC;QACb;QAEA,0CAA0C;QAC1C,IAAI,UAAU,aAAa,GAAG;YAC5B,MAAM,IAAI,CAAC;QACb;QAEA,uDAAuD;QACvD,IAAI,aAAa,GAAG;YAClB,MAAM,IAAI,CAAC;QACb;QAEA,OAAO;IACT;IAEA,MAAM,eAAe;IAErB,MAAM,kBAAkB,CAAC;QACvB,IAAI,OAAO,SAAS,YAAY,SAAS,aAAa;YACpD,aAAa;QACf;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,cAAc,SAAS,MAAM,MAAM,CAAC,KAAK;QAC/C,IAAI,kBAAkB;YACpB,iBAAiB;QACnB;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,cAAc,GAAG;YACnB,aAAa,cAAc;QAC7B;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,cAAc,YAAY;YAC5B,aAAa,cAAc;QAC7B;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,gBAAgB,GAAG;YACrB,aAAa;QACf;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,gBAAgB,YAAY;YAC9B,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,gKAAgK,EAAE,WAAW;;0BAE5L,8OAAC;gBAAI,WAAU;;oBAEZ,YAAY,aAAa,mBACxB,8OAAC;wBAAI,WAAU;;4BAA2C;0CAChD,8OAAC;gCAAK,WAAU;0CAAe;;;;;;4BAAiB;4BAAI;0CAC5D,8OAAC;gCAAK,WAAU;0CAAe;;;;;;4BAAe;4BAAI;0CAClD,8OAAC;gCAAK,WAAU;0CAAe;;;;;;4BAAkB;;;;;;;oBAKpD,wBAAwB,kCACvB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,OAAO;4BACP,UAAU;4BACV,WAAU;sCAET,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC;oCAAkB,OAAO;;wCACvB;wCAAK;;mCADK;;;;;;;;;;;;;;;;;;;;;YAUtB,aAAa,mBACZ,8OAAC;gBAAI,WAAU;gBAA8B,cAAW;;oBAErD,iBAAiB,cAAc,mBAC9B,8OAAC;wBACC,SAAS;wBACT,WAAU;wBACV,cAAW;kCAEX,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;kCAKjB,8OAAC;wBACC,SAAS;wBACT,UAAU,gBAAgB;wBAC1B,WAAW,CAAC,8DAA8D,EACxE,gBAAgB,IACZ,0HACA,uLACL,CAAC,EAAE,iBAAiB,cAAc,IAAI,KAAK,gBAAgB;wBAC5D,cAAW;kCAEX,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;oBAId,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;sCACZ,SAAS,sBACR,8OAAC;gCAAK,WAAU;0CAAgK;;;;;qDAIhL,8OAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAW,CAAC,8DAA8D,EACxE,SAAS,cACL,0DACA,wLACJ;gCACF,cAAY,CAAC,WAAW,EAAE,MAAM;gCAChC,gBAAc,SAAS,cAAc,SAAS;0CAE7C;;;;;;2BAhBc;;;;;kCAuBvB,8OAAC;wBACC,SAAS;wBACT,UAAU,gBAAgB;wBAC1B,WAAW,CAAC,8DAA8D,EACxE,gBAAgB,aACZ,0HACA,uLACL,CAAC,EAAE,iBAAiB,cAAc,aAAa,KAAK,gBAAgB;wBACrE,cAAW;kCAEX,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;oBAId,iBAAiB,cAAc,4BAC9B,8OAAC;wBACC,SAAS;wBACT,WAAU;wBACV,cAAW;kCAEX,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAO3B;uCAEe", "debugId": null}}, {"offset": {"line": 2694, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/DataTable.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { PaginatedResponse, PaginateQuery } from '../../services/userService';\r\nimport Pagination from './Pagination';\r\n\r\ninterface Column<T> {\r\n  key: keyof T | string;\r\n  label: string;\r\n  sortable?: boolean;\r\n  searchable?: boolean;\r\n  render?: (value: any, item: T) => React.ReactNode;\r\n  className?: string;\r\n}\r\n\r\ninterface DataTableProps<T> {\r\n  columns: Column<T>[];\r\n  data: PaginatedResponse<T> | null;\r\n  loading?: boolean;\r\n  onQueryChange: (query: PaginateQuery) => void;\r\n  searchPlaceholder?: string;\r\n  className?: string;\r\n}\r\n\r\nexport default function DataTable<T extends Record<string, any>>({\r\n  columns,\r\n  data,\r\n  loading = false,\r\n  onQueryChange,\r\n  searchPlaceholder = \"Search...\",\r\n  className = \"\",\r\n}: DataTableProps<T>) {\r\n  const [query, setQuery] = useState<PaginateQuery>({\r\n    page: 1,\r\n    limit: 10,\r\n    search: '',\r\n    sortBy: [],\r\n  });\r\n  const [searchInput, setSearchInput] = useState('');\r\n\r\n  // Debounced search effect\r\n  useEffect(() => {\r\n    const timeoutId = setTimeout(() => {\r\n      if (searchInput !== query.search) {\r\n        handleSearch(searchInput);\r\n      }\r\n    }, 300);\r\n\r\n    return () => clearTimeout(timeoutId);\r\n  }, [searchInput]);\r\n\r\n  const handleSearch = useCallback((search: string) => {\r\n    try {\r\n      const newQuery = { ...query, search, page: 1 };\r\n      setQuery(newQuery);\r\n      onQueryChange(newQuery);\r\n    } catch (error) {\r\n      console.error('Error handling search:', error);\r\n    }\r\n  }, [query, onQueryChange]);\r\n\r\n  const handleSort = (columnKey: string) => {\r\n    const currentSort = query.sortBy?.find(sort => sort.startsWith(columnKey));\r\n    let newSortBy: string[] = [];\r\n\r\n    if (!currentSort) {\r\n      newSortBy = [`${columnKey}:ASC`];\r\n    } else if (currentSort.endsWith(':ASC')) {\r\n      newSortBy = [`${columnKey}:DESC`];\r\n    } else {\r\n      newSortBy = [];\r\n    }\r\n\r\n    const newQuery = { ...query, sortBy: newSortBy, page: 1 };\r\n    setQuery(newQuery);\r\n    onQueryChange(newQuery);\r\n  };\r\n\r\n  const handlePageChange = (page: number) => {\r\n    const newQuery = { ...query, page };\r\n    setQuery(newQuery);\r\n    onQueryChange(newQuery);\r\n  };\r\n\r\n  const handleLimitChange = (limit: number) => {\r\n    const newQuery = { ...query, limit, page: 1 };\r\n    setQuery(newQuery);\r\n    onQueryChange(newQuery);\r\n  };\r\n\r\n  const getSortDirection = (columnKey: string): 'asc' | 'desc' | null => {\r\n    const currentSort = query.sortBy?.find(sort => sort.startsWith(columnKey));\r\n    if (!currentSort) return null;\r\n    return currentSort.endsWith(':ASC') ? 'asc' : 'desc';\r\n  };\r\n\r\n  // Handle null data case early\r\n  if (!data) {\r\n    return (\r\n      <div className={`bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden ${className}`}>\r\n        <div className=\"p-6 text-center text-gray-500 dark:text-gray-400\">\r\n          <div className=\"flex items-center justify-center\">\r\n            <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-red-600\"></div>\r\n            <span className=\"ml-2\">Loading...</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const handlePageSizeChange = (newPageSize: number) => {\r\n    const newQuery = { ...query, limit: newPageSize, page: 1 };\r\n    setQuery(newQuery);\r\n    onQueryChange(newQuery);\r\n  };\r\n\r\n  return (\r\n    <div className={`bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden ${className}`}>\r\n      {/* Search Bar */}\r\n      <div className=\"p-6 border-b border-gray-200 dark:border-gray-700\">\r\n        <div className=\"relative\">\r\n          <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n            <i className=\"ri-search-line text-gray-400 dark:text-gray-500\"></i>\r\n          </div>\r\n          <input\r\n            type=\"text\"\r\n            placeholder={searchPlaceholder}\r\n            value={searchInput}\r\n            onChange={(e) => setSearchInput(e.target.value)}\r\n            className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-gray-900 dark:text-gray-100\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Table */}\r\n      <div className=\"overflow-x-auto\" style={{ minHeight: '500px' }}>\r\n        <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\r\n          <thead className=\"bg-gray-50 dark:bg-gray-900\">\r\n            <tr>\r\n              {columns.map((column) => (\r\n                <th\r\n                  key={String(column.key)}\r\n                  className={`px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${\r\n                    column.sortable ? 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800' : ''\r\n                  } ${column.className || ''}`}\r\n                  onClick={() => column.sortable && handleSort(String(column.key))}\r\n                >\r\n                  <div className=\"flex items-center space-x-1\">\r\n                    <span>{column.label}</span>\r\n                    {column.sortable && (\r\n                      <div className=\"flex flex-col\">\r\n                        <i className={`ri-arrow-up-s-line text-xs ${\r\n                          getSortDirection(String(column.key)) === 'asc' ? 'text-red-600' : 'text-gray-400'\r\n                        }`}></i>\r\n                        <i className={`ri-arrow-down-s-line text-xs -mt-1 ${\r\n                          getSortDirection(String(column.key)) === 'desc' ? 'text-red-600' : 'text-gray-400'\r\n                        }`}></i>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </th>\r\n              ))}\r\n            </tr>\r\n          </thead>\r\n          <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\r\n            {loading ? (\r\n              <tr>\r\n                <td colSpan={columns.length} className=\"px-6 py-4 text-center text-gray-500 dark:text-gray-400\">\r\n                  <div className=\"flex items-center justify-center\">\r\n                    <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-red-600\"></div>\r\n                    <span className=\"ml-2\">Loading...</span>\r\n                  </div>\r\n                </td>\r\n              </tr>\r\n            ) : !data?.data || data.data.length === 0 ? (\r\n              <tr>\r\n                <td colSpan={columns.length} className=\"px-6 py-4 text-center text-gray-500 dark:text-gray-400\">\r\n                  No data found\r\n                </td>\r\n              </tr>\r\n            ) : (\r\n              data.data.map((item, index) => (\r\n                <tr key={index} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\r\n                  {columns.map((column) => (\r\n                    <td key={String(column.key)} className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100\">\r\n                      {column.render\r\n                        ? column.render(item[column.key as keyof T], item)\r\n                        : String(item[column.key as keyof T] || '')\r\n                      }\r\n                    </td>\r\n                  ))}\r\n                </tr>\r\n              ))\r\n            )}\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n\r\n      {/* Pagination */}\r\n      {data?.meta && (\r\n        <Pagination\r\n          meta={data.meta}\r\n          onPageChange={handlePageChange}\r\n          onPageSizeChange={handlePageSizeChange}\r\n          showFirstLast={true}\r\n          showPageSizeSelector={true}\r\n          showInfo={true}\r\n          maxVisiblePages={7}\r\n          pageSizeOptions={[10, 25, 50, 100]}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;AAwBe,SAAS,UAAyC,EAC/D,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,aAAa,EACb,oBAAoB,WAAW,EAC/B,YAAY,EAAE,EACI;IAClB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QAChD,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ,EAAE;IACZ;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,WAAW;YAC3B,IAAI,gBAAgB,MAAM,MAAM,EAAE;gBAChC,aAAa;YACf;QACF,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAY;IAEhB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,IAAI;YACF,MAAM,WAAW;gBAAE,GAAG,KAAK;gBAAE;gBAAQ,MAAM;YAAE;YAC7C,SAAS;YACT,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF,GAAG;QAAC;QAAO;KAAc;IAEzB,MAAM,aAAa,CAAC;QAClB,MAAM,cAAc,MAAM,MAAM,EAAE,KAAK,CAAA,OAAQ,KAAK,UAAU,CAAC;QAC/D,IAAI,YAAsB,EAAE;QAE5B,IAAI,CAAC,aAAa;YAChB,YAAY;gBAAC,GAAG,UAAU,IAAI,CAAC;aAAC;QAClC,OAAO,IAAI,YAAY,QAAQ,CAAC,SAAS;YACvC,YAAY;gBAAC,GAAG,UAAU,KAAK,CAAC;aAAC;QACnC,OAAO;YACL,YAAY,EAAE;QAChB;QAEA,MAAM,WAAW;YAAE,GAAG,KAAK;YAAE,QAAQ;YAAW,MAAM;QAAE;QACxD,SAAS;QACT,cAAc;IAChB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,WAAW;YAAE,GAAG,KAAK;YAAE;QAAK;QAClC,SAAS;QACT,cAAc;IAChB;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,WAAW;YAAE,GAAG,KAAK;YAAE;YAAO,MAAM;QAAE;QAC5C,SAAS;QACT,cAAc;IAChB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,cAAc,MAAM,MAAM,EAAE,KAAK,CAAA,OAAQ,KAAK,UAAU,CAAC;QAC/D,IAAI,CAAC,aAAa,OAAO;QACzB,OAAO,YAAY,QAAQ,CAAC,UAAU,QAAQ;IAChD;IAEA,8BAA8B;IAC9B,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAW,CAAC,4DAA4D,EAAE,WAAW;sBACxF,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAK,WAAU;sCAAO;;;;;;;;;;;;;;;;;;;;;;IAKjC;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,WAAW;YAAE,GAAG,KAAK;YAAE,OAAO;YAAa,MAAM;QAAE;QACzD,SAAS;QACT,cAAc;IAChB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,4DAA4D,EAAE,WAAW;;0BAExF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;;;;;;;;;;sCAEf,8OAAC;4BACC,MAAK;4BACL,aAAa;4BACb,OAAO;4BACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4BAC9C,WAAU;;;;;;;;;;;;;;;;;0BAMhB,8OAAC;gBAAI,WAAU;gBAAkB,OAAO;oBAAE,WAAW;gBAAQ;0BAC3D,cAAA,8OAAC;oBAAM,WAAU;;sCACf,8OAAC;4BAAM,WAAU;sCACf,cAAA,8OAAC;0CACE,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;wCAEC,WAAW,CAAC,kGAAkG,EAC5G,OAAO,QAAQ,GAAG,4DAA4D,GAC/E,CAAC,EAAE,OAAO,SAAS,IAAI,IAAI;wCAC5B,SAAS,IAAM,OAAO,QAAQ,IAAI,WAAW,OAAO,OAAO,GAAG;kDAE9D,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAM,OAAO,KAAK;;;;;;gDAClB,OAAO,QAAQ,kBACd,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAW,CAAC,2BAA2B,EACxC,iBAAiB,OAAO,OAAO,GAAG,OAAO,QAAQ,iBAAiB,iBAClE;;;;;;sEACF,8OAAC;4DAAE,WAAW,CAAC,mCAAmC,EAChD,iBAAiB,OAAO,OAAO,GAAG,OAAO,SAAS,iBAAiB,iBACnE;;;;;;;;;;;;;;;;;;uCAfH,OAAO,OAAO,GAAG;;;;;;;;;;;;;;;sCAuB9B,8OAAC;4BAAM,WAAU;sCACd,wBACC,8OAAC;0CACC,cAAA,8OAAC;oCAAG,SAAS,QAAQ,MAAM;oCAAE,WAAU;8CACrC,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAAO;;;;;;;;;;;;;;;;;;;;;uCAI3B,CAAC,MAAM,QAAQ,KAAK,IAAI,CAAC,MAAM,KAAK,kBACtC,8OAAC;0CACC,cAAA,8OAAC;oCAAG,SAAS,QAAQ,MAAM;oCAAE,WAAU;8CAAyD;;;;;;;;;;uCAKlG,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC;oCAAe,WAAU;8CACvB,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;4CAA4B,WAAU;sDACpC,OAAO,MAAM,GACV,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,CAAY,EAAE,QAC3C,OAAO,IAAI,CAAC,OAAO,GAAG,CAAY,IAAI;2CAHnC,OAAO,OAAO,GAAG;;;;;mCAFrB;;;;;;;;;;;;;;;;;;;;;YAiBlB,MAAM,sBACL,8OAAC,0IAAA,CAAA,UAAU;gBACT,MAAM,KAAK,IAAI;gBACf,cAAc;gBACd,kBAAkB;gBAClB,eAAe;gBACf,sBAAsB;gBACtB,UAAU;gBACV,iBAAiB;gBACjB,iBAAiB;oBAAC;oBAAI;oBAAI;oBAAI;iBAAI;;;;;;;;;;;;AAK5C", "debugId": null}}, {"offset": {"line": 3061, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/Select.tsx"], "sourcesContent": ["'use client';\n\nimport React, { forwardRef } from 'react';\n\ninterface SelectOption {\n  value: string;\n  label: string;\n  disabled?: boolean;\n}\n\ninterface SelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'onChange'> {\n  label?: string;\n  error?: string;\n  helperText?: string;\n  required?: boolean;\n  options: SelectOption[];\n  placeholder?: string;\n  className?: string;\n  containerClassName?: string;\n  onChange?: (value: string) => void;\n}\n\nconst Select = forwardRef<HTMLSelectElement, SelectProps>(({\n  label,\n  error,\n  helperText,\n  required = false,\n  options = [],\n  placeholder = 'Select an option...',\n  className = '',\n  containerClassName = '',\n  onChange,\n  id,\n  value,\n  ...props\n}, ref) => {\n  const selectId = id || `select-${Math.random().toString(36).substr(2, 9)}`;\n  \n  const baseSelectClasses = `\n    w-full px-3 py-2 border rounded-md shadow-sm \n    focus:outline-none focus:ring-2 focus:ring-offset-2 \n    disabled:opacity-50 disabled:cursor-not-allowed\n    dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600\n    transition-colors duration-200\n    appearance-none bg-white\n    bg-no-repeat bg-right bg-[length:16px_16px]\n    pr-10\n  `;\n  \n  const selectClasses = error\n    ? `${baseSelectClasses} border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600`\n    : `${baseSelectClasses} border-gray-300 focus:border-primary focus:ring-primary dark:focus:border-primary dark:focus:ring-primary`;\n\n  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    if (onChange) {\n      onChange(e.target.value);\n    }\n  };\n\n  return (\n    <div className={`space-y-1 ${containerClassName}`}>\n      {label && (\n        <label \n          htmlFor={selectId}\n          className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\"\n        >\n          {label}\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\n        </label>\n      )}\n      \n      <div className=\"relative\">\n        <select\n          ref={ref}\n          id={selectId}\n          value={value || ''}\n          onChange={handleChange}\n          className={`${selectClasses} ${className}`}\n          {...props}\n        >\n          {placeholder && (\n            <option value=\"\" disabled>\n              {placeholder}\n            </option>\n          )}\n          \n          {options.map((option) => (\n            <option \n              key={option.value} \n              value={option.value}\n              disabled={option.disabled}\n            >\n              {option.label}\n            </option>\n          ))}\n        </select>\n        \n        {/* Custom dropdown arrow */}\n        <div className=\"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\">\n          <i className=\"ri-arrow-down-s-line text-gray-400 dark:text-gray-500\"></i>\n        </div>\n      </div>\n      \n      {error && (\n        <p className=\"text-sm text-red-600 dark:text-red-400 flex items-center\">\n          <i className=\"ri-error-warning-line mr-1\"></i>\n          {error}\n        </p>\n      )}\n      \n      {helperText && !error && (\n        <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n          {helperText}\n        </p>\n      )}\n    </div>\n  );\n});\n\nSelect.displayName = 'Select';\n\nexport default Select;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAsBA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAkC,CAAC,EACzD,KAAK,EACL,KAAK,EACL,UAAU,EACV,WAAW,KAAK,EAChB,UAAU,EAAE,EACZ,cAAc,qBAAqB,EACnC,YAAY,EAAE,EACd,qBAAqB,EAAE,EACvB,QAAQ,EACR,EAAE,EACF,KAAK,EACL,GAAG,OACJ,EAAE;IACD,MAAM,WAAW,MAAM,CAAC,OAAO,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAE1E,MAAM,oBAAoB,CAAC;;;;;;;;;EAS3B,CAAC;IAED,MAAM,gBAAgB,QAClB,GAAG,kBAAkB,2EAA2E,CAAC,GACjG,GAAG,kBAAkB,0GAA0G,CAAC;IAEpI,MAAM,eAAe,CAAC;QACpB,IAAI,UAAU;YACZ,SAAS,EAAE,MAAM,CAAC,KAAK;QACzB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,oBAAoB;;YAC9C,uBACC,8OAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,KAAK;wBACL,IAAI;wBACJ,OAAO,SAAS;wBAChB,UAAU;wBACV,WAAW,GAAG,cAAc,CAAC,EAAE,WAAW;wBACzC,GAAG,KAAK;;4BAER,6BACC,8OAAC;gCAAO,OAAM;gCAAG,QAAQ;0CACtB;;;;;;4BAIJ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;oCAEC,OAAO,OAAO,KAAK;oCACnB,UAAU,OAAO,QAAQ;8CAExB,OAAO,KAAK;mCAJR,OAAO,KAAK;;;;;;;;;;;kCAUvB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;;YAIhB,uBACC,8OAAC;gBAAE,WAAU;;kCACX,8OAAC;wBAAE,WAAU;;;;;;oBACZ;;;;;;;YAIJ,cAAc,CAAC,uBACd,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;AAEA,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 3204, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/license/LicenseManagementTable.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { useAuth } from '../../contexts/AuthContext';\r\nimport { Application, ApplicationStatus } from '../../types/license';\r\nimport { applicationService } from '../../services/applicationService';\r\nimport { licenseCategoryService, LicenseCategory } from '../../services/licenseCategoryService';\r\nimport { licenseTypeService, LicenseType } from '../../services/licenseTypeService';\r\nimport AssignButton from '../common/AssignButton';\r\nimport ApplicationViewModal from './ApplicationViewModal';\r\nimport { PaginateQuery } from '../../services/userService';\r\nimport DataTable from '../common/DataTable';\r\nimport Select from '../common/Select';\r\n\r\ninterface LicenseManagementTableProps {\r\n  licenseTypeId?: string;\r\n  licenseTypeCode?: string; // Primary filter by license type code\r\n  licenseTypeFilter?: string; // Filter by license type name (fallback)\r\n  title: string;\r\n  description: string;\r\n  searchPlaceholder: string;\r\n  emptyStateIcon: string;\r\n  emptyStateMessage: string;\r\n  departmentType?: string; // Department type for navigation\r\n}\r\n\r\nexport default function LicenseManagementTable({\r\n  licenseTypeId,\r\n  licenseTypeCode,\r\n  licenseTypeFilter,\r\n  title,\r\n  description,\r\n  searchPlaceholder,\r\n  emptyStateIcon,\r\n  emptyStateMessage,\r\n  departmentType,\r\n}: LicenseManagementTableProps) {\r\n  const { user } = useAuth();\r\n  const router = useRouter();\r\n  const [applicationsData, setApplicationsData] = useState<any>(null);\r\n  const [licenseCategories, setLicenseCategories] = useState<LicenseCategory[]>([]);\r\n  const [licenseTypes, setLicenseTypes] = useState<LicenseType[]>([]);\r\n  const [resolvedLicenseTypeId, setResolvedLicenseTypeId] = useState<string | undefined>(undefined);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  const [selectedLicenseCategory, setSelectedLicenseCategory] = useState('');\r\n  const [statusFilter, setStatusFilter] = useState<ApplicationStatus | ''>('');\r\n  const [dateRangeFilter, setDateRangeFilter] = useState('');\r\n\r\n  // View modal state\r\n  const [showViewModal, setShowViewModal] = useState(false);\r\n  const [selectedApplicationId, setSelectedApplicationId] = useState<string | null>(null);\r\n\r\n  const isAdmin = user?.isAdmin;\r\n\r\n  // Function for viewing applications - opens modal\r\n  const handleViewApplication = (applicationId: string) => {\r\n    setSelectedApplicationId(applicationId);\r\n    setShowViewModal(true);\r\n  };\r\n\r\n  const handleCloseViewModal = () => {\r\n    setShowViewModal(false);\r\n    setSelectedApplicationId(null);\r\n  };\r\n\r\n  const handleViewModalUpdate = () => {\r\n    // Refresh the applications list when modal updates data\r\n    loadApplications({ page: 1, limit: 10 });\r\n  };\r\n\r\n  // Load applications function for DataTable\r\n  const loadApplications = useCallback(async (query: PaginateQuery) => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      const effectiveLicenseTypeId = licenseTypeId || resolvedLicenseTypeId;\r\n\r\n      // Build filters object\r\n      const filters: any = {};\r\n\r\n      // Add license category filter if selected\r\n      if (selectedLicenseCategory) {\r\n        filters.licenseCategoryId = selectedLicenseCategory;\r\n      }\r\n\r\n      // Add license type filter - prioritize licenseTypeCode search\r\n      if (effectiveLicenseTypeId) {\r\n        filters.licenseTypeId = effectiveLicenseTypeId;\r\n      } else if (licenseTypeCode) {\r\n        // If we have a license type code but no resolved ID yet, add it as a filter\r\n        filters.licenseTypeCode = licenseTypeCode;\r\n      }\r\n\r\n      // Add status filter if selected\r\n      if (statusFilter) {\r\n        filters.status = statusFilter;\r\n      }\r\n\r\n      // Handle draft inclusion logic\r\n      if (statusFilter === ApplicationStatus.DRAFT) {\r\n        // If user specifically selected \"Draft\" status, include drafts\r\n        filters.include_draft = 'true';\r\n      } else {\r\n        // For all other cases, exclude drafts by default\r\n        filters.include_draft = 'false';\r\n      }\r\n\r\n      const params = {\r\n        page: query.page,\r\n        limit: query.limit,\r\n        search: query.search || undefined,\r\n        filters: Object.keys(filters).length > 0 ? filters : undefined,\r\n      };\r\n\r\n      console.log('Loading applications with params:', params);\r\n      const response = await applicationService.getApplications(params);\r\n      setApplicationsData(response);\r\n    } catch (err: any) {\r\n      console.error('Error loading applications:', err);\r\n      setError('Failed to load applications');\r\n      // Set empty data structure to prevent undefined errors\r\n      setApplicationsData({\r\n        data: [],\r\n        meta: {\r\n          itemsPerPage: query.limit || 10,\r\n          totalItems: 0,\r\n          currentPage: query.page || 1,\r\n          totalPages: 0,\r\n          sortBy: [],\r\n          searchBy: [],\r\n          search: '',\r\n          select: [],\r\n        },\r\n        links: {\r\n          current: '',\r\n        },\r\n      });\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [licenseTypeId, resolvedLicenseTypeId, selectedLicenseCategory, statusFilter, licenseTypeCode]);\r\n\r\n\r\n\r\n  // Load license types and resolve license type ID from code or filter name\r\n  useEffect(() => {\r\n    const fetchLicenseTypes = async () => {\r\n      try {\r\n        const response = await licenseTypeService.getAllLicenseTypes();\r\n        const types = Array.isArray(response) ? response : (response?.data || []);\r\n\r\n        if (!Array.isArray(types)) {\r\n          console.warn('License types response is not an array:', types);\r\n          setLicenseTypes([]);\r\n          return;\r\n        }\r\n\r\n        setLicenseTypes(types);\r\n\r\n        // Priority 1: If we have a licenseTypeCode, find the matching license type ID\r\n        if (licenseTypeCode && types.length > 0) {\r\n          const matchingType = types.find(type =>\r\n            type.code === licenseTypeCode\r\n          );\r\n          if (matchingType) {\r\n            setResolvedLicenseTypeId(matchingType.license_type_id);\r\n          }\r\n        }\r\n        // Priority 2: If we have a licenseTypeFilter (fallback), find the matching license type ID\r\n        else if (licenseTypeFilter && types.length > 0) {\r\n          const matchingType = types.find(type =>\r\n            type.name.toLowerCase().includes(licenseTypeFilter.toLowerCase())\r\n          );\r\n          if (matchingType) {\r\n            setResolvedLicenseTypeId(matchingType.license_type_id);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Error fetching license types:', error);\r\n        setLicenseTypes([]);\r\n      }\r\n    };\r\n\r\n    fetchLicenseTypes();\r\n  }, [licenseTypeCode, licenseTypeFilter]);\r\n\r\n  // Fetch license categories for the dropdown\r\n  useEffect(() => {\r\n    const fetchLicenseCategories = async () => {\r\n      try {\r\n        let categories: LicenseCategory[] = [];\r\n        const effectiveLicenseTypeId = licenseTypeId || resolvedLicenseTypeId;\r\n\r\n        if (effectiveLicenseTypeId) {\r\n          // Fetch categories for specific license type\r\n          const response = await licenseCategoryService.getLicenseCategoriesByType(effectiveLicenseTypeId);\r\n          categories = Array.isArray(response) ? response : (response?.data || []);\r\n        } else {\r\n          // Fetch all categories\r\n          const response = await licenseCategoryService.getAllLicenseCategories();\r\n          categories = Array.isArray(response) ? response : (response?.data || []);\r\n        }\r\n\r\n        if (!Array.isArray(categories)) {\r\n          console.warn('License categories response is not an array:', categories);\r\n          categories = [];\r\n        }\r\n\r\n        setLicenseCategories(categories);\r\n      } catch (error) {\r\n        console.error('Error fetching license categories:', error);\r\n        setLicenseCategories([]);\r\n      }\r\n    };\r\n\r\n    if (resolvedLicenseTypeId || licenseTypeId) {\r\n      fetchLicenseCategories();\r\n    }\r\n  }, [licenseTypeId, resolvedLicenseTypeId]);\r\n\r\n  // Load applications on component mount and when filters change\r\n  useEffect(() => {\r\n    if (resolvedLicenseTypeId || licenseTypeId) {\r\n      loadApplications({ page: 1, limit: 10 });\r\n    }\r\n  }, [loadApplications, resolvedLicenseTypeId, licenseTypeId]);\r\n\r\n  const getStatusBadge = (status: ApplicationStatus) => {\r\n    const statusClasses: Record<ApplicationStatus, string> = {\r\n      [ApplicationStatus.DRAFT]: 'bg-gray-100 text-gray-800',\r\n      [ApplicationStatus.SUBMITTED]: 'bg-blue-100 text-blue-800',\r\n      [ApplicationStatus.UNDER_REVIEW]: 'bg-yellow-100 text-yellow-800',\r\n      [ApplicationStatus.EVALUATION]: 'bg-purple-100 text-purple-800',\r\n      [ApplicationStatus.APPROVED]: 'bg-green-100 text-green-800',\r\n      [ApplicationStatus.REJECTED]: 'bg-red-100 text-red-800',\r\n      [ApplicationStatus.WITHDRAWN]: 'bg-gray-100 text-gray-800',\r\n    };\r\n\r\n    const statusLabels: Record<ApplicationStatus, string> = {\r\n      [ApplicationStatus.DRAFT]: 'Draft',\r\n      [ApplicationStatus.SUBMITTED]: 'Submitted',\r\n      [ApplicationStatus.UNDER_REVIEW]: 'Under Review',\r\n      [ApplicationStatus.EVALUATION]: 'Evaluation',\r\n      [ApplicationStatus.APPROVED]: 'Approved',\r\n      [ApplicationStatus.REJECTED]: 'Rejected',\r\n      [ApplicationStatus.WITHDRAWN]: 'Withdrawn',\r\n    };\r\n\r\n    return (\r\n      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClasses[status]}`}>\r\n        {statusLabels[status]}\r\n      </span>\r\n    );\r\n  };\r\n\r\n  // Define columns for applications table\r\n  const applicationColumns = [\r\n    {\r\n      key: 'application_number',\r\n      label: 'Application Number',\r\n      sortable: true,\r\n      render: (value: string) => (\r\n        <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n          {value}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'applicant',\r\n      label: 'Applicant',\r\n      render: (value: any, application: Application) => (\r\n        <div className=\"flex items-center\">\r\n          <div className=\"flex-shrink-0 h-10 w-10\">\r\n            <div className=\"h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center\">\r\n              <i className=\"ri-building-line text-blue-600 dark:text-blue-400\"></i>\r\n            </div>\r\n          </div>\r\n          <div className=\"ml-4\">\r\n            <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n              {application.applicant?.name || 'N/A'}\r\n            </div>\r\n            <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n              BRN: {application.applicant?.business_registration_number || 'N/A'} |\r\n              TPIN: {application.applicant?.tpin || 'N/A'}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'license_category',\r\n      label: 'License Category',\r\n      render: (value: any, application: Application) => (\r\n        <div>\r\n          <div className=\"text-sm text-gray-900 dark:text-gray-100\">\r\n            {application.license_category?.name || 'N/A'}\r\n          </div>\r\n          <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n            {application.license_category?.license_type?.name || 'N/A'}\r\n          </div>\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'status',\r\n      label: 'Status',\r\n      render: (value: ApplicationStatus) => getStatusBadge(value),\r\n    },\r\n    {\r\n      key: 'progress_percentage',\r\n      label: 'Progress',\r\n      render: (value: number) => getProgressBar(value),\r\n    },\r\n    {\r\n      key: 'submitted_at',\r\n      label: 'Submitted Date',\r\n      sortable: true,\r\n      render: (value: string, application: Application) => (\r\n        <span className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n          {application.submitted_at\r\n            ? new Date(application.submitted_at).toLocaleDateString()\r\n            : new Date(application.created_at).toLocaleDateString()\r\n          }\r\n        </span>\r\n      ),\r\n    },\r\n    {\r\n      key: 'actions',\r\n      label: 'Actions',\r\n      render: (value: any, application: Application) => (\r\n        <div className=\"flex items-center justify-end space-x-2\">\r\n          <button\r\n            type=\"button\"\r\n            onClick={() => handleViewApplication(application.application_id)}\r\n            className=\"inline-flex items-center px-3 py-1 text-xs font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/40 transition-colors\"\r\n          >\r\n            <i className=\"ri-eye-line mr-1\"></i>\r\n            View\r\n          </button>\r\n          {application.assigned_to ? (\r\n            <div\r\n              className=\"inline-flex items-center px-3 py-1 text-xs font-medium text-purple-600 dark:text-purple-400 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-md\"\r\n              title={application.assignee ? `Assigned to: ${application.assignee.first_name} ${application.assignee.last_name}` : 'Assigned'}\r\n            >\r\n              <i className=\"ri-user-check-line mr-1\"></i>\r\n              Assigned\r\n            </div>\r\n          ) : (\r\n            <AssignButton\r\n              itemId={application.application_id}\r\n              itemType=\"application\"\r\n              itemTitle={application.application_number}\r\n              onAssignSuccess={handleAssignSuccess}\r\n              className=\"text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 hover:bg-green-100 dark:hover:bg-green-900/40\"\r\n            />\r\n          )}\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  const getProgressBar = (percentage: number) => {\r\n    const getProgressColor = (percent: number) => {\r\n      if (percent >= 100) return 'bg-green-600';\r\n      if (percent >= 75) return 'bg-blue-600';\r\n      if (percent >= 50) return 'bg-yellow-600';\r\n      if (percent >= 25) return 'bg-orange-600';\r\n      return 'bg-red-600';\r\n    };\r\n\r\n    return (\r\n      <div className=\"flex items-center\">\r\n        <div className=\"w-16 bg-gray-200 rounded-full h-2 mr-2\">\r\n          <div \r\n            className={`h-2 rounded-full ${getProgressColor(percentage)}`} \r\n            style={{ width: `${percentage}%` }}\r\n          ></div>\r\n        </div>\r\n        <span className=\"text-sm text-gray-500\">{percentage}%</span>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  const handleAssignSuccess = () => {\r\n    // Refresh the applications list when assignment is successful\r\n    loadApplications({ page: 1, limit: 10 });\r\n  };\r\n\r\n  const handleFilterChange = (filterType: string, value: string) => {\r\n    switch (filterType) {\r\n      case 'licenseCategory':\r\n        setSelectedLicenseCategory(value);\r\n        break;\r\n      case 'status':\r\n        setStatusFilter(value as ApplicationStatus | '');\r\n        break;\r\n      case 'dateRange':\r\n        setDateRangeFilter(value);\r\n        break;\r\n    }\r\n    // Reload applications with new filters\r\n    loadApplications({ page: 1, limit: 10 });\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-6\">\r\n      <div className=\"max-w-7xl mx-auto\">\r\n        {/* Page header */}\r\n        <div className=\"mb-8\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <h1 className=\"text-3xl font-bold text-gray-900 dark:text-gray-100\">{title}</h1>\r\n              <p className=\"mt-2 text-sm text-gray-600 dark:text-gray-400\">\r\n                {description}\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Filters Section */}\r\n        <div className=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6\">\r\n          <h2 className=\"text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4\">Filters</h2>\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n            {/* License Category Filter */}\r\n            <Select\r\n              label=\"License Category\"\r\n              value={selectedLicenseCategory}\r\n              onChange={(value) => handleFilterChange('licenseCategory', value)}\r\n              options={[\r\n                { value: '', label: 'All Categories' },\r\n                ...licenseCategories.map(category => ({\r\n                  value: category.license_category_id,\r\n                  label: category.name\r\n                }))\r\n              ]}\r\n            />\r\n\r\n            {/* Status Filter */}\r\n            <Select\r\n              label=\"Application Status\"\r\n              value={statusFilter}\r\n              onChange={(value) => handleFilterChange('status', value)}\r\n              options={[\r\n                { value: '', label: 'All Statuses' },\r\n                { value: ApplicationStatus.DRAFT, label: 'Draft' },\r\n                { value: ApplicationStatus.SUBMITTED, label: 'Submitted' },\r\n                { value: ApplicationStatus.UNDER_REVIEW, label: 'Under Review' },\r\n                { value: ApplicationStatus.EVALUATION, label: 'Evaluation' },\r\n                { value: ApplicationStatus.APPROVED, label: 'Approved' },\r\n                { value: ApplicationStatus.REJECTED, label: 'Rejected' },\r\n                { value: ApplicationStatus.WITHDRAWN, label: 'Withdrawn' }\r\n              ]}\r\n            />\r\n\r\n            {/* Date Range Filter */}\r\n            <Select\r\n              label=\"Date Range\"\r\n              value={dateRangeFilter}\r\n              onChange={(value) => handleFilterChange('dateRange', value)}\r\n              options={[\r\n                { value: '', label: 'All Time' },\r\n                { value: 'last-30', label: 'Last 30 Days' },\r\n                { value: 'last-90', label: 'Last 90 Days' },\r\n                { value: 'last-year', label: 'Last Year' }\r\n              ]}\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n\r\n\r\n        {/* Applications Table */}\r\n        <DataTable\r\n          columns={applicationColumns}\r\n          data={applicationsData}\r\n          loading={loading}\r\n          onQueryChange={loadApplications}\r\n          searchPlaceholder={searchPlaceholder}\r\n        />\r\n\r\n        {/* Application View Modal */}\r\n        <ApplicationViewModal\r\n          isOpen={showViewModal}\r\n          onClose={handleCloseViewModal}\r\n          applicationId={selectedApplicationId}\r\n          departmentType={departmentType}\r\n          licenseTypeCode={licenseTypeCode}\r\n          onUpdate={handleViewModalUpdate}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAbA;;;;;;;;;;;;;AA2Be,SAAS,uBAAuB,EAC7C,aAAa,EACb,eAAe,EACf,iBAAiB,EACjB,KAAK,EACL,WAAW,EACX,iBAAiB,EACjB,cAAc,EACd,iBAAiB,EACjB,cAAc,EACc;IAC5B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC9D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAChF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACvF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IACzE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,mBAAmB;IACnB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElF,MAAM,UAAU,MAAM;IAEtB,kDAAkD;IAClD,MAAM,wBAAwB,CAAC;QAC7B,yBAAyB;QACzB,iBAAiB;IACnB;IAEA,MAAM,uBAAuB;QAC3B,iBAAiB;QACjB,yBAAyB;IAC3B;IAEA,MAAM,wBAAwB;QAC5B,wDAAwD;QACxD,iBAAiB;YAAE,MAAM;YAAG,OAAO;QAAG;IACxC;IAEA,2CAA2C;IAC3C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC1C,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,yBAAyB,iBAAiB;YAEhD,uBAAuB;YACvB,MAAM,UAAe,CAAC;YAEtB,0CAA0C;YAC1C,IAAI,yBAAyB;gBAC3B,QAAQ,iBAAiB,GAAG;YAC9B;YAEA,8DAA8D;YAC9D,IAAI,wBAAwB;gBAC1B,QAAQ,aAAa,GAAG;YAC1B,OAAO,IAAI,iBAAiB;gBAC1B,4EAA4E;gBAC5E,QAAQ,eAAe,GAAG;YAC5B;YAEA,gCAAgC;YAChC,IAAI,cAAc;gBAChB,QAAQ,MAAM,GAAG;YACnB;YAEA,+BAA+B;YAC/B,IAAI,iBAAiB,uHAAA,CAAA,oBAAiB,CAAC,KAAK,EAAE;gBAC5C,+DAA+D;gBAC/D,QAAQ,aAAa,GAAG;YAC1B,OAAO;gBACL,iDAAiD;gBACjD,QAAQ,aAAa,GAAG;YAC1B;YAEA,MAAM,SAAS;gBACb,MAAM,MAAM,IAAI;gBAChB,OAAO,MAAM,KAAK;gBAClB,QAAQ,MAAM,MAAM,IAAI;gBACxB,SAAS,OAAO,IAAI,CAAC,SAAS,MAAM,GAAG,IAAI,UAAU;YACvD;YAEA,QAAQ,GAAG,CAAC,qCAAqC;YACjD,MAAM,WAAW,MAAM,qIAAA,CAAA,qBAAkB,CAAC,eAAe,CAAC;YAC1D,oBAAoB;QACtB,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,SAAS;YACT,uDAAuD;YACvD,oBAAoB;gBAClB,MAAM,EAAE;gBACR,MAAM;oBACJ,cAAc,MAAM,KAAK,IAAI;oBAC7B,YAAY;oBACZ,aAAa,MAAM,IAAI,IAAI;oBAC3B,YAAY;oBACZ,QAAQ,EAAE;oBACV,UAAU,EAAE;oBACZ,QAAQ;oBACR,QAAQ,EAAE;gBACZ;gBACA,OAAO;oBACL,SAAS;gBACX;YACF;QACF,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;QAAe;QAAuB;QAAyB;QAAc;KAAgB;IAIjG,0EAA0E;IAC1E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB;YACxB,IAAI;gBACF,MAAM,WAAW,MAAM,qIAAA,CAAA,qBAAkB,CAAC,kBAAkB;gBAC5D,MAAM,QAAQ,MAAM,OAAO,CAAC,YAAY,WAAY,UAAU,QAAQ,EAAE;gBAExE,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ;oBACzB,QAAQ,IAAI,CAAC,2CAA2C;oBACxD,gBAAgB,EAAE;oBAClB;gBACF;gBAEA,gBAAgB;gBAEhB,8EAA8E;gBAC9E,IAAI,mBAAmB,MAAM,MAAM,GAAG,GAAG;oBACvC,MAAM,eAAe,MAAM,IAAI,CAAC,CAAA,OAC9B,KAAK,IAAI,KAAK;oBAEhB,IAAI,cAAc;wBAChB,yBAAyB,aAAa,eAAe;oBACvD;gBACF,OAEK,IAAI,qBAAqB,MAAM,MAAM,GAAG,GAAG;oBAC9C,MAAM,eAAe,MAAM,IAAI,CAAC,CAAA,OAC9B,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,kBAAkB,WAAW;oBAEhE,IAAI,cAAc;wBAChB,yBAAyB,aAAa,eAAe;oBACvD;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,gBAAgB,EAAE;YACpB;QACF;QAEA;IACF,GAAG;QAAC;QAAiB;KAAkB;IAEvC,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,yBAAyB;YAC7B,IAAI;gBACF,IAAI,aAAgC,EAAE;gBACtC,MAAM,yBAAyB,iBAAiB;gBAEhD,IAAI,wBAAwB;oBAC1B,6CAA6C;oBAC7C,MAAM,WAAW,MAAM,yIAAA,CAAA,yBAAsB,CAAC,0BAA0B,CAAC;oBACzE,aAAa,MAAM,OAAO,CAAC,YAAY,WAAY,UAAU,QAAQ,EAAE;gBACzE,OAAO;oBACL,uBAAuB;oBACvB,MAAM,WAAW,MAAM,yIAAA,CAAA,yBAAsB,CAAC,uBAAuB;oBACrE,aAAa,MAAM,OAAO,CAAC,YAAY,WAAY,UAAU,QAAQ,EAAE;gBACzE;gBAEA,IAAI,CAAC,MAAM,OAAO,CAAC,aAAa;oBAC9B,QAAQ,IAAI,CAAC,gDAAgD;oBAC7D,aAAa,EAAE;gBACjB;gBAEA,qBAAqB;YACvB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sCAAsC;gBACpD,qBAAqB,EAAE;YACzB;QACF;QAEA,IAAI,yBAAyB,eAAe;YAC1C;QACF;IACF,GAAG;QAAC;QAAe;KAAsB;IAEzC,+DAA+D;IAC/D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,yBAAyB,eAAe;YAC1C,iBAAiB;gBAAE,MAAM;gBAAG,OAAO;YAAG;QACxC;IACF,GAAG;QAAC;QAAkB;QAAuB;KAAc;IAE3D,MAAM,iBAAiB,CAAC;QACtB,MAAM,gBAAmD;YACvD,CAAC,uHAAA,CAAA,oBAAiB,CAAC,KAAK,CAAC,EAAE;YAC3B,CAAC,uHAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,EAAE;YAC/B,CAAC,uHAAA,CAAA,oBAAiB,CAAC,YAAY,CAAC,EAAE;YAClC,CAAC,uHAAA,CAAA,oBAAiB,CAAC,UAAU,CAAC,EAAE;YAChC,CAAC,uHAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,EAAE;YAC9B,CAAC,uHAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,EAAE;YAC9B,CAAC,uHAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,EAAE;QACjC;QAEA,MAAM,eAAkD;YACtD,CAAC,uHAAA,CAAA,oBAAiB,CAAC,KAAK,CAAC,EAAE;YAC3B,CAAC,uHAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,EAAE;YAC/B,CAAC,uHAAA,CAAA,oBAAiB,CAAC,YAAY,CAAC,EAAE;YAClC,CAAC,uHAAA,CAAA,oBAAiB,CAAC,UAAU,CAAC,EAAE;YAChC,CAAC,uHAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,EAAE;YAC9B,CAAC,uHAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,EAAE;YAC9B,CAAC,uHAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,EAAE;QACjC;QAEA,qBACE,8OAAC;YAAK,WAAW,CAAC,8DAA8D,EAAE,aAAa,CAAC,OAAO,EAAE;sBACtG,YAAY,CAAC,OAAO;;;;;;IAG3B;IAEA,wCAAwC;IACxC,MAAM,qBAAqB;QACzB;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,sBACP,8OAAC;oBAAI,WAAU;8BACZ;;;;;;QAGP;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,OAAY,4BACnB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;;;;;;sCAGjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,YAAY,SAAS,EAAE,QAAQ;;;;;;8CAElC,8OAAC;oCAAI,WAAU;;wCAA2C;wCAClD,YAAY,SAAS,EAAE,gCAAgC;wCAAM;wCAC5D,YAAY,SAAS,EAAE,QAAQ;;;;;;;;;;;;;;;;;;;QAKhD;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,OAAY,4BACnB,8OAAC;;sCACC,8OAAC;4BAAI,WAAU;sCACZ,YAAY,gBAAgB,EAAE,QAAQ;;;;;;sCAEzC,8OAAC;4BAAI,WAAU;sCACZ,YAAY,gBAAgB,EAAE,cAAc,QAAQ;;;;;;;;;;;;QAI7D;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,QAA6B,eAAe;QACvD;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,QAAkB,eAAe;QAC5C;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,OAAe,4BACtB,8OAAC;oBAAK,WAAU;8BACb,YAAY,YAAY,GACrB,IAAI,KAAK,YAAY,YAAY,EAAE,kBAAkB,KACrD,IAAI,KAAK,YAAY,UAAU,EAAE,kBAAkB;;;;;;QAI7D;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,OAAY,4BACnB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,MAAK;4BACL,SAAS,IAAM,sBAAsB,YAAY,cAAc;4BAC/D,WAAU;;8CAEV,8OAAC;oCAAE,WAAU;;;;;;gCAAuB;;;;;;;wBAGrC,YAAY,WAAW,iBACtB,8OAAC;4BACC,WAAU;4BACV,OAAO,YAAY,QAAQ,GAAG,CAAC,aAAa,EAAE,YAAY,QAAQ,CAAC,UAAU,CAAC,CAAC,EAAE,YAAY,QAAQ,CAAC,SAAS,EAAE,GAAG;;8CAEpH,8OAAC;oCAAE,WAAU;;;;;;gCAA8B;;;;;;iDAI7C,8OAAC,4IAAA,CAAA,UAAY;4BACX,QAAQ,YAAY,cAAc;4BAClC,UAAS;4BACT,WAAW,YAAY,kBAAkB;4BACzC,iBAAiB;4BACjB,WAAU;;;;;;;;;;;;QAKpB;KACD;IAED,MAAM,iBAAiB,CAAC;QACtB,MAAM,mBAAmB,CAAC;YACxB,IAAI,WAAW,KAAK,OAAO;YAC3B,IAAI,WAAW,IAAI,OAAO;YAC1B,IAAI,WAAW,IAAI,OAAO;YAC1B,IAAI,WAAW,IAAI,OAAO;YAC1B,OAAO;QACT;QAEA,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,WAAW,CAAC,iBAAiB,EAAE,iBAAiB,aAAa;wBAC7D,OAAO;4BAAE,OAAO,GAAG,WAAW,CAAC,CAAC;wBAAC;;;;;;;;;;;8BAGrC,8OAAC;oBAAK,WAAU;;wBAAyB;wBAAW;;;;;;;;;;;;;IAG1D;IAEA,MAAM,sBAAsB;QAC1B,8DAA8D;QAC9D,iBAAiB;YAAE,MAAM;YAAG,OAAO;QAAG;IACxC;IAEA,MAAM,qBAAqB,CAAC,YAAoB;QAC9C,OAAQ;YACN,KAAK;gBACH,2BAA2B;gBAC3B;YACF,KAAK;gBACH,gBAAgB;gBAChB;YACF,KAAK;gBACH,mBAAmB;gBACnB;QACJ;QACA,uCAAuC;QACvC,iBAAiB;YAAE,MAAM;YAAG,OAAO;QAAG;IACxC;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAuD;;;;;;8CACrE,8OAAC;oCAAE,WAAU;8CACV;;;;;;;;;;;;;;;;;;;;;;8BAOT,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA8D;;;;;;sCAC5E,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,sIAAA,CAAA,UAAM;oCACL,OAAM;oCACN,OAAO;oCACP,UAAU,CAAC,QAAU,mBAAmB,mBAAmB;oCAC3D,SAAS;wCACP;4CAAE,OAAO;4CAAI,OAAO;wCAAiB;2CAClC,kBAAkB,GAAG,CAAC,CAAA,WAAY,CAAC;gDACpC,OAAO,SAAS,mBAAmB;gDACnC,OAAO,SAAS,IAAI;4CACtB,CAAC;qCACF;;;;;;8CAIH,8OAAC,sIAAA,CAAA,UAAM;oCACL,OAAM;oCACN,OAAO;oCACP,UAAU,CAAC,QAAU,mBAAmB,UAAU;oCAClD,SAAS;wCACP;4CAAE,OAAO;4CAAI,OAAO;wCAAe;wCACnC;4CAAE,OAAO,uHAAA,CAAA,oBAAiB,CAAC,KAAK;4CAAE,OAAO;wCAAQ;wCACjD;4CAAE,OAAO,uHAAA,CAAA,oBAAiB,CAAC,SAAS;4CAAE,OAAO;wCAAY;wCACzD;4CAAE,OAAO,uHAAA,CAAA,oBAAiB,CAAC,YAAY;4CAAE,OAAO;wCAAe;wCAC/D;4CAAE,OAAO,uHAAA,CAAA,oBAAiB,CAAC,UAAU;4CAAE,OAAO;wCAAa;wCAC3D;4CAAE,OAAO,uHAAA,CAAA,oBAAiB,CAAC,QAAQ;4CAAE,OAAO;wCAAW;wCACvD;4CAAE,OAAO,uHAAA,CAAA,oBAAiB,CAAC,QAAQ;4CAAE,OAAO;wCAAW;wCACvD;4CAAE,OAAO,uHAAA,CAAA,oBAAiB,CAAC,SAAS;4CAAE,OAAO;wCAAY;qCAC1D;;;;;;8CAIH,8OAAC,sIAAA,CAAA,UAAM;oCACL,OAAM;oCACN,OAAO;oCACP,UAAU,CAAC,QAAU,mBAAmB,aAAa;oCACrD,SAAS;wCACP;4CAAE,OAAO;4CAAI,OAAO;wCAAW;wCAC/B;4CAAE,OAAO;4CAAW,OAAO;wCAAe;wCAC1C;4CAAE,OAAO;4CAAW,OAAO;wCAAe;wCAC1C;4CAAE,OAAO;4CAAa,OAAO;wCAAY;qCAC1C;;;;;;;;;;;;;;;;;;8BAQP,8OAAC,yIAAA,CAAA,UAAS;oBACR,SAAS;oBACT,MAAM;oBACN,SAAS;oBACT,eAAe;oBACf,mBAAmB;;;;;;8BAIrB,8OAAC,qJAAA,CAAA,UAAoB;oBACnB,QAAQ;oBACR,SAAS;oBACT,eAAe;oBACf,gBAAgB;oBAChB,iBAAiB;oBACjB,UAAU;;;;;;;;;;;;;;;;;AAKpB", "debugId": null}}, {"offset": {"line": 3902, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/applications/%5Blicense-type%5D/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useState } from 'react';\r\nimport { useParams } from 'next/navigation';\r\nimport LicenseManagementTable from '../../../components/license/LicenseManagementTable';\r\nimport { LicenseType } from '@/types/license';\r\nimport { licenseTypeService } from '@/services/licenseTypeService';\r\n\r\nexport default function LicenseTypeApplicationsPage() {\r\n  const params = useParams();\r\n  const licenseTypeCode = params['license-type'] as string;\r\n  const [licenseType, setLicenseType] = useState<LicenseType | null>(null);\r\n\r\n  // Load required documents and existing uploads\r\n  useEffect(() => {\r\n    const fetchLicenseType = async () => {\r\n      try {\r\n        const licenseTypeResponse = await licenseTypeService.getLicenseTypeByCode(licenseTypeCode);\r\n        setLicenseType(licenseTypeResponse);\r\n      } catch (error) {\r\n        console.error('Error fetching license type:', error);\r\n      }\r\n    };\r\n\r\n    if (licenseTypeCode) {\r\n      fetchLicenseType();\r\n    }\r\n  }, [licenseTypeCode]);\r\n\r\n  return (\r\n    <LicenseManagementTable\r\n      licenseTypeCode={licenseTypeCode}\r\n      title={`${licenseType?.name || 'License'} Management`}\r\n      description={licenseType?.description || licenseType?.name || 'License management'}\r\n      searchPlaceholder={`Search ${licenseType?.name || 'license'} applications...`}\r\n      emptyStateIcon={'ri-mail-line'}\r\n      emptyStateMessage={'No applications are available.'}\r\n      departmentType={licenseType?.name}\r\n    />\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,kBAAkB,MAAM,CAAC,eAAe;IAC9C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAEnE,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI;gBACF,MAAM,sBAAsB,MAAM,qIAAA,CAAA,qBAAkB,CAAC,oBAAoB,CAAC;gBAC1E,eAAe;YACjB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;YAChD;QACF;QAEA,IAAI,iBAAiB;YACnB;QACF;IACF,GAAG;QAAC;KAAgB;IAEpB,qBACE,8OAAC,uJAAA,CAAA,UAAsB;QACrB,iBAAiB;QACjB,OAAO,GAAG,aAAa,QAAQ,UAAU,WAAW,CAAC;QACrD,aAAa,aAAa,eAAe,aAAa,QAAQ;QAC9D,mBAAmB,CAAC,OAAO,EAAE,aAAa,QAAQ,UAAU,gBAAgB,CAAC;QAC7E,gBAAgB;QAChB,mBAAmB;QACnB,gBAAgB,aAAa;;;;;;AAGnC", "debugId": null}}]}
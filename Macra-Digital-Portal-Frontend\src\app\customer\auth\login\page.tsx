'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { useAuth } from '@/contexts/AuthContext';
import { AuthLayout, StatusMessage, PageTransition } from '@/components/auth';

function LoginForm() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [fieldErrors, setFieldErrors] = useState<{email?: string; password?: string}>({});
  const [isCustomerPortal, setIsCustomerPortal] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const [requires2FA, setRequires2FA] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState('');

  const { login: staffLogin, isAuthenticated: staffAuthenticated, loading: staffLoading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Client-side initialization
  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    // Check if we're on customer portal - fix the logic completely
    // Only run on client side to avoid SSR issues
    if (isClient && typeof window !== 'undefined') {
      const port = window.location.port;
      // We're on customer portal if: URL path starts with /customer OR port is 3002 OR hostname includes 'customer'
      const customerPortal = window.location.pathname.startsWith('/customer') || 
                            window.location.hostname.includes('customer') ||
                            port === '3002';
      console.log('Portal detection:', { 
        port, 
        hostname: window.location.hostname, 
        pathname: window.location.pathname,
        customerPortal
      });
      setIsCustomerPortal(customerPortal);
    }

    const message = searchParams.get('message');
    if (message) {
      setSuccessMessage(message);
    }

    // Add timeout to prevent infinite loading - but don't redirect on timeout
    const loadingTimeout = setTimeout(() => {
      if (loading && !error) {
        console.warn('Login page loading timeout, resetting loading state');
        setLoading(false);
      }
    }, 10000); // 10 second timeout

    return () => clearTimeout(loadingTimeout);
  }, [searchParams, loading, isClient, error, isCustomerPortal, router]);

  // Redirect if already authenticated - only within customer portal, never to staff
  useEffect(() => {
    if (requires2FA) return;

    if (!staffLoading && !loading && !error && isCustomerPortal && isClient) {
      if (staffAuthenticated) {
        console.log('User already authenticated on customer portal, redirecting to /customer');
        router.replace('/customer');
      }
    }
  }, [requires2FA, isCustomerPortal, staffAuthenticated, staffLoading, loading, router, error, isClient]);

  const validateEmail = (email: string): string | null => {
    if (!email.trim()) {
      return 'Email address is required';
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return 'Please enter a valid email address';
    }

    return null;
  };

  const validatePassword = (password: string): string | null => {
    if (!password) {
      return 'Password is required';
    }

    if (password.length < 8) {
      return 'Password must be at least 8 characters long';
    }

    return null;
  };

  const validateForm = (): string | null => {
    const emailError = validateEmail(email);
    const passwordError = validatePassword(password);

    setFieldErrors({
      email: emailError || undefined,
      password: passwordError || undefined,
    });

    if (emailError) return emailError;
    if (passwordError) return passwordError;

    return null;
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setEmail(value);

    // Clear field error when user starts typing
    if (fieldErrors.email) {
      setFieldErrors(prev => ({ ...prev, email: undefined }));
    }

    // Clear general error when user starts typing
    if (error) {
      setError('');
    }
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setPassword(value);

    // Clear field error when user starts typing
    if (fieldErrors.password) {
      setFieldErrors(prev => ({ ...prev, password: undefined }));
    }

    // Clear general error when user starts typing
    if (error) {
      setError('');
    }
  };

  const getErrorMessage = (err: unknown): string => {
    // Type guard to check if error has expected properties
    const isAxiosError = (error: unknown): error is {
      code?: string;
      message?: string;
      response?: {
        data?: {
          message?: string | string[];
          statusCode?: number;
          error?: string;
        };
      };
    } => {
      return typeof error === 'object' && error !== null;
    };

    if (!isAxiosError(err)) {
      return 'An unexpected error occurred. Please try again.';
    }

    // Handle network errors
    if (err.code === 'ERR_NETWORK' || err.message === 'Network Error') {
      return 'Unable to connect to the server. Please check your internet connection and try again.';
    }

    // Handle timeout errors
    if (err.code === 'ECONNABORTED') {
      return 'Request timed out. Please check your connection and try again.';
    }

    // Handle response errors
    if (err.response?.data) {
      const { message, statusCode } = err.response.data;

      // Handle validation errors (array of messages)
      if (Array.isArray(message)) {
        return message.join('. ');
      }

      // Handle specific status codes
      switch (statusCode) {
        case 400:
          if (typeof message === 'string') {
            return message;
          }
          return 'Invalid input. Please check your email and password format.';

        case 401:
          if (message && message.toLowerCase().includes('credential')) {
            return 'Invalid email or password. Please check your credentials and try again.';
          }
          if (message && message.toLowerCase().includes('account')) {
            return 'Account not found or inactive. Please contact support if you believe this is an error.';
          }
          return 'Authentication failed. Please verify your email and password.';

        case 403:
          return 'Your account has been suspended or you do not have permission to access this system.';

        case 429:
          return 'Too many login attempts. Please wait a few minutes before trying again.';

        case 500:
          return 'Server error occurred. Please try again later or contact support.';

        case 503:
          return 'Service temporarily unavailable. Please try again in a few minutes.';

        default:
          if (typeof message === 'string') {
            return message;
          }
          return `Login failed (Error ${statusCode}). Please try again.`;
      }
    }

    // Handle other error types
    if (err.message) {
      // Handle specific error messages
      if (err.message.toLowerCase().includes('fetch')) {
        return 'Unable to connect to the server. Please check your internet connection.';
      }
      return err.message;
    }

    return 'An unexpected error occurred. Please try again.';
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Clear previous messages
    setError('');
    setSuccessMessage('');

    // Validate form before submission
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setLoading(true);

    try {
      // Always treat this as customer login since we're on customer portal
      const result = await staffLogin(email.trim().toLowerCase(), password, rememberMe);

      console.log("Customer login result:", result);
      if (result && result.user) {
        // Check if account recovery is required
        if (result?.requiresRecovery) {
          console.log('Account recovery required, redirecting to recovery page');
          setLoadingMessage('Account recovery required. Redirecting...');
          setLoading(true);

          // Redirect to recovery page with user info
          const recoveryUrl = `/customer/auth/recover?email=${encodeURIComponent(result.user.email)}&name=${encodeURIComponent(result.user.first_name)}`;
          router.replace(recoveryUrl);
          return;
        }

        if (result?.requiresTwoFactor) {
          setRequires2FA(true);
          if (result?.user?.two_factor_enabled) {
            console.log('OTP verification required, redirecting to /customer/auth/verify-login');
            setLoadingMessage('OTP verification required. Redirecting to verify login...');
            setLoading(true);
            router.replace('/customer/auth/verify-login');
            return;
          } else {
            console.log('2FA not enabled, redirecting to /customer/auth/setup-2fa');
            setLoadingMessage('2FA setup required. Redirecting to setup 2FA...');
            setLoading(true);

            // Store user and token info for 2FA setup
            sessionStorage.setItem('2fa_setup_user', JSON.stringify(result.user));
            sessionStorage.setItem('2fa_setup_token', result.token || '');
            sessionStorage.setItem('remember_me', rememberMe.toString());

            console.log('Session storage:', sessionStorage);
            router.replace('/customer/auth/setup-2fa');
            return;
          }
        } else {
          console.log('Login successful, no 2FA required, redirecting to customer dashboard');
          setLoadingMessage('Login successful! Redirecting to dashboard...');
          setLoading(true);
          router.replace('/customer');
          return;
        }
      } else {
        setLoadingMessage(`Invalid user session details detected. Redirecting to login..`);
        router.replace('/customer/auth/login');
      }
    } catch (err: unknown) {
      console.error('Customer login error:', err);
      const errorMessage = getErrorMessage(err);
      setError(errorMessage);
      setLoading(false);
      // Return early - stay on login page when authentication fails
      return;
    }
  };

  // Show loading while client is initializing, checking authentication, or during login/redirect
  if (!isClient || loading) {
    console.log('Customer login page loading state:', { isClient, loading, isCustomerPortal });
    const dynamicMessages = [
      'Connecting to customer portal...',
      'Verifying credentials...',
      'Setting up your session...',
      'Almost ready...'
    ];

    return (
      <PageTransition
        isLoading={true}
        loadingMessage={loadingMessage || (!isClient ? 'Loading...' : 'Signing in...')}
        loadingSubmessage="Please wait while we process your request"
        dynamicMessages={loading ? dynamicMessages : undefined}
        showProgress={loading}
      >
        <div />
      </PageTransition>
    );
  }

  return (
    <AuthLayout
      title={isCustomerPortal ? 'Sign in to your account' : 'Customer Portal Login'}
      subtitle={isCustomerPortal ? (
        <>
          Or{' '}
          <Link href="/customer/auth/signup" className="font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300">
            create a new account
          </Link>
        </>
      ) : 'Access your customer dashboard'}
      isCustomerPortal={true}
    >
          {error && (
            <StatusMessage
              type="error"
              message={error}
              className="mb-4"
              dismissible={true}
              onDismiss={() => setError('')}
            />
          )}

          {successMessage && (
            <StatusMessage
              type="success"
              message={successMessage}
              className="mb-4"
              dismissible={true}
              onDismiss={() => setSuccessMessage('')}
            />
          )}

          <form className="space-y-6 animate-fadeIn animate-delay-200" onSubmit={handleSubmit}>
            <div className="animate-slideInFromBottom animate-delay-300">
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Email address
              </label>
              <div className="mt-1">
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={handleEmailChange}
                  className={`appearance-none block w-full px-4 py-3 border-2 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 sm:text-sm bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 field-focus-ring transition-smooth ${
                    fieldErrors.email
                      ? 'border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500 animate-shake'
                      : 'border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500'
                  }`}
                  placeholder="Enter your email address"
                />
              </div>
              {fieldErrors.email && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400 flex items-center animate-slideInFromTop">
                  <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  {fieldErrors.email}
                </p>
              )}
            </div>

            <div className="animate-slideInFromBottom animate-delay-500">
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Password
              </label>
              <div className="mt-1">
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="current-password"
                  required
                  value={password}
                  onChange={handlePasswordChange}
                  className={`appearance-none block w-full px-4 py-3 border-2 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 sm:text-sm bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 field-focus-ring transition-smooth ${
                    fieldErrors.password
                      ? 'border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500 animate-shake'
                      : 'border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500'
                  }`}
                  placeholder="Enter your password"
                />
              </div>
              {fieldErrors.password && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400 flex items-center animate-slideInFromTop">
                  <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  {fieldErrors.password}
                </p>
              )}
            </div>

            {/* Show Remember Me and Forgot Password only for Customer Portal */}
            {isCustomerPortal && (
              <div className="flex items-center justify-between animate-fadeIn animate-delay-500">
                <div className="flex items-center">
                  <input
                    id="remember-me"
                    name="remember-me"
                    type="checkbox"
                    checked={rememberMe}
                    onChange={(e) => setRememberMe(e.target.checked)}
                    className="h-5 w-5 text-red-600 focus:ring-2 focus:ring-red-500 border-2 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 transition-smooth"
                  />
                  <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-900 dark:text-gray-100">
                    Remember me
                  </label>
                </div>

                <div className="text-sm">
                  <Link href="/customer/auth/forgot-password" className="font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300 transition-colors hover:underline">
                    Forgot your password?
                  </Link>
                </div>
              </div>
            )}

            <div className="animate-slideInFromBottom animate-delay-500">
              <button
                type="submit"
                disabled={loading}
                className="w-full flex justify-center items-center py-3 px-4 border-2 border-transparent rounded-md shadow-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 button-hover-lift transition-smooth disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Signing in...
                  </>
                ) : (
                  <>
                    <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                    </svg>
                    Sign in
                  </>
                )}
              </button>
            </div>
          </form>
    </AuthLayout>
  );
}

export default function LoginPage() {
  console.log('LoginPage component rendering...');
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600"></div>
        <p className="mt-4 text-gray-600">Loading login page...</p>
      </div>
    }>
      <LoginForm />
    </Suspense>
  );
}
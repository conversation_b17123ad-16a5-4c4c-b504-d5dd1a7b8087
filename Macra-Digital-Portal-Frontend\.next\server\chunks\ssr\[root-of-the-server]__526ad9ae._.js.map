{"version": 3, "sources": [], "sections": [{"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/lib/auth.ts"], "sourcesContent": ["import axios from 'axios';\r\n\r\n// Authentication utility functions\r\n\r\nexport const getAuthToken = (): string | null => {\r\n  if (typeof window === 'undefined') {\r\n    return null; // Server-side rendering\r\n  }\r\n  \r\n  return localStorage.getItem('auth_token');\r\n};\r\n\r\nexport const setAuthToken = (token: string): void => {\r\n  if (typeof window !== 'undefined') {\r\n    localStorage.setItem('auth_token', token);\r\n  }\r\n};\r\n\r\nexport const removeAuthToken = (): void => {\r\n  if (typeof window !== 'undefined') {\r\n    localStorage.removeItem('auth_token');\r\n  }\r\n};\r\n\r\nexport const isAuthenticated = (): boolean => {\r\n  return !!getAuthToken();\r\n};\r\n\r\n\r\n\r\n// Create axios instance with auth\r\nexport const createAuthenticatedAxios = (API_BASE_URL: string = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001') => {\r\n  const token = getAuthToken();\r\n  const instance = axios.create({\r\n    baseURL: API_BASE_URL,\r\n    timeout: 10000, // 10 second timeout\r\n    headers: {\r\n      'Authorization': token ? `Bearer ${token}` : '',\r\n      'Content-Type': 'application/json',\r\n    },\r\n  });\r\n\r\n  // Add request interceptor for debugging\r\n  instance.interceptors.request.use(\r\n    (config) => {\r\n      console.log(`Making ${config.method?.toUpperCase()} request to: ${config.baseURL}${config.url}`);\r\n      return config;\r\n    },\r\n    (error) => {\r\n      console.error('Request interceptor error:', error);\r\n      return Promise.reject(error);\r\n    }\r\n  );\r\n\r\n  // Add response interceptor for better error handling\r\n  instance.interceptors.response.use(\r\n    (response) => {\r\n      return response;\r\n    },\r\n    (error) => {\r\n      console.error('API Error Details:', {\r\n        url: error.config?.url,\r\n        method: error.config?.method,\r\n        status: error.response?.status,\r\n        statusText: error.response?.statusText,\r\n        message: error.message,\r\n        code: error.code,\r\n        data: error.response?.data\r\n      });\r\n\r\n      // Handle authentication errors - auto logout on 401\r\n      if (error.response?.status === 401) {\r\n        console.warn('Authentication failed - token invalid or expired. Logging out...');\r\n        removeAuthToken();\r\n        // Redirect to login page\r\n        if (typeof window !== 'undefined') {\r\n          window.location.href = '/auth/login';\r\n        }\r\n      }\r\n\r\n      // Handle network errors\r\n      if (error.code === 'ERR_NETWORK' || error.message === 'Network Error') {\r\n        console.error('Network error - backend may not be accessible');\r\n      }\r\n\r\n      return Promise.reject(error);\r\n    }\r\n  );\r\n\r\n  return instance;\r\n};"], "names": [], "mappings": ";;;;;;;AAAA;;AAIO,MAAM,eAAe;IAC1B,wCAAmC;QACjC,OAAO,MAAM,wBAAwB;IACvC;;AAGF;AAEO,MAAM,eAAe,CAAC;IAC3B,uCAAmC;;IAEnC;AACF;AAEO,MAAM,kBAAkB;IAC7B,uCAAmC;;IAEnC;AACF;AAEO,MAAM,kBAAkB;IAC7B,OAAO,CAAC,CAAC;AACX;AAKO,MAAM,2BAA2B,CAAC,eAAuB,6DAAmC,uBAAuB;IACxH,MAAM,QAAQ;IACd,MAAM,WAAW,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;QAC5B,SAAS;QACT,SAAS;QACT,SAAS;YACP,iBAAiB,QAAQ,CAAC,OAAO,EAAE,OAAO,GAAG;YAC7C,gBAAgB;QAClB;IACF;IAEA,wCAAwC;IACxC,SAAS,YAAY,CAAC,OAAO,CAAC,GAAG,CAC/B,CAAC;QACC,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE,cAAc,aAAa,EAAE,OAAO,OAAO,GAAG,OAAO,GAAG,EAAE;QAC/F,OAAO;IACT,GACA,CAAC;QACC,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,QAAQ,MAAM,CAAC;IACxB;IAGF,qDAAqD;IACrD,SAAS,YAAY,CAAC,QAAQ,CAAC,GAAG,CAChC,CAAC;QACC,OAAO;IACT,GACA,CAAC;QACC,QAAQ,KAAK,CAAC,sBAAsB;YAClC,KAAK,MAAM,MAAM,EAAE;YACnB,QAAQ,MAAM,MAAM,EAAE;YACtB,QAAQ,MAAM,QAAQ,EAAE;YACxB,YAAY,MAAM,QAAQ,EAAE;YAC5B,SAAS,MAAM,OAAO;YACtB,MAAM,MAAM,IAAI;YAChB,MAAM,MAAM,QAAQ,EAAE;QACxB;QAEA,oDAAoD;QACpD,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;YAClC,QAAQ,IAAI,CAAC;YACb;YACA,yBAAyB;YACzB,uCAAmC;;YAEnC;QACF;QAEA,wBAAwB;QACxB,IAAI,MAAM,IAAI,KAAK,iBAAiB,MAAM,OAAO,KAAK,iBAAiB;YACrE,QAAQ,KAAK,CAAC;QAChB;QAEA,OAAO,QAAQ,MAAM,CAAC;IACxB;IAGF,OAAO;AACT", "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/lib/authUtils.ts"], "sourcesContent": ["import Cookies from 'js-cookie';\r\nimport { getAuthToken, removeAuthToken } from './auth';\r\n\r\n/**\r\n * Utility functions for authentication management\r\n */\r\n\r\n/**\r\n * Check if a JWT token is expired\r\n * @param token - JWT token to check\r\n * @returns true if token is expired, false otherwise\r\n */\r\nexport const isTokenExpired = (token: string): boolean => {\r\n  if (!token) return true;\r\n\r\n  try {\r\n    // Decode JWT payload (without verification - just for expiry check)\r\n    const payload = JSON.parse(atob(token.split('.')[1]));\r\n    const currentTime = Math.floor(Date.now() / 1000);\r\n    \r\n    // Check if token has expired\r\n    return payload.exp < currentTime;\r\n  } catch (error) {\r\n    console.error('Error decoding token:', error);\r\n    return true; // Treat invalid tokens as expired\r\n  }\r\n};\r\n\r\n/**\r\n * Validate current authentication state\r\n * @returns true if user is properly authenticated, false otherwise\r\n */\r\nexport const validateAuthState = (): boolean => {\r\n  const token = getAuthToken();\r\n  const userCookie = Cookies.get('auth_user');\r\n\r\n  // Check if token exists and is not expired\r\n  if (!token || isTokenExpired(token)) {\r\n    console.warn('Token is missing or expired');\r\n    return false;\r\n  }\r\n\r\n  // Check if user data exists\r\n  if (!userCookie) {\r\n    console.warn('User data is missing');\r\n    return false;\r\n  }\r\n\r\n  try {\r\n    JSON.parse(userCookie); // Validate user data format\r\n    return true;\r\n  } catch (error) {\r\n    console.error('Invalid user data format:', error);\r\n    return false;\r\n  }\r\n};\r\n\r\n/**\r\n * Clear all authentication data and redirect to login\r\n */\r\nexport const forceLogout = (): void => {\r\n  console.warn('Forcing logout due to invalid authentication state');\r\n  \r\n  // Clear all auth data\r\n  removeAuthToken();\r\n  Cookies.remove('auth_token');\r\n  Cookies.remove('auth_user');\r\n  \r\n  // Clear localStorage and sessionStorage\r\n  if (typeof window !== 'undefined') {\r\n    localStorage.removeItem('auth_token');\r\n    localStorage.removeItem('user_preferences');\r\n    sessionStorage.clear();\r\n    \r\n    // Redirect to appropriate login based on current portal\r\n    const isCustomerPortal = window.location.pathname.startsWith('/customer') || \r\n                            window.location.hostname.includes('customer');\r\n    \r\n    window.location.href = isCustomerPortal ? '/customer/auth/login' : '/auth/login';\r\n  }\r\n};\r\n\r\n/**\r\n * Periodically check token validity and auto-logout if expired\r\n * @param intervalMs - Check interval in milliseconds (default: 60 seconds)\r\n */\r\nexport const startTokenValidationTimer = (intervalMs: number = 60000): NodeJS.Timeout => {\r\n  return setInterval(() => {\r\n    if (!validateAuthState()) {\r\n      forceLogout();\r\n    }\r\n  }, intervalMs);\r\n};\r\n\r\n/**\r\n * Periodically check token validity and auto-logout if expired\r\n * @param intervalMs - Check interval in milliseconds (default: 60 seconds)\r\n */\r\nexport const processApiResponse = (response: any): any =>  {\r\n\r\n    // Check if it's a standard datatable success response format\r\n  if (response?.data?.meta !== undefined && response.data.data) {\r\n    return response.data;\r\n  }\r\n\r\n  // Check if it's a standard success response format\r\n  if (response?.data?.data) {\r\n    return response.data.data;\r\n  }\r\n\r\n  // Check if it's direct data format\r\n  else if (response.data) {\r\n    return response.data;\r\n  }\r\n\r\n  return response.data;\r\n};\r\n\r\n/**\r\n * Check token expiry and warn user before it expires\r\n * @param warningMinutes - Minutes before expiry to show warning (default: 5)\r\n */\r\nexport const checkTokenExpiry = (warningMinutes: number = 5): void => {\r\n  const token = getAuthToken();\r\n  if (!token) return;\r\n\r\n  try {\r\n    const payload = JSON.parse(atob(token.split('.')[1]));\r\n    const expiryTime = payload.exp * 1000; // Convert to milliseconds\r\n    const currentTime = Date.now();\r\n    const timeUntilExpiry = expiryTime - currentTime;\r\n    const warningTime = warningMinutes * 60 * 1000; // Convert to milliseconds\r\n\r\n    if (timeUntilExpiry <= warningTime && timeUntilExpiry > 0) {\r\n      console.warn(`Token will expire in ${Math.floor(timeUntilExpiry / 60000)} minutes`);\r\n      // You can add a toast notification here if needed\r\n    }\r\n  } catch (error) {\r\n    console.error('Error checking token expiry:', error);\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAWO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,CAAC,OAAO,OAAO;IAEnB,IAAI;QACF,oEAAoE;QACpE,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;QACnD,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;QAE5C,6BAA6B;QAC7B,OAAO,QAAQ,GAAG,GAAG;IACvB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,MAAM,kCAAkC;IACjD;AACF;AAMO,MAAM,oBAAoB;IAC/B,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;IACzB,MAAM,aAAa,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;IAE/B,2CAA2C;IAC3C,IAAI,CAAC,SAAS,eAAe,QAAQ;QACnC,QAAQ,IAAI,CAAC;QACb,OAAO;IACT;IAEA,4BAA4B;IAC5B,IAAI,CAAC,YAAY;QACf,QAAQ,IAAI,CAAC;QACb,OAAO;IACT;IAEA,IAAI;QACF,KAAK,KAAK,CAAC,aAAa,4BAA4B;QACpD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;IACT;AACF;AAKO,MAAM,cAAc;IACzB,QAAQ,IAAI,CAAC;IAEb,sBAAsB;IACtB,CAAA,GAAA,kHAAA,CAAA,kBAAe,AAAD;IACd,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;IACf,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;IAEf,wCAAwC;IACxC,uCAAmC;;IAUnC;AACF;AAMO,MAAM,4BAA4B,CAAC,aAAqB,KAAK;IAClE,OAAO,YAAY;QACjB,IAAI,CAAC,qBAAqB;YACxB;QACF;IACF,GAAG;AACL;AAMO,MAAM,qBAAqB,CAAC;IAE/B,6DAA6D;IAC/D,IAAI,UAAU,MAAM,SAAS,aAAa,SAAS,IAAI,CAAC,IAAI,EAAE;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,mDAAmD;IACnD,IAAI,UAAU,MAAM,MAAM;QACxB,OAAO,SAAS,IAAI,CAAC,IAAI;IAC3B,OAGK,IAAI,SAAS,IAAI,EAAE;QACtB,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO,SAAS,IAAI;AACtB;AAMO,MAAM,mBAAmB,CAAC,iBAAyB,CAAC;IACzD,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;IACzB,IAAI,CAAC,OAAO;IAEZ,IAAI;QACF,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;QACnD,MAAM,aAAa,QAAQ,GAAG,GAAG,MAAM,0BAA0B;QACjE,MAAM,cAAc,KAAK,GAAG;QAC5B,MAAM,kBAAkB,aAAa;QACrC,MAAM,cAAc,iBAAiB,KAAK,MAAM,0BAA0B;QAE1E,IAAI,mBAAmB,eAAe,kBAAkB,GAAG;YACzD,QAAQ,IAAI,CAAC,CAAC,qBAAqB,EAAE,KAAK,KAAK,CAAC,kBAAkB,OAAO,QAAQ,CAAC;QAClF,kDAAkD;QACpD;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;IAChD;AACF", "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/lib/apiClient.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosError } from 'axios';\r\nimport { getAuthToken } from './auth';\r\nimport { forceLogout } from './authUtils';\r\nimport { apiRateLimiter, withExponentialBackoff } from '../utils/rateLimiter';\r\n\r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';\r\n\r\n/**\r\n * Create an authenticated axios instance with proper error handling\r\n * This instance automatically handles 401 errors and performs auto-logout\r\n */\r\nexport const createApiClient = (baseURL: string = API_BASE_URL): AxiosInstance => {\r\n  const instance = axios.create({\r\n    baseURL,\r\n    timeout: 120000, // 120 second timeout for better reliability\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n    },\r\n  });\r\n\r\n  // Request interceptor to add auth token and rate limiting\r\n  instance.interceptors.request.use(\r\n    async (config) => {\r\n      // // Apply rate limiting based on endpoint\r\n      // const endpoint = config.url || '';\r\n      // const rateLimitKey = endpoint.includes('license-types') || endpoint.includes('license-categories')\r\n      //   ? 'license-data'\r\n      //   : 'general';\r\n\r\n      // // Check rate limit before making request\r\n      // await apiRateLimiter.checkRateLimit(rateLimitKey);\r\n\r\n      const token = getAuthToken();\r\n      if (token) {\r\n        config.headers.Authorization = `Bearer ${token}`;\r\n      }\r\n\r\n      // Log request for debugging (only in development)\r\n      if (process.env.NODE_ENV === 'development') {\r\n        console.log(`[Staff API] Making ${config.method?.toUpperCase()} request to: ${config.baseURL}${config.url}`);\r\n      }\r\n      return config;\r\n    },\r\n    (error) => {\r\n      console.error('Request interceptor error:', error);\r\n      return Promise.reject(error);\r\n    }\r\n  );\r\n\r\n  // Response interceptor for error handling and auto-logout\r\n  instance.interceptors.response.use(\r\n    (response) => {\r\n      return response;\r\n    },\r\n    async (error: AxiosError) => {\r\n      const originalRequest = error.config as AxiosError['config'] & {\r\n        _retry?: boolean;\r\n        _retryCount?: number;\r\n      };\r\n\r\n      // Handle 429 Rate Limiting (same as customer API client)\r\n      if (error.response?.status === 429) {\r\n        // Ensure originalRequest exists and has the required properties\r\n        if (originalRequest && !originalRequest._retry) {\r\n          originalRequest._retry = true;\r\n\r\n          // Get retry delay from headers or use exponential backoff\r\n          const retryAfter = error.response.headers['retry-after'];\r\n          const delay = retryAfter ? parseInt(retryAfter) * 1000 : Math.min(1000 * Math.pow(2, originalRequest._retryCount || 0), 10000);\r\n\r\n          originalRequest._retryCount = (originalRequest._retryCount || 0) + 1;\r\n\r\n          // Don't retry more than 3 times\r\n          if (originalRequest._retryCount <= 10) {\r\n            if (process.env.NODE_ENV === 'development') {\r\n              console.warn(`[Staff API] Rate limited. Retrying after ${delay}ms (attempt ${originalRequest._retryCount})`);\r\n            }\r\n\r\n            await new Promise(resolve => setTimeout(resolve, delay));\r\n            return instance(originalRequest);\r\n          } else {\r\n            // Exhausted retries\r\n            if (process.env.NODE_ENV === 'development') {\r\n              console.error('[Staff API] Rate limiting exhausted retries:', {\r\n                url: error.config?.url,\r\n                method: error.config?.method,\r\n                retryCount: originalRequest._retryCount\r\n              });\r\n            }\r\n          }\r\n        } else {\r\n          // Already retrying or no original request\r\n          if (process.env.NODE_ENV === 'development') {\r\n            console.warn('[Staff API] Rate limited but already retrying or no original request');\r\n          }\r\n        }\r\n      }\r\n\r\n      // Only log detailed errors in development (for non-rate-limiting errors or exhausted retries)\r\n      if (process.env.NODE_ENV === 'development' && error.response?.status !== 429) {\r\n        console.error('API Error Details:', {\r\n          url: error.config?.url,\r\n          method: error.config?.method,\r\n          status: error.response?.status,\r\n          statusText: error.response?.statusText,\r\n          message: error.message,\r\n          code: error.code,\r\n          data: error.response?.data\r\n        });\r\n      }\r\n\r\n      // Handle authentication errors - auto logout on 401\r\n      if (error.response?.status === 401) {\r\n        console.warn('Authentication failed - token invalid or expired. Forcing logout...');\r\n        forceLogout();\r\n        return Promise.reject(new Error('Authentication failed. Please log in again.'));\r\n      }\r\n\r\n      // Handle authorization errors\r\n      if (error.response?.status === 403) {\r\n        console.warn('Access denied - insufficient permissions');\r\n        // You can add a toast notification here\r\n      }\r\n\r\n      // Handle validation/conflict errors (duplicate registration, etc.)\r\n      if (error.response?.status === 409 || error.response?.status === 422) {\r\n        const errorData = error.response?.data as { message?: string };\r\n        console.warn('Validation/Conflict error:', errorData?.message || error.message);\r\n        // Let the calling service handle the specific error message\r\n      }\r\n\r\n      // Handle network errors\r\n      if (error.code === 'ERR_NETWORK' || error.message === 'Network Error') {\r\n        console.error('Network error - backend may not be accessible');\r\n        // You can add a toast notification here\r\n      }\r\n\r\n      // Handle timeout errors\r\n      if (error.code === 'ECONNABORTED') {\r\n        console.error('Request timeout - server took too long to respond');\r\n        // You can add a toast notification here\r\n      }\r\n\r\n      return Promise.reject(error);\r\n    }\r\n  );\r\n\r\n  return instance;\r\n};\r\n\r\n/**\r\n * Default API client instance\r\n */\r\nexport const apiClient = createApiClient();\r\n\r\n/**\r\n * Auth-specific API client\r\n */\r\nexport const authApiClient = createApiClient(`${API_BASE_URL}/auth`);\r\n\r\n/**\r\n * Users API client\r\n */\r\nexport const usersApiClient = createApiClient(`${API_BASE_URL}/users`);\r\n\r\n/**\r\n * Roles API client\r\n */\r\nexport const rolesApiClient = createApiClient(`${API_BASE_URL}/roles`);\r\n\r\n/**\r\n * Audit Trail API client\r\n */\r\nexport const auditApiClient = createApiClient(`${API_BASE_URL}/audit-trail`);\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;;;;AAGA,MAAM,eAAe,6DAAmC;AAMjD,MAAM,kBAAkB,CAAC,UAAkB,YAAY;IAC5D,MAAM,WAAW,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;QAC5B;QACA,SAAS;QACT,SAAS;YACP,gBAAgB;QAClB;IACF;IAEA,0DAA0D;IAC1D,SAAS,YAAY,CAAC,OAAO,CAAC,GAAG,CAC/B,OAAO;QACL,2CAA2C;QAC3C,qCAAqC;QACrC,qGAAqG;QACrG,qBAAqB;QACrB,iBAAiB;QAEjB,4CAA4C;QAC5C,qDAAqD;QAErD,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,IAAI,OAAO;YACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;QAClD;QAEA,kDAAkD;QAClD,wCAA4C;YAC1C,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,OAAO,MAAM,EAAE,cAAc,aAAa,EAAE,OAAO,OAAO,GAAG,OAAO,GAAG,EAAE;QAC7G;QACA,OAAO;IACT,GACA,CAAC;QACC,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,QAAQ,MAAM,CAAC;IACxB;IAGF,0DAA0D;IAC1D,SAAS,YAAY,CAAC,QAAQ,CAAC,GAAG,CAChC,CAAC;QACC,OAAO;IACT,GACA,OAAO;QACL,MAAM,kBAAkB,MAAM,MAAM;QAKpC,yDAAyD;QACzD,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;YAClC,gEAAgE;YAChE,IAAI,mBAAmB,CAAC,gBAAgB,MAAM,EAAE;gBAC9C,gBAAgB,MAAM,GAAG;gBAEzB,0DAA0D;gBAC1D,MAAM,aAAa,MAAM,QAAQ,CAAC,OAAO,CAAC,cAAc;gBACxD,MAAM,QAAQ,aAAa,SAAS,cAAc,OAAO,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,CAAC,GAAG,gBAAgB,WAAW,IAAI,IAAI;gBAExH,gBAAgB,WAAW,GAAG,CAAC,gBAAgB,WAAW,IAAI,CAAC,IAAI;gBAEnE,gCAAgC;gBAChC,IAAI,gBAAgB,WAAW,IAAI,IAAI;oBACrC,wCAA4C;wBAC1C,QAAQ,IAAI,CAAC,CAAC,yCAAyC,EAAE,MAAM,YAAY,EAAE,gBAAgB,WAAW,CAAC,CAAC,CAAC;oBAC7G;oBAEA,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACjD,OAAO,SAAS;gBAClB,OAAO;oBACL,oBAAoB;oBACpB,wCAA4C;wBAC1C,QAAQ,KAAK,CAAC,gDAAgD;4BAC5D,KAAK,MAAM,MAAM,EAAE;4BACnB,QAAQ,MAAM,MAAM,EAAE;4BACtB,YAAY,gBAAgB,WAAW;wBACzC;oBACF;gBACF;YACF,OAAO;gBACL,0CAA0C;gBAC1C,wCAA4C;oBAC1C,QAAQ,IAAI,CAAC;gBACf;YACF;QACF;QAEA,8FAA8F;QAC9F,IAAI,oDAAyB,iBAAiB,MAAM,QAAQ,EAAE,WAAW,KAAK;YAC5E,QAAQ,KAAK,CAAC,sBAAsB;gBAClC,KAAK,MAAM,MAAM,EAAE;gBACnB,QAAQ,MAAM,MAAM,EAAE;gBACtB,QAAQ,MAAM,QAAQ,EAAE;gBACxB,YAAY,MAAM,QAAQ,EAAE;gBAC5B,SAAS,MAAM,OAAO;gBACtB,MAAM,MAAM,IAAI;gBAChB,MAAM,MAAM,QAAQ,EAAE;YACxB;QACF;QAEA,oDAAoD;QACpD,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;YAClC,QAAQ,IAAI,CAAC;YACb,CAAA,GAAA,uHAAA,CAAA,cAAW,AAAD;YACV,OAAO,QAAQ,MAAM,CAAC,IAAI,MAAM;QAClC;QAEA,8BAA8B;QAC9B,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;YAClC,QAAQ,IAAI,CAAC;QACb,wCAAwC;QAC1C;QAEA,mEAAmE;QACnE,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,MAAM,QAAQ,EAAE,WAAW,KAAK;YACpE,MAAM,YAAY,MAAM,QAAQ,EAAE;YAClC,QAAQ,IAAI,CAAC,8BAA8B,WAAW,WAAW,MAAM,OAAO;QAC9E,4DAA4D;QAC9D;QAEA,wBAAwB;QACxB,IAAI,MAAM,IAAI,KAAK,iBAAiB,MAAM,OAAO,KAAK,iBAAiB;YACrE,QAAQ,KAAK,CAAC;QACd,wCAAwC;QAC1C;QAEA,wBAAwB;QACxB,IAAI,MAAM,IAAI,KAAK,gBAAgB;YACjC,QAAQ,KAAK,CAAC;QACd,wCAAwC;QAC1C;QAEA,OAAO,QAAQ,MAAM,CAAC;IACxB;IAGF,OAAO;AACT;AAKO,MAAM,YAAY;AAKlB,MAAM,gBAAgB,gBAAgB,GAAG,aAAa,KAAK,CAAC;AAK5D,MAAM,iBAAiB,gBAAgB,GAAG,aAAa,MAAM,CAAC;AAK9D,MAAM,iBAAiB,gBAAgB,GAAG,aAAa,MAAM,CAAC;AAK9D,MAAM,iBAAiB,gBAAgB,GAAG,aAAa,YAAY,CAAC", "debugId": null}}, {"offset": {"line": 438, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/auth.service.ts"], "sourcesContent": ["import { AxiosInstance } from 'axios';\r\nimport { authApiClient } from '../lib/apiClient';\r\nimport { processApiResponse } from '@/lib/authUtils';\r\n\r\nexport interface LoginData {\r\n  email: string;\r\n  password: string;\r\n}\r\n\r\nexport interface RegisterData {\r\n  email: string;\r\n  password: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  middle_name?: string;\r\n  phone: string;\r\n  organization?: string;\r\n}\r\n\r\nexport interface User {\r\n  user_id: string;\r\n  email: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  middle_name?: string;\r\n  phone?: string;\r\n  status?: string;\r\n  profile_image?: string;\r\n  roles: string[]; // Array of role names from the API\r\n  isAdmin?: boolean; // Computed property for backward compatibility\r\n  two_factor_enabled?: boolean;\r\n}\r\n\r\nexport interface AuthResponse {\r\n  access_token: string;\r\n  user: User;\r\n  requiresTwoFactor?: boolean;\r\n}\r\n\r\nexport interface ForgotPasswordData {\r\n  email: string;\r\n}\r\n\r\nexport interface ResetPasswordData {\r\n  user_id: string;\r\n  code: string;\r\n  new_password: string;\r\n  unique: string;\r\n}\r\n\r\nexport interface TwoFactorData {\r\n  user_id: string;\r\n  code: string;\r\n  unique: string;\r\n}\r\n\r\nexport interface SetUpTwoFactorData {\r\n  access_token: string;\r\n  user_id: string;\r\n}\r\n\r\ntype TwoFactorResponse = {\r\n  access_token: string;\r\n  user: {\r\n    user_id: string;\r\n    email: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    two_factor_enabled: boolean;\r\n    roles: string[];\r\n  };\r\n  message: string;\r\n};\r\n\r\ntype SetupTwoFactorAuthResponse = {\r\n  otp_auth_url: string;\r\n  qr_code_data_url: string;\r\n  secret: string;\r\n  message: string;\r\n}\r\n\r\n\r\nclass AuthService {\r\n  private api: AxiosInstance;\r\n\r\n  constructor() {\r\n    // Use the centralized auth API client with proper error handling\r\n    this.api = authApiClient;\r\n  }\r\n\r\n  async login(data: LoginData): Promise<AuthResponse> {\r\n    const response = await this.api.post('/login', data);\r\n\r\n    // The backend returns data directly, not nested in a data property\r\n    const authData = processApiResponse(response);\r\n\r\n    // Check if the auth data is empty (which indicates an error)\r\n    if (!authData || Object.keys(authData).length === 0) {\r\n      throw new Error('Authentication failed - invalid credentials');\r\n    }\r\n\r\n    // Validate that we have the required fields\r\n    if (!authData.access_token || !authData.user) {\r\n      throw new Error('Authentication failed - incomplete response');\r\n    }\r\n\r\n    return authData;\r\n  }\r\n\r\n  async register(data: RegisterData): Promise<AuthResponse> {\r\n    const response = await this.api.post('/register', data);\r\n\r\n    // The backend returns data directly, not nested in a data property\r\n    return response.data;\r\n  }\r\n\r\n  async forgotPassword(data: ForgotPasswordData): Promise<{ message: string }> {\r\n    const response = await this.api.post('/forgot-password', data);\r\n    return response.data;\r\n  }\r\n\r\n  async resetPassword(data: ResetPasswordData): Promise<{ message: string }> {\r\n    try {\r\n      console.log('🔄 Calling reset password API:', { ...data, new_password: '***' });\r\n      const response = await this.api.post('/reset-password', data);\r\n      console.log('✅ Reset password API response:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('❌ Reset password API error:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async verify2FA(data: TwoFactorData): Promise<TwoFactorResponse> {\r\n    try {\r\n      console.log('🔄 Calling verify 2FA API:', data);\r\n      const response = await this.api.post('/verify-2fa', data);\r\n      console.log('✅ Verify 2FA API response:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('❌ Verify 2FA API error:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async verifyEmail(data: TwoFactorData): Promise<{ message: string }> {\r\n    const response = await this.api.post('/verify-email', data);\r\n    return response.data;\r\n  }\r\n\r\n  async setupTwoFactorAuth(data: SetUpTwoFactorData): Promise<SetupTwoFactorAuthResponse> {\r\n    const response = await this.api.post('/setup-2fa', data);\r\n    return response.data;\r\n  }\r\n\r\n\r\n  async verifyTwoFactorCode(data: TwoFactorData): Promise<{ message: string }> {\r\n    const response = await this.api.post('/verify-2fa', data);\r\n    return response.data;\r\n  }\r\n\r\n  async generateTwoFactorCode(userId: string, action: string): Promise<{ message: string }> {\r\n    const response = await this.api.post('/generate-2fa', { user_id: userId, action });\r\n    return response.data;\r\n  }\r\n\r\n  async refreshToken(): Promise<AuthResponse> {\r\n    const response = await this.api.post('/refresh');\r\n    return response.data;\r\n  }\r\n\r\n  setAuthToken(token: string): void {\r\n    if (typeof window !== 'undefined') {\r\n      localStorage.setItem('auth_token', token);\r\n    }\r\n  }\r\n\r\n  getAuthToken(): string | null {\r\n    if (typeof window !== 'undefined') {\r\n      return localStorage.getItem('auth_token');\r\n    }\r\n    return null;\r\n  }\r\n\r\n  clearAuthToken(): void {\r\n    if (typeof window !== 'undefined') {\r\n      localStorage.removeItem('auth_token');\r\n    }\r\n  }\r\n}\r\n\r\nexport const authService = new AuthService();\r\n"], "names": [], "mappings": ";;;AACA;AACA;;;AAgFA,MAAM;IACI,IAAmB;IAE3B,aAAc;QACZ,iEAAiE;QACjE,IAAI,CAAC,GAAG,GAAG,uHAAA,CAAA,gBAAa;IAC1B;IAEA,MAAM,MAAM,IAAe,EAAyB;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU;QAE/C,mEAAmE;QACnE,MAAM,WAAW,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAEpC,6DAA6D;QAC7D,IAAI,CAAC,YAAY,OAAO,IAAI,CAAC,UAAU,MAAM,KAAK,GAAG;YACnD,MAAM,IAAI,MAAM;QAClB;QAEA,4CAA4C;QAC5C,IAAI,CAAC,SAAS,YAAY,IAAI,CAAC,SAAS,IAAI,EAAE;YAC5C,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT;IAEA,MAAM,SAAS,IAAkB,EAAyB;QACxD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa;QAElD,mEAAmE;QACnE,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,eAAe,IAAwB,EAAgC;QAC3E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,oBAAoB;QACzD,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,cAAc,IAAuB,EAAgC;QACzE,IAAI;YACF,QAAQ,GAAG,CAAC,kCAAkC;gBAAE,GAAG,IAAI;gBAAE,cAAc;YAAM;YAC7E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,mBAAmB;YACxD,QAAQ,GAAG,CAAC,kCAAkC,SAAS,IAAI;YAC3D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR;IACF;IAEA,MAAM,UAAU,IAAmB,EAA8B;QAC/D,IAAI;YACF,QAAQ,GAAG,CAAC,8BAA8B;YAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe;YACpD,QAAQ,GAAG,CAAC,8BAA8B,SAAS,IAAI;YACvD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF;IAEA,MAAM,YAAY,IAAmB,EAAgC;QACnE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB;QACtD,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,mBAAmB,IAAwB,EAAuC;QACtF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc;QACnD,OAAO,SAAS,IAAI;IACtB;IAGA,MAAM,oBAAoB,IAAmB,EAAgC;QAC3E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,sBAAsB,MAAc,EAAE,MAAc,EAAgC;QACxF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB;YAAE,SAAS;YAAQ;QAAO;QAChF,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,eAAsC;QAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa,KAAa,EAAQ;QAChC,uCAAmC;;QAEnC;IACF;IAEA,eAA8B;QAC5B,uCAAmC;;QAEnC;QACA,OAAO;IACT;IAEA,iBAAuB;QACrB,uCAAmC;;QAEnC;IACF;AACF;AAEO,MAAM,cAAc,IAAI", "debugId": null}}, {"offset": {"line": 546, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\r\nimport { authService, AuthResponse } from '../services/auth.service';\r\nimport { User } from '../services/auth.service';\r\nimport Cookies from 'js-cookie';\r\nimport { startTokenValidationTimer, isTokenExpired } from '../lib/authUtils';\r\n\r\ninterface AuthContextType {\r\n  user: User | null;\r\n  token: string | null;\r\n  login: (email: string, password: string, rememberMe?: boolean) => Promise<{ requiresTwoFactor?: boolean; requiresRecovery?: boolean; userId?: string; user?: User; token?: string } | void>;\r\n  completeTwoFactorLogin: (token: string, userData: User, rememberMe?: boolean) => Promise<void>;\r\n  register: (userData: RegisterData) => Promise<void>;\r\n  logout: () => void;\r\n  updateUser: (user: User) => void;\r\n  loading: boolean;\r\n  isAuthenticated: boolean;\r\n}\r\n\r\ninterface RegisterData {\r\n  email: string;\r\n  password: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  middle_name?: string;\r\n  phone: string;\r\n  organization?: string;\r\n}\r\n\r\ninterface UpdateUserData {\r\n  user_id: string;\r\n  email: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  middle_name?: string;\r\n  phone?: string;\r\n  status?: string;\r\n  profile_image?: string;\r\n  roles: (string | { name?: string; role_name?: string })[];\r\n  isAdmin?: boolean;\r\n}\r\n\r\n\r\n\r\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\r\n\r\nexport const useAuth = () => {\r\n  const context = useContext(AuthContext);\r\n  if (context === undefined) {\r\n    throw new Error('useAuth must be used within an AuthProvider');\r\n  }\r\n  return context;\r\n};\r\n\r\ninterface AuthProviderProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\r\n  const [user, setUser] = useState<User | null>(null);\r\n  const [token, setToken] = useState<string | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [mounted, setMounted] = useState(false);\r\n  // Set mounted to true after hydration\r\n  useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  // Start token validation timer when mounted and authenticated\r\n  useEffect(() => {\r\n    if (!mounted || !user || !token) return;\r\n\r\n    // Start periodic token validation (check every 5 minutes instead of 1 minute)\r\n    const validationTimer = startTokenValidationTimer(300000);\r\n\r\n    return () => {\r\n      clearInterval(validationTimer);\r\n    };\r\n  }, [mounted, user, token]);\r\n\r\n  useEffect(() => {\r\n    if (!mounted) return;\r\n\r\n\r\n\r\n    // Check for existing token on mount with validation\r\n    const initAuth = () => {\r\n      \r\n      // Get saved data from cookies\r\n      const savedToken = Cookies.get('auth_token');\r\n      const savedUser = Cookies.get('auth_user');\r\n\r\n      \r\n\r\n      if (savedToken && savedUser) {\r\n        try {\r\n          const parsedUser = JSON.parse(savedUser);\r\n          // Ensure isAdmin property is set for backward compatibility\r\n          const userWithAdmin = {\r\n            ...parsedUser,\r\n            isAdmin: parsedUser.roles?.includes('administrator') || parsedUser.isAdmin || false,\r\n            isCustomer: parsedUser.roles?.includes('customer')\r\n          };\r\n\r\n          // Validate token is not expired\r\n          if (!isTokenExpired(savedToken)) {\r\n            setToken(savedToken);\r\n            setUser(userWithAdmin);\r\n            authService.setAuthToken(savedToken);\r\n          } else {\r\n            Cookies.remove('auth_token');\r\n            Cookies.remove('auth_user');\r\n            authService.clearAuthToken();\r\n          }\r\n        } catch (error) {\r\n          console.error('AuthContext: Failed to parse saved user data:', error);\r\n          Cookies.remove('auth_token');\r\n          Cookies.remove('auth_user');\r\n          authService.clearAuthToken();\r\n        }\r\n      } else {\r\n        console.log('AuthContext: No saved authentication data found');\r\n      }\r\n\r\n      setLoading(false);\r\n    };\r\n\r\n    // Execute immediately instead of waiting for next render cycle\r\n    initAuth();\r\n  }, [mounted]);\r\n\r\n  const login = async (email: string, password: string, rememberMe: boolean = false): Promise<{requiresTwoFactor?:boolean, requiresRecovery?: boolean, userId?: string, user?: User, token?: string } | void> => {\r\n    try {\r\n      const response: AuthResponse = await authService.login({ email, password });\r\n\r\n      console.log(\"=====================================LOGIN RESPONSE======================================\")\r\n      console.log('Access Token:', response.access_token ? 'Present' : 'Missing');\r\n      console.log('User Data:', response.user);\r\n      console.log(\"=================================================================================\");      \r\n\r\n      // Check for account recovery requirement first\r\n      if (response.requiresRecovery) {\r\n        console.log('AuthContext: Account recovery required');\r\n        return {\r\n          requiresRecovery: true,\r\n          requiresTwoFactor: response.requiresTwoFactor || false,\r\n          userId: response.user.user_id,\r\n          user: response.user,\r\n          token: response.access_token || ''\r\n        };\r\n      }\r\n\r\n      // Validate response structure for normal login\r\n      if (!response || !response.user || !response.access_token) {\r\n        throw new Error('Invalid response from authentication service');\r\n      }\r\n      \r\n      // Ensure roles is an array\r\n      const roles = Array.isArray(response.user.roles) ? response.user.roles : [];\r\n\r\n      // Add computed isAdmin property for backward compatibility\r\n      const userWithAdmin = {\r\n        ...response.user,\r\n        roles,\r\n        isAdmin: roles.includes('administrator'),\r\n        isCustomer: roles.includes('customer')\r\n      };\r\n\r\n      // Check for 2FA requirement first, before setting user/token in context\r\n      if (response.user) {\r\n        // Use backend's requiresTwoFactor decision if available, otherwise fall back to old logic\r\n        const backendRequires2FA = response.requiresTwoFactor !== undefined ? response.requiresTwoFactor : response.user.two_factor_enabled;\r\n\r\n        if (backendRequires2FA) {\r\n          if (response.user.two_factor_enabled) {\r\n            // User has 2FA enabled and backend requires verification\r\n            console.log('AuthContext: 2FA verification required by backend');\r\n            return {\r\n              requiresTwoFactor: true,\r\n              userId: response.user.user_id,\r\n              user: userWithAdmin,\r\n              token: response.access_token\r\n            };\r\n          } else {\r\n            // User doesn't have 2FA enabled, they need to set it up\r\n            console.log('AuthContext: 2FA not enabled, requiring 2FA setup');\r\n            return {\r\n              requiresTwoFactor: true,  // Still requires 2FA (setup)\r\n              userId: response.user.user_id,\r\n              user: userWithAdmin,\r\n              token: response.access_token\r\n            };\r\n          }\r\n        } else {\r\n          // Backend says no 2FA required, proceed with normal login\r\n          console.log('AuthContext: No 2FA required by backend, proceeding with login');\r\n          setUser(userWithAdmin);\r\n          setToken(response.access_token);\r\n\r\n          // Set cookies with appropriate expiration based on rememberMe\r\n          const cookieExpiration = rememberMe ? 30 : 1; // 30 days if remember me, 1 day otherwise\r\n          Cookies.set('auth_token', response.access_token, { expires: cookieExpiration });\r\n          Cookies.set('auth_user', JSON.stringify(userWithAdmin), { expires: cookieExpiration });\r\n          authService.setAuthToken(response.access_token);\r\n\r\n          return {\r\n            requiresTwoFactor: false,\r\n            userId: response.user.user_id,\r\n            user: userWithAdmin,\r\n            token: response.access_token\r\n          };\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('AuthContext: Login failed', error);\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  const completeTwoFactorLogin = async (token: string, userData: User, rememberMe: boolean = false): Promise<void> => {\r\n    try {\r\n      // Ensure roles is an array\r\n      const roles = Array.isArray(userData.roles) ? userData.roles : [];\r\n\r\n      // Add computed isAdmin property for backward compatibility\r\n      const userWithAdmin = {\r\n        ...userData,\r\n        roles,\r\n        isAdmin: roles.includes('administrator'),\r\n        isCustomer: roles.includes('customer'),\r\n      };\r\n\r\n      if (process.env.NODE_ENV === 'development') {\r\n        console.log('AuthContext: 2FA login successful, setting user', userWithAdmin);\r\n      }\r\n      \r\n      setToken(token);\r\n      setUser(userWithAdmin);\r\n\r\n      // Set cookies with appropriate expiration based on rememberMe\r\n      const cookieExpiration = rememberMe ? 30 : 1; // 30 days if remember me, 1 day otherwise\r\n      Cookies.set('auth_token', token, { expires: cookieExpiration });\r\n      Cookies.set('auth_user', JSON.stringify(userWithAdmin), { expires: cookieExpiration });\r\n      authService.setAuthToken(token);\r\n    } catch (error) {\r\n      console.error('AuthContext: 2FA login completion failed', error);\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  const register = async (userData: RegisterData): Promise<void> => {\r\n    await authService.register(userData);\r\n\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log('AuthContext: Registration successful - user should login manually');\r\n    }\r\n\r\n    // Don't automatically log in the user after registration\r\n    // User should be redirected to login page to manually log in\r\n    // This follows the requirement that after account creation,\r\n    // users should be redirected to login page, not dashboard\r\n  };\r\n\r\n  const logout = (): void => {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log('AuthContext: Logging out user');\r\n    }\r\n\r\n    // Clear state\r\n    setUser(null);\r\n    setToken(null);\r\n\r\n    // Remove from cookies\r\n    Cookies.remove('auth_token');\r\n    Cookies.remove('auth_user');\r\n\r\n    // Clear auth service token\r\n    authService.clearAuthToken();\r\n\r\n    // Clear any other localStorage items related to auth\r\n    if (mounted && typeof window !== 'undefined') {\r\n      localStorage.removeItem('auth_token');\r\n      localStorage.removeItem('user_preferences');\r\n      // Clear any cached data\r\n      sessionStorage.clear();\r\n    }\r\n\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log('AuthContext: Logout complete');\r\n    }\r\n  };\r\n\r\n  const updateUser = (updatedUser: UpdateUserData): void => {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log('AuthContext: Updating user', updatedUser);\r\n    }\r\n\r\n    // Convert roles to string array if they're objects\r\n    let roles: string[] = [];\r\n    if (updatedUser.roles) {\r\n      roles = updatedUser.roles.map((role: string | { name?: string; role_name?: string }) =>\r\n        typeof role === 'string' ? role : role.name || role.role_name || 'unknown'\r\n      );\r\n    }\r\n\r\n    // Ensure isAdmin property is set for backward compatibility\r\n    const userWithAdmin = {\r\n      user_id: updatedUser.user_id,\r\n      email: updatedUser.email,\r\n      first_name: updatedUser.first_name,\r\n      last_name: updatedUser.last_name,\r\n      middle_name: updatedUser.middle_name,\r\n      phone: updatedUser.phone,\r\n      status: updatedUser.status,\r\n      profile_image: updatedUser.profile_image, // Include profile image\r\n      roles: roles,\r\n      isAdmin: roles.includes('administrator') || updatedUser.isAdmin || false,\r\n      isCustomer: roles.includes('customer')\r\n    };\r\n\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log('AuthContext: Setting updated user', userWithAdmin);\r\n    }\r\n    setUser(userWithAdmin);\r\n    // Update cookies with new user data\r\n    Cookies.set('auth_user', JSON.stringify(userWithAdmin), { expires: 1 });\r\n  };\r\n\r\n  const value: AuthContextType = {\r\n    user,\r\n    token,\r\n    login,\r\n    completeTwoFactorLogin,\r\n    register,\r\n    logout,\r\n    updateUser,\r\n    loading: loading || !mounted,\r\n    isAuthenticated: mounted && !!user && !!token,\r\n    \r\n  };\r\n\r\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AA6CA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE;IACpE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,8DAA8D;IAC9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO;QAEjC,8EAA8E;QAC9E,MAAM,kBAAkB,CAAA,GAAA,uHAAA,CAAA,4BAAyB,AAAD,EAAE;QAElD,OAAO;YACL,cAAc;QAChB;IACF,GAAG;QAAC;QAAS;QAAM;KAAM;IAEzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;QAId,oDAAoD;QACpD,MAAM,WAAW;YAEf,8BAA8B;YAC9B,MAAM,aAAa,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;YAC/B,MAAM,YAAY,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;YAI9B,IAAI,cAAc,WAAW;gBAC3B,IAAI;oBACF,MAAM,aAAa,KAAK,KAAK,CAAC;oBAC9B,4DAA4D;oBAC5D,MAAM,gBAAgB;wBACpB,GAAG,UAAU;wBACb,SAAS,WAAW,KAAK,EAAE,SAAS,oBAAoB,WAAW,OAAO,IAAI;wBAC9E,YAAY,WAAW,KAAK,EAAE,SAAS;oBACzC;oBAEA,gCAAgC;oBAChC,IAAI,CAAC,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa;wBAC/B,SAAS;wBACT,QAAQ;wBACR,kIAAA,CAAA,cAAW,CAAC,YAAY,CAAC;oBAC3B,OAAO;wBACL,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;wBACf,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;wBACf,kIAAA,CAAA,cAAW,CAAC,cAAc;oBAC5B;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,iDAAiD;oBAC/D,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;oBACf,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;oBACf,kIAAA,CAAA,cAAW,CAAC,cAAc;gBAC5B;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;YAEA,WAAW;QACb;QAEA,+DAA+D;QAC/D;IACF,GAAG;QAAC;KAAQ;IAEZ,MAAM,QAAQ,OAAO,OAAe,UAAkB,aAAsB,KAAK;QAC/E,IAAI;YACF,MAAM,WAAyB,MAAM,kIAAA,CAAA,cAAW,CAAC,KAAK,CAAC;gBAAE;gBAAO;YAAS;YAEzE,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,iBAAiB,SAAS,YAAY,GAAG,YAAY;YACjE,QAAQ,GAAG,CAAC,cAAc,SAAS,IAAI;YACvC,QAAQ,GAAG,CAAC;YAEZ,+CAA+C;YAC/C,IAAI,SAAS,gBAAgB,EAAE;gBAC7B,QAAQ,GAAG,CAAC;gBACZ,OAAO;oBACL,kBAAkB;oBAClB,mBAAmB,SAAS,iBAAiB,IAAI;oBACjD,QAAQ,SAAS,IAAI,CAAC,OAAO;oBAC7B,MAAM,SAAS,IAAI;oBACnB,OAAO,SAAS,YAAY,IAAI;gBAClC;YACF;YAEA,+CAA+C;YAC/C,IAAI,CAAC,YAAY,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,YAAY,EAAE;gBACzD,MAAM,IAAI,MAAM;YAClB;YAEA,2BAA2B;YAC3B,MAAM,QAAQ,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,KAAK,IAAI,SAAS,IAAI,CAAC,KAAK,GAAG,EAAE;YAE3E,2DAA2D;YAC3D,MAAM,gBAAgB;gBACpB,GAAG,SAAS,IAAI;gBAChB;gBACA,SAAS,MAAM,QAAQ,CAAC;gBACxB,YAAY,MAAM,QAAQ,CAAC;YAC7B;YAEA,wEAAwE;YACxE,IAAI,SAAS,IAAI,EAAE;gBACjB,0FAA0F;gBAC1F,MAAM,qBAAqB,SAAS,iBAAiB,KAAK,YAAY,SAAS,iBAAiB,GAAG,SAAS,IAAI,CAAC,kBAAkB;gBAEnI,IAAI,oBAAoB;oBACtB,IAAI,SAAS,IAAI,CAAC,kBAAkB,EAAE;wBACpC,yDAAyD;wBACzD,QAAQ,GAAG,CAAC;wBACZ,OAAO;4BACL,mBAAmB;4BACnB,QAAQ,SAAS,IAAI,CAAC,OAAO;4BAC7B,MAAM;4BACN,OAAO,SAAS,YAAY;wBAC9B;oBACF,OAAO;wBACL,wDAAwD;wBACxD,QAAQ,GAAG,CAAC;wBACZ,OAAO;4BACL,mBAAmB;4BACnB,QAAQ,SAAS,IAAI,CAAC,OAAO;4BAC7B,MAAM;4BACN,OAAO,SAAS,YAAY;wBAC9B;oBACF;gBACF,OAAO;oBACL,0DAA0D;oBAC1D,QAAQ,GAAG,CAAC;oBACZ,QAAQ;oBACR,SAAS,SAAS,YAAY;oBAE9B,8DAA8D;oBAC9D,MAAM,mBAAmB,aAAa,KAAK,GAAG,0CAA0C;oBACxF,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,cAAc,SAAS,YAAY,EAAE;wBAAE,SAAS;oBAAiB;oBAC7E,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,aAAa,KAAK,SAAS,CAAC,gBAAgB;wBAAE,SAAS;oBAAiB;oBACpF,kIAAA,CAAA,cAAW,CAAC,YAAY,CAAC,SAAS,YAAY;oBAE9C,OAAO;wBACL,mBAAmB;wBACnB,QAAQ,SAAS,IAAI,CAAC,OAAO;wBAC7B,MAAM;wBACN,OAAO,SAAS,YAAY;oBAC9B;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,MAAM,yBAAyB,OAAO,OAAe,UAAgB,aAAsB,KAAK;QAC9F,IAAI;YACF,2BAA2B;YAC3B,MAAM,QAAQ,MAAM,OAAO,CAAC,SAAS,KAAK,IAAI,SAAS,KAAK,GAAG,EAAE;YAEjE,2DAA2D;YAC3D,MAAM,gBAAgB;gBACpB,GAAG,QAAQ;gBACX;gBACA,SAAS,MAAM,QAAQ,CAAC;gBACxB,YAAY,MAAM,QAAQ,CAAC;YAC7B;YAEA,wCAA4C;gBAC1C,QAAQ,GAAG,CAAC,mDAAmD;YACjE;YAEA,SAAS;YACT,QAAQ;YAER,8DAA8D;YAC9D,MAAM,mBAAmB,aAAa,KAAK,GAAG,0CAA0C;YACxF,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,cAAc,OAAO;gBAAE,SAAS;YAAiB;YAC7D,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,aAAa,KAAK,SAAS,CAAC,gBAAgB;gBAAE,SAAS;YAAiB;YACpF,kIAAA,CAAA,cAAW,CAAC,YAAY,CAAC;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,MAAM;QACR;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,MAAM,kIAAA,CAAA,cAAW,CAAC,QAAQ,CAAC;QAE3B,wCAA4C;YAC1C,QAAQ,GAAG,CAAC;QACd;IAEA,yDAAyD;IACzD,6DAA6D;IAC7D,4DAA4D;IAC5D,0DAA0D;IAC5D;IAEA,MAAM,SAAS;QACb,wCAA4C;YAC1C,QAAQ,GAAG,CAAC;QACd;QAEA,cAAc;QACd,QAAQ;QACR,SAAS;QAET,sBAAsB;QACtB,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QAEf,2BAA2B;QAC3B,kIAAA,CAAA,cAAW,CAAC,cAAc;QAE1B,qDAAqD;QACrD,uCAA8C;;QAK9C;QAEA,wCAA4C;YAC1C,QAAQ,GAAG,CAAC;QACd;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,wCAA4C;YAC1C,QAAQ,GAAG,CAAC,8BAA8B;QAC5C;QAEA,mDAAmD;QACnD,IAAI,QAAkB,EAAE;QACxB,IAAI,YAAY,KAAK,EAAE;YACrB,QAAQ,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,OAC7B,OAAO,SAAS,WAAW,OAAO,KAAK,IAAI,IAAI,KAAK,SAAS,IAAI;QAErE;QAEA,4DAA4D;QAC5D,MAAM,gBAAgB;YACpB,SAAS,YAAY,OAAO;YAC5B,OAAO,YAAY,KAAK;YACxB,YAAY,YAAY,UAAU;YAClC,WAAW,YAAY,SAAS;YAChC,aAAa,YAAY,WAAW;YACpC,OAAO,YAAY,KAAK;YACxB,QAAQ,YAAY,MAAM;YAC1B,eAAe,YAAY,aAAa;YACxC,OAAO;YACP,SAAS,MAAM,QAAQ,CAAC,oBAAoB,YAAY,OAAO,IAAI;YACnE,YAAY,MAAM,QAAQ,CAAC;QAC7B;QAEA,wCAA4C;YAC1C,QAAQ,GAAG,CAAC,qCAAqC;QACnD;QACA,QAAQ;QACR,oCAAoC;QACpC,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,aAAa,KAAK,SAAS,CAAC,gBAAgB;YAAE,SAAS;QAAE;IACvE;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;QACA,SAAS,WAAW,CAAC;QACrB,iBAAiB,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAC;IAE1C;IAEA,qBAAO,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C", "debugId": null}}, {"offset": {"line": 860, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/Loader.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport Image from 'next/image'\r\n\r\nconst Loader = ({ message = 'Loading...' }) => {\r\n  return (\r\n    <div className=\"text-center\">\r\n    <div className=\"relative w-20 h-20 mx-auto\">\r\n        {/* SVG Spinner with tapered stroke ends */}\r\n        <svg\r\n        className=\"absolute inset-0 animate-spin\"\r\n        viewBox=\"0 0 50 50\"\r\n        fill=\"none\"\r\n        >\r\n        <defs>\r\n            <linearGradient id=\"fadeGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\r\n            <stop offset=\"0%\" stopColor=\"#dc2626\" stopOpacity=\"0\" />\r\n            <stop offset=\"20%\" stopColor=\"#dc2626\" stopOpacity=\"1\" />\r\n            <stop offset=\"80%\" stopColor=\"#dc2626\" stopOpacity=\"1\" />\r\n            <stop offset=\"100%\" stopColor=\"#dc2626\" stopOpacity=\"0\" />\r\n            </linearGradient>\r\n        </defs>\r\n\r\n        <circle\r\n            cx=\"25\"\r\n            cy=\"25\"\r\n            r=\"20\"\r\n            stroke=\"rgba(255, 255, 255, 0.0)\"\r\n            strokeWidth=\"2\"\r\n        />\r\n        <circle\r\n            cx=\"25\"\r\n            cy=\"25\"\r\n            r=\"20\"\r\n            stroke=\"url(#fadeGradient)\"\r\n            strokeWidth=\"1\"\r\n            strokeDasharray=\"70\"\r\n            strokeDashoffset=\"10\"\r\n            fill=\"none\"\r\n        />\r\n        </svg>\r\n\r\n        {/* MACRA Logo */}\r\n        <Image\r\n        src=\"/images/macra-logo.png\"\r\n        alt=\"MACRA Logo\"\r\n        width={40}\r\n        height={40}\r\n        className=\"object-contain absolute inset-0 h-10 w-10 m-auto animate-fadeLoop\"\r\n        />\r\n    </div>\r\n\r\n    {/* Dynamic message */}\r\n    <p className=\"mt-4 text-gray-600\">{message}</p>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default Loader\r\n\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,SAAS,CAAC,EAAE,UAAU,YAAY,EAAE;IACxC,qBACE,8OAAC;QAAI,WAAU;;0BACf,8OAAC;gBAAI,WAAU;;kCAEX,8OAAC;wBACD,WAAU;wBACV,SAAQ;wBACR,MAAK;;0CAEL,8OAAC;0CACG,cAAA,8OAAC;oCAAe,IAAG;oCAAe,IAAG;oCAAK,IAAG;oCAAK,IAAG;oCAAO,IAAG;;sDAC/D,8OAAC;4CAAK,QAAO;4CAAK,WAAU;4CAAU,aAAY;;;;;;sDAClD,8OAAC;4CAAK,QAAO;4CAAM,WAAU;4CAAU,aAAY;;;;;;sDACnD,8OAAC;4CAAK,QAAO;4CAAM,WAAU;4CAAU,aAAY;;;;;;sDACnD,8OAAC;4CAAK,QAAO;4CAAO,WAAU;4CAAU,aAAY;;;;;;;;;;;;;;;;;0CAIxD,8OAAC;gCACG,IAAG;gCACH,IAAG;gCACH,GAAE;gCACF,QAAO;gCACP,aAAY;;;;;;0CAEhB,8OAAC;gCACG,IAAG;gCACH,IAAG;gCACH,GAAE;gCACF,QAAO;gCACP,aAAY;gCACZ,iBAAgB;gCAChB,kBAAiB;gCACjB,MAAK;;;;;;;;;;;;kCAKT,8OAAC,6HAAA,CAAA,UAAK;wBACN,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;;;;;;;0BAKd,8OAAC;gBAAE,WAAU;0BAAsB;;;;;;;;;;;;AAGvC;uCAEe", "debugId": null}}, {"offset": {"line": 1005, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/contexts/LoadingContext.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { createContext, useContext, useState, useEffect } from 'react';\r\nimport { usePathname } from 'next/navigation';\r\nimport Loader from '@/components/Loader';\r\n\r\ninterface LoadingContextType {\r\n  isLoading: boolean;\r\n  setLoading: (loading: boolean) => void;\r\n  showLoader: (message?: string) => void;\r\n  hideLoader: () => void;\r\n}\r\n\r\nconst LoadingContext = createContext<LoadingContextType | undefined>(undefined);\r\n\r\nexport const useLoading = () => {\r\n  const context = useContext(LoadingContext);\r\n  if (!context) {\r\n    throw new Error('useLoading must be used within a LoadingProvider');\r\n  }\r\n  return context;\r\n};\r\n\r\ninterface LoadingProviderProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\nexport const LoadingProvider: React.FC<LoadingProviderProps> = ({ children }) => {\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [loadingMessage, setLoadingMessage] = useState('Loading...');\r\n  const pathname = usePathname();\r\n\r\n  // Handle route changes\r\n  useEffect(() => {\r\n    const handleStart = () => {\r\n      setIsLoading(true);\r\n      setLoadingMessage('Loading page...');\r\n    };\r\n\r\n    const handleComplete = () => {\r\n      // Add a small delay to ensure smooth transition\r\n      setTimeout(() => {\r\n        setIsLoading(false);\r\n      }, 300);\r\n    };\r\n\r\n    // Listen for route changes\r\n    handleStart();\r\n    handleComplete();\r\n  }, [pathname]);\r\n\r\n  const setLoading = (loading: boolean) => {\r\n    setIsLoading(loading);\r\n  };\r\n\r\n  const showLoader = (message: string = 'Loading...') => {\r\n    setLoadingMessage(message);\r\n    setIsLoading(true);\r\n  };\r\n\r\n  const hideLoader = () => {\r\n    setIsLoading(false);\r\n  };\r\n\r\n  return (\r\n    <LoadingContext.Provider value={{ isLoading, setLoading, showLoader, hideLoader }}>\r\n      {children}\r\n      {isLoading && (\r\n        <div className=\"fixed inset-0 bg-white dark:bg-gray-900 bg-opacity-80 dark:bg-opacity-80 backdrop-blur-sm z-50 flex items-center justify-center\">\r\n          <Loader message={loadingMessage} />\r\n        </div>\r\n      )}\r\n    </LoadingContext.Provider>\r\n  );\r\n};"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAaA,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAkC;AAE9D,MAAM,aAAa;IACxB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,MAAM,kBAAkD,CAAC,EAAE,QAAQ,EAAE;IAC1E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;YAClB,aAAa;YACb,kBAAkB;QACpB;QAEA,MAAM,iBAAiB;YACrB,gDAAgD;YAChD,WAAW;gBACT,aAAa;YACf,GAAG;QACL;QAEA,2BAA2B;QAC3B;QACA;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,aAAa,CAAC;QAClB,aAAa;IACf;IAEA,MAAM,aAAa,CAAC,UAAkB,YAAY;QAChD,kBAAkB;QAClB,aAAa;IACf;IAEA,MAAM,aAAa;QACjB,aAAa;IACf;IAEA,qBACE,8OAAC,eAAe,QAAQ;QAAC,OAAO;YAAE;YAAW;YAAY;YAAY;QAAW;;YAC7E;YACA,2BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,4HAAA,CAAA,UAAM;oBAAC,SAAS;;;;;;;;;;;;;;;;;AAK3B", "debugId": null}}, {"offset": {"line": 1094, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/lib/ThemeContext.js"], "sourcesContent": ["'use client';\r\n\r\nimport { createContext, useContext, useState, useEffect } from 'react';\r\n\r\nconst ThemeContext = createContext();\r\n\r\nexport function ThemeProvider({ children }) {\r\n  const [theme, setTheme] = useState('system');\r\n  const [resolvedTheme, setResolvedTheme] = useState('light');\r\n  const [mounted, setMounted] = useState(false);\r\n\r\n  // Set mounted to true after hydration\r\n  useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  // Initialize theme from localStorage or system preference\r\n  useEffect(() => {\r\n    if (!mounted) return;\r\n\r\n    const savedTheme = localStorage.getItem('theme');\r\n    if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {\r\n      setTheme(savedTheme);\r\n    } else {\r\n      setTheme('system');\r\n    }\r\n  }, [mounted]);\r\n\r\n  // Update resolved theme based on current theme setting\r\n  useEffect(() => {\r\n    if (!mounted) return;\r\n\r\n    const updateResolvedTheme = () => {\r\n      if (theme === 'system') {\r\n        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\r\n        setResolvedTheme(systemTheme);\r\n      } else {\r\n        setResolvedTheme(theme);\r\n      }\r\n    };\r\n\r\n    updateResolvedTheme();\r\n\r\n    // Listen for system theme changes when using system theme\r\n    if (theme === 'system') {\r\n      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\r\n      mediaQuery.addEventListener('change', updateResolvedTheme);\r\n      return () => mediaQuery.removeEventListener('change', updateResolvedTheme);\r\n    }\r\n  }, [theme, mounted]);\r\n\r\n  // Apply theme to document\r\n  useEffect(() => {\r\n    if (!mounted) return;\r\n\r\n    if (resolvedTheme === 'dark') {\r\n      document.documentElement.classList.add('dark');\r\n    } else {\r\n      document.documentElement.classList.remove('dark');\r\n    }\r\n  }, [resolvedTheme, mounted]);\r\n\r\n  const setThemePreference = (newTheme) => {\r\n    setTheme(newTheme);\r\n    if (mounted) {\r\n      localStorage.setItem('theme', newTheme);\r\n    }\r\n  };\r\n\r\n  const toggleTheme = () => {\r\n    const newTheme = resolvedTheme === 'light' ? 'dark' : 'light';\r\n    setThemePreference(newTheme);\r\n  };\r\n\r\n  // Prevent hydration mismatch by not rendering until mounted\r\n  if (!mounted) {\r\n    return (\r\n      <ThemeContext.Provider value={{\r\n        theme: 'light',\r\n        resolvedTheme: 'light',\r\n        setTheme: () => {},\r\n        toggleTheme: () => {}\r\n      }}>\r\n        {children}\r\n      </ThemeContext.Provider>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <ThemeContext.Provider value={{\r\n      theme,\r\n      resolvedTheme,\r\n      setTheme: setThemePreference,\r\n      toggleTheme\r\n    }}>\r\n      {children}\r\n    </ThemeContext.Provider>\r\n  );\r\n}\r\n\r\nexport function useTheme() {\r\n  const context = useContext(ThemeContext);\r\n  if (!context) {\r\n    throw new Error('useTheme must be used within a ThemeProvider');\r\n  }\r\n  return context;\r\n}"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAIA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD;AAE1B,SAAS,cAAc,EAAE,QAAQ,EAAE;IACxC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,0DAA0D;IAC1D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;QAEd,MAAM,aAAa,aAAa,OAAO,CAAC;QACxC,IAAI,cAAc;YAAC;YAAS;YAAQ;SAAS,CAAC,QAAQ,CAAC,aAAa;YAClE,SAAS;QACX,OAAO;YACL,SAAS;QACX;IACF,GAAG;QAAC;KAAQ;IAEZ,uDAAuD;IACvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;QAEd,MAAM,sBAAsB;YAC1B,IAAI,UAAU,UAAU;gBACtB,MAAM,cAAc,OAAO,UAAU,CAAC,gCAAgC,OAAO,GAAG,SAAS;gBACzF,iBAAiB;YACnB,OAAO;gBACL,iBAAiB;YACnB;QACF;QAEA;QAEA,0DAA0D;QAC1D,IAAI,UAAU,UAAU;YACtB,MAAM,aAAa,OAAO,UAAU,CAAC;YACrC,WAAW,gBAAgB,CAAC,UAAU;YACtC,OAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;QACxD;IACF,GAAG;QAAC;QAAO;KAAQ;IAEnB,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;QAEd,IAAI,kBAAkB,QAAQ;YAC5B,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;QACzC,OAAO;YACL,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;QAC5C;IACF,GAAG;QAAC;QAAe;KAAQ;IAE3B,MAAM,qBAAqB,CAAC;QAC1B,SAAS;QACT,IAAI,SAAS;YACX,aAAa,OAAO,CAAC,SAAS;QAChC;IACF;IAEA,MAAM,cAAc;QAClB,MAAM,WAAW,kBAAkB,UAAU,SAAS;QACtD,mBAAmB;IACrB;IAEA,4DAA4D;IAC5D,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC,aAAa,QAAQ;YAAC,OAAO;gBAC5B,OAAO;gBACP,eAAe;gBACf,UAAU,KAAO;gBACjB,aAAa,KAAO;YACtB;sBACG;;;;;;IAGP;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;YAC5B;YACA;YACA,UAAU;YACV;QACF;kBACG;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1215, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/ui/Toast.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useState } from 'react';\r\n\r\nexport interface ToastProps {\r\n  message: string;\r\n  type: 'success' | 'error' | 'warning' | 'info';\r\n  duration?: number;\r\n  onClose: () => void;\r\n}\r\n\r\nconst Toast: React.FC<ToastProps> = ({ message, type, duration = 5000, onClose }) => {\r\n  const [isVisible, setIsVisible] = useState(true);\r\n\r\n  useEffect(() => {\r\n    const timer = setTimeout(() => {\r\n      setIsVisible(false);\r\n      setTimeout(onClose, 300); // Wait for fade out animation\r\n    }, duration);\r\n\r\n    return () => clearTimeout(timer);\r\n  }, [duration, onClose]);\r\n\r\n  const getToastStyles = () => {\r\n    switch (type) {\r\n      case 'success':\r\n        return 'bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-300';\r\n      case 'error':\r\n        return 'bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-300';\r\n      case 'warning':\r\n        return 'bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-yellow-300';\r\n      case 'info':\r\n        return 'bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-300';\r\n      default:\r\n        return 'bg-gray-50 border-gray-200 text-gray-800 dark:bg-gray-900/20 dark:border-gray-800 dark:text-gray-300';\r\n    }\r\n  };\r\n\r\n  const getIcon = () => {\r\n    switch (type) {\r\n      case 'success':\r\n        return 'ri-check-circle-line';\r\n      case 'error':\r\n        return 'ri-error-warning-line';\r\n      case 'warning':\r\n        return 'ri-alert-line';\r\n      case 'info':\r\n        return 'ri-information-line';\r\n      default:\r\n        return 'ri-information-line';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={`relative max-w-sm w-full border rounded-lg p-4 shadow-lg transition-all duration-300 ${\r\n        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-2'\r\n      } ${getToastStyles()}`}\r\n    >\r\n      <div className=\"flex items-start\">\r\n        <div className=\"flex-shrink-0\">\r\n          <i className={`${getIcon()} text-lg`}></i>\r\n        </div>\r\n        <div className=\"ml-3 flex-1\">\r\n          <p className=\"text-sm font-medium\">{message}</p>\r\n        </div>\r\n        <div className=\"ml-4 flex-shrink-0\">\r\n          <button\r\n            type=\"button\"\r\n            onClick={() => {\r\n              setIsVisible(false);\r\n              setTimeout(onClose, 300);\r\n            }}\r\n            className=\"inline-flex rounded-md p-1.5 hover:bg-black/5 dark:hover:bg-white/5 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current\"\r\n          >\r\n            <span className=\"sr-only\">Dismiss</span>\r\n            <i className=\"ri-close-line text-sm\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Toast;\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAWA,MAAM,QAA8B,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,IAAI,EAAE,OAAO,EAAE;IAC9E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,WAAW;YACvB,aAAa;YACb,WAAW,SAAS,MAAM,8BAA8B;QAC1D,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAU;KAAQ;IAEtB,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,UAAU;QACd,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QACC,WAAW,CAAC,qFAAqF,EAC/F,YAAY,8BAA8B,2BAC3C,CAAC,EAAE,kBAAkB;kBAEtB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAW,GAAG,UAAU,QAAQ,CAAC;;;;;;;;;;;8BAEtC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAuB;;;;;;;;;;;8BAEtC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,MAAK;wBACL,SAAS;4BACP,aAAa;4BACb,WAAW,SAAS;wBACtB;wBACA,WAAU;;0CAEV,8OAAC;gCAAK,WAAU;0CAAU;;;;;;0CAC1B,8OAAC;gCAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzB;uCAEe", "debugId": null}}, {"offset": {"line": 1352, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/contexts/ToastContext.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { createContext, useContext, useState, ReactNode } from 'react';\r\nimport Toast, { ToastProps } from '@/components/ui/Toast';\r\nimport '@/styles/toast.css';\r\n\r\ninterface ToastContextType {\r\n  showToast: (message: string, type: ToastProps['type'], duration?: number) => void;\r\n  showSuccess: (message: string, duration?: number) => void;\r\n  showError: (message: string, duration?: number) => void;\r\n  showWarning: (message: string, duration?: number) => void;\r\n  showInfo: (message: string, duration?: number) => void;\r\n}\r\n\r\nconst ToastContext = createContext<ToastContextType | undefined>(undefined);\r\n\r\ninterface ToastItem {\r\n  id: string;\r\n  message: string;\r\n  type: ToastProps['type'];\r\n  duration?: number;\r\n}\r\n\r\ninterface ToastProviderProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport const ToastProvider: React.FC<ToastProviderProps> = ({ children }) => {\r\n  const [toasts, setToasts] = useState<ToastItem[]>([]);\r\n\r\n  const showToast = (message: string, type: ToastProps['type'], duration?: number) => {\r\n    const id = Math.random().toString(36).substr(2, 9);\r\n    const newToast: ToastItem = { id, message, type, duration };\r\n    \r\n    setToasts(prev => [...prev, newToast]);\r\n  };\r\n\r\n  const removeToast = (id: string) => {\r\n    setToasts(prev => prev.filter(toast => toast.id !== id));\r\n  };\r\n\r\n  const showSuccess = (message: string, duration?: number) => {\r\n    showToast(message, 'success', duration);\r\n  };\r\n\r\n  const showError = (message: string, duration?: number) => {\r\n    showToast(message, 'error', duration);\r\n  };\r\n\r\n  const showWarning = (message: string, duration?: number) => {\r\n    showToast(message, 'warning', duration);\r\n  };\r\n\r\n  const showInfo = (message: string, duration?: number) => {\r\n    showToast(message, 'info', duration);\r\n  };\r\n\r\n  return (\r\n    <ToastContext.Provider value={{\r\n      showToast,\r\n      showSuccess,\r\n      showError,\r\n      showWarning,\r\n      showInfo\r\n    }}>\r\n      {children}\r\n      \r\n      {/* Render toasts */}\r\n      <div className=\"toast-container\">\r\n        {toasts.map((toast, index) => (\r\n          <div\r\n            key={toast.id}\r\n            className=\"toast-wrapper\"\r\n            data-index={index}\r\n            data-toast-index={index}\r\n          >\r\n            <Toast\r\n              message={toast.message}\r\n              type={toast.type}\r\n              duration={toast.duration}\r\n              onClose={() => removeToast(toast.id)}\r\n            />\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </ToastContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useToast = (): ToastContextType => {\r\n  const context = useContext(ToastContext);\r\n  if (!context) {\r\n    throw new Error('useToast must be used within a ToastProvider');\r\n  }\r\n  return context;\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;;AAcA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAa1D,MAAM,gBAA8C,CAAC,EAAE,QAAQ,EAAE;IACtE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAEpD,MAAM,YAAY,CAAC,SAAiB,MAA0B;QAC5D,MAAM,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QAChD,MAAM,WAAsB;YAAE;YAAI;YAAS;YAAM;QAAS;QAE1D,UAAU,CAAA,OAAQ;mBAAI;gBAAM;aAAS;IACvC;IAEA,MAAM,cAAc,CAAC;QACnB,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACtD;IAEA,MAAM,cAAc,CAAC,SAAiB;QACpC,UAAU,SAAS,WAAW;IAChC;IAEA,MAAM,YAAY,CAAC,SAAiB;QAClC,UAAU,SAAS,SAAS;IAC9B;IAEA,MAAM,cAAc,CAAC,SAAiB;QACpC,UAAU,SAAS,WAAW;IAChC;IAEA,MAAM,WAAW,CAAC,SAAiB;QACjC,UAAU,SAAS,QAAQ;IAC7B;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;YAC5B;YACA;YACA;YACA;YACA;QACF;;YACG;0BAGD,8OAAC;gBAAI,WAAU;0BACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;wBAEC,WAAU;wBACV,cAAY;wBACZ,oBAAkB;kCAElB,cAAA,8OAAC,iIAAA,CAAA,UAAK;4BACJ,SAAS,MAAM,OAAO;4BACtB,MAAM,MAAM,IAAI;4BAChB,UAAU,MAAM,QAAQ;4BACxB,SAAS,IAAM,YAAY,MAAM,EAAE;;;;;;uBAThC,MAAM,EAAE;;;;;;;;;;;;;;;;AAgBzB;AAEO,MAAM,WAAW;IACtB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1451, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/NoSSR.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ReactNode, useEffect, useState } from 'react';\r\n\r\ninterface NoSSRProps {\r\n  children: ReactNode;\r\n  fallback?: ReactNode;\r\n}\r\n\r\nexport default function NoSSR({ children, fallback = null }: NoSSRProps) {\r\n  const [mounted, setMounted] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  if (!mounted) {\r\n    return <>{fallback}</>;\r\n  }\r\n\r\n  return <>{children}</>;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASe,SAAS,MAAM,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAc;IACrE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBAAO;sBAAG;;IACZ;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 1479, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/ClientWrapper.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ReactNode } from 'react';\r\nimport { AuthProvider } from '../contexts/AuthContext';\r\nimport { LoadingProvider } from '../contexts/LoadingContext';\r\nimport { ThemeProvider } from '../lib/ThemeContext';\r\nimport { ToastProvider } from '../contexts/ToastContext';\r\nimport Loader from './Loader';\r\nimport NoSSR from './NoSSR';\r\n\r\ninterface ClientWrapperProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport default function ClientWrapper({ children }: ClientWrapperProps) {\r\n  return (\r\n    <NoSSR fallback={\r\n      <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\">\r\n        <Loader message=\"Initializing application...\" />\r\n      </div>\r\n    }>\r\n      <ThemeProvider>\r\n        <LoadingProvider>\r\n          <ToastProvider>\r\n            <AuthProvider>\r\n              {children}\r\n            </AuthProvider>\r\n          </ToastProvider>\r\n        </LoadingProvider>\r\n      </ThemeProvider>\r\n    </NoSSR>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;AAce,SAAS,cAAc,EAAE,QAAQ,EAAsB;IACpE,qBACE,8OAAC,2HAAA,CAAA,UAAK;QAAC,wBACL,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,4HAAA,CAAA,UAAM;gBAAC,SAAQ;;;;;;;;;;;kBAGlB,cAAA,8OAAC,0HAAA,CAAA,gBAAa;sBACZ,cAAA,8OAAC,kIAAA,CAAA,kBAAe;0BACd,cAAA,8OAAC,gIAAA,CAAA,gBAAa;8BACZ,cAAA,8OAAC,+HAAA,CAAA,eAAY;kCACV;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 1550, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/LoadingOptimizer.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect } from 'react';\r\n\r\ninterface LoadingOptimizerProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\nconst LoadingOptimizer: React.FC<LoadingOptimizerProps> = ({ children }) => {\r\n  useEffect(() => {\r\n    // Optimize images with lazy loading\r\n    const optimizeImages = () => {\r\n      const images = document.querySelectorAll('img[data-src]');\r\n      const imageObserver = new IntersectionObserver((entries) => {\r\n        entries.forEach((entry) => {\r\n          if (entry.isIntersecting) {\r\n            const img = entry.target as HTMLImageElement;\r\n            img.src = img.dataset.src || '';\r\n            img.classList.remove('lazy');\r\n            imageObserver.unobserve(img);\r\n          }\r\n        });\r\n      });\r\n\r\n      images.forEach((img) => imageObserver.observe(img));\r\n    };\r\n\r\n    // Optimize page performance\r\n    const optimizePerformance = () => {\r\n      // Add will-change to elements that will animate\r\n      const animatedElements = document.querySelectorAll('.nav-item, .dashboard-card, .btn-primary');\r\n      animatedElements.forEach((el) => {\r\n        (el as HTMLElement).style.willChange = 'transform, background-color';\r\n      });\r\n\r\n      // Optimize scroll performance\r\n      document.documentElement.style.scrollBehavior = 'smooth';\r\n    };\r\n\r\n    // Run optimizations after DOM is ready\r\n    const timer = setTimeout(() => {\r\n      optimizeImages();\r\n      optimizePerformance();\r\n    }, 100);\r\n\r\n    return () => {\r\n      clearTimeout(timer);\r\n      // Cleanup will-change properties\r\n      const animatedElements = document.querySelectorAll('.nav-item, .dashboard-card, .btn-primary');\r\n      animatedElements.forEach((el) => {\r\n        (el as HTMLElement).style.willChange = 'auto';\r\n      });\r\n    };\r\n  }, []);\r\n\r\n  return <>{children}</>;\r\n};\r\n\r\nexport default LoadingOptimizer;"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAQA,MAAM,mBAAoD,CAAC,EAAE,QAAQ,EAAE;IACrE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oCAAoC;QACpC,MAAM,iBAAiB;YACrB,MAAM,SAAS,SAAS,gBAAgB,CAAC;YACzC,MAAM,gBAAgB,IAAI,qBAAqB,CAAC;gBAC9C,QAAQ,OAAO,CAAC,CAAC;oBACf,IAAI,MAAM,cAAc,EAAE;wBACxB,MAAM,MAAM,MAAM,MAAM;wBACxB,IAAI,GAAG,GAAG,IAAI,OAAO,CAAC,GAAG,IAAI;wBAC7B,IAAI,SAAS,CAAC,MAAM,CAAC;wBACrB,cAAc,SAAS,CAAC;oBAC1B;gBACF;YACF;YAEA,OAAO,OAAO,CAAC,CAAC,MAAQ,cAAc,OAAO,CAAC;QAChD;QAEA,4BAA4B;QAC5B,MAAM,sBAAsB;YAC1B,gDAAgD;YAChD,MAAM,mBAAmB,SAAS,gBAAgB,CAAC;YACnD,iBAAiB,OAAO,CAAC,CAAC;gBACvB,GAAmB,KAAK,CAAC,UAAU,GAAG;YACzC;YAEA,8BAA8B;YAC9B,SAAS,eAAe,CAAC,KAAK,CAAC,cAAc,GAAG;QAClD;QAEA,uCAAuC;QACvC,MAAM,QAAQ,WAAW;YACvB;YACA;QACF,GAAG;QAEH,OAAO;YACL,aAAa;YACb,iCAAiC;YACjC,MAAM,mBAAmB,SAAS,gBAAgB,CAAC;YACnD,iBAAiB,OAAO,CAAC,CAAC;gBACvB,GAAmB,KAAK,CAAC,UAAU,GAAG;YACzC;QACF;IACF,GAAG,EAAE;IAEL,qBAAO;kBAAG;;AACZ;uCAEe", "debugId": null}}]}